// page {
//     background: none;
//     background-color: none;
//     background:  rgba(245, 246, 250, 1);
// }
.container {
    width: 100%;
    padding-top: 176px;
    background:  rgba(245, 246, 250, 1);
}

.header {
    width: 100%;
    height: 176px;
    background: linear-gradient(180deg, rgba(224, 243, 255, 1) 0%, rgba(245, 246, 250, 1) 100%);
    position: fixed;
    top: 0;
    z-index: 10;
}

.columns {
    display: flex;
    flex-direction: column;
    box-sizing: border-box;
    align-items: center;
    padding-top: 16px;
    flex: 1;
}

.safety {
    width: 100%;
    height: calc(content(safe-area-inset-bottom) + 168px);
    height: calc(env(safe-area-inset-bottom) + 168px);
}

.safetyNoFooter {
    width: 100%;
    height: calc(content(safe-area-inset-bottom) + 48px) !important;
    height: calc(env(safe-area-inset-bottom) + 48px) !important;
}

.footerBar {
    position: fixed;
    bottom: 0;
    width: 100%;
    left: 0;
    background-color: #fff;
    box-sizing: border-box;
    padding: 24px 32px;
    z-index: 190;
    padding-bottom: calc(content(safe-area-inset-bottom) + 24px);
    padding-bottom: calc(env(safe-area-inset-bottom) + 24px);
}

.footerBtn {
    background-color: rgb(255, 137, 4);
    color: #fff;
    height: 96px;
    border-radius: 48px;
    z-index: 191;
    display: flex;
    justify-content: center;
    align-items: center;
    line-height: 48px;
    font-weight: bold;
    font-size: 34px;
}

.applied {
    background: rgb(245, 247, 252);
    color: rgba(0, 0, 0, 0.65);
}


.hidden {
    display: none !important;
}

.fabuIcon{
    width: 36px;
    height: 36px;
    margin-right: 12px;
    display: flex;
    flex-direction: row;
    align-items: center;
}

.backIcon {
    width: 48px;
    height: 48px;
    position: fixed;
    left: 32px;
}

.hidden {
    display: none;
}



