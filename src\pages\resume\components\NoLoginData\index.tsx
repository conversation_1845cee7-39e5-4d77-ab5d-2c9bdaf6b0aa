import { Image } from '@tarojs/components'
import style from './index.module.scss'
import { useLogin } from '@/core/utils/login'
import LoginBtn from '@/components/LoginBtn'

export default () => {
  const [loginProps] = useLogin({})

  return (
    <V className={style.body}>
      <Image className={style.img} src="https://cdn.yupaowang.com/yupao_common/3bdca70b.png" />
      <V className={style.title}><T>登录后才可以查看简历信息</T></V>
      <V className={style.btn} >
        <T>注册/登录查看简历信息</T>
        <LoginBtn {...loginProps} style={{ opacity: 0, position: 'absolute', left: 0, top: 0, right: 0, bottom: 0 }} />
      </V>
    </V>
  )
}
