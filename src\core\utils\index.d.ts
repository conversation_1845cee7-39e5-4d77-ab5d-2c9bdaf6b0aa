export type ITypeOf = 'string' | 'number' | 'boolean' | 'undefined' | 'null' |
  'date' | 'regexp' | 'promise' | 'object' | 'array' | 'function' |
  'map' | 'set' | 'weakmap' | 'weakset' | 'weakRef' | 'window' | 'global' |
  'symbol' | 'bigint' | 'generator' | 'iterator' | 'int8Array' | 'uint8Array' |
  'uint8ClampedArray' | 'int16Array' | 'uint16Array' | 'int32Array' |
  'uint32Array' | 'float32Array' | 'float64Array' | 'arrayBuffer' |
  'bigInt64Array' | 'bigUint64Array' | 'dataView' | 'sharedArrayBuffer' |
  'math' | 'stringObject' | 'numberObject' | 'booleanObject' |
  'symbolObject' | 'error' | 'evalError' | 'atomics' |
  'rangeError' | 'referenceError' | 'syntaxError' | 'typeError' | 'uriError' |
  'arguments' | 'arrayIterator' | 'asyncGenerator' | 'asyncGeneratorFunction' |
  'boundFunction' | 'console' | 'finalizationRegistry' | 'intl.Collator' |
  'intl.DateTimeFormat' | 'intl.ListFormat' | 'intl.NumberFormat' | 'intl.PluralRules' |
  'intl.RelativeTimeFormat' | 'intl.Locale' | 'intl.DisplayNames'

type SourceMap = typeof import('./classify').sourceMap

/**
 * 职位选择器参数
 * @interface IClassifyDataRq
 * @property {Array<any>} value - 默认选中的值,格式:[id:末级职位,industries:[行业ID(可为空,默认处理成第一个有职位的行业)]]
 * @property {number} maxSelectNum - 最多可选的数量
 * @property {string} title - 标题
 * @property {boolean} isZpSingle - 招聘类是否单选,并且订单类和招聘互斥
 * @property {boolean} isHideZpClassify - 搜索内容是否过滤招聘类职位
 * @property {boolean} isSelectToBack - 最大可选数量为1是，直接返回选中值 true直接返回选中值
 */
export interface IClassifyDataRq {
  value?: Array<any>
  maxSelectNum?: number
  title?: string
  isZpSingle?: boolean
  isHideZpClassify?: boolean
  isSelectToBack?: boolean
  maxSelectToastTxt?: string
  isClear?: boolean,
  /**
   * "1:订阅好活,2:找活列表,3:编辑招工,4:发布招工,5:招工搜索结果页,
6:编辑找活名片,7:新用户找活意向,8:发布找活,9:招工列表,10:找活
搜索结果页,11:全部职位"
   */
  source_id?: keyof SourceMap,
}
/**
 * 职位选择器返回数据类型
 * @interface IClassifyDataRp
 * @property {string} id - 职位ID
 * @property {string} name - 职位名称
 * @property {string} mode - 1.招聘类 2.订单类
 * @property {Array<string>} industries - 行业ID
 */export interface IClassifyDataRp {
  id?: string
  name?: string
  mode?: number
  industries?: Array<string>
}

export type IAddressParams = {
  /** 页面标题- 默认：请选择求职区域 */
  title?: string
  /** 最大选择的数量: 默认为1，代表单选 */
  maxNum?: number
  /**
   * 已选中的地址数组，当为单选时则只拿第一个值做选中处理
   * @info 值可以是地址id，或者地址ad_code
   * */
  areas?: (string | number)[]
  /** 是否显示定位地址-默认false不显示 */
  showLocation?: boolean
  /** 选择地址的级别: 默认为3级 */
  level?: 2 | 3
  /** 是否隐藏全国的地址选项-默认false不隐藏 */
  hideNation?: boolean
  /** 需要禁用的地址id数组，禁用的地址无法被选中 */
  disabledIds?: number[]
}

export interface BuryCallAddressParams {
  /**
   * 1-招工列表、2-发布招工、3-找活列表、4-编辑招工、5-编辑找活名片、6-招工搜索中间页、7-招工搜索结果页、8-找活搜索中间页、9- 找活搜索结果页 、10-发布找活名片、11- 新牛人引导 、12-IM发位置
   */
  source_id: string,
  /**
   * 招工列表、发布招工、找活列表、编辑招工、编辑找活名片、招工搜索中间页、招工搜索结果页、找活搜索中间页、找活搜索结果页、发布找活名片、新牛人引导、IM发位置
   */
  source: string,
  /** 按钮的label */
  button_name: string,
}

export type OpenAddressCB = (areaDataS: ILocation.TAreaData[]) => void
