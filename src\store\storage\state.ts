/*
 * @Date: 2023-12-26 13:55:06
 * @Description: 项目中所有 storage 和初始值
 */

import { resumeDetailsDef } from './defData'

export default {
  /** 是否存在token，用来判断是否登录 */
  token: '',
  userInfo: {} as YModels['POST/account/v1/userBase/getLoginUserInfo']['Res']['data'],
  /** 首页选中的工种数据 */
  homeClassify: [],
  /** 首页定位附近的数据 */
  homeNearbyLocation: {
    success: null,
    data: null,
  },
  jobListFilterConfig: {} as YModels['POST/account/v1/setting/config']['Res']['data']['jobListFilterConfig'],
  /** 首页附近浮标 */
  // homeNearbyBuoy: true,
  /** 列表进入就获取的定位数据 */
  homeLocation: {
    success: null as boolean | null,
    data: {
      areaId: 1,
      name: '全国',
      latitude: null,
      longitude: null,
    },
  },
  /** 用户定位数据 */
  userLocation: {
    success: null as boolean | null,
    /** 定位的省份信息 */
    province: '' as ILocation.TAreaData | '',
    /** 定位的城市信息 */
    city: '' as ILocation.TAreaData | '',
    /** 定位的地区信息 */
    district: '' as ILocation.TAreaData | '',
    /** 定位的地址id  */
    id: '',
    ad_code: '',
    name: '',
    ad_name: '',
    letter: '',
    latitude: null,
    longitude: null,
    level: 0,
    pid: 0,
    gid: 0,
  },
  /** 主站搜索页-历史搜索数据 recruit:招工，resume:找活 */
  searchHistory: { recruit: [] as Array<string>, resume: [] as Array<string> },
  /** 我的找活名片详情 */
  myResumeDetails: { ...resumeDetailsDef },

  buryDebug: false,
}
