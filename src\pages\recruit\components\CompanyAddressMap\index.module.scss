.map {
    width: 100%;
    height: 240px;
    border-radius: 24px;
}

.companyContainer {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 32px;
}

.emptyMapContainer {
    width: 100%;
    height: 96px;
    background-image: url(https://cdn.yupaowang.com/yupao_mini/ali_map_empty_bg.png);
    background-color: rgba(245, 247, 252, 1);
    background-size: contain;
    border-radius: 16px;
    padding: 0 24px;
    box-sizing: border-box;
    display: flex;
    flex-direction: row;
    align-items: center;
    vertical-align: middle;
}

.addressText {
    margin-left: 16px;
    font-size: 26px;
    font-weight: 400;
    line-height: 36px;
    color: $text85
}

.companyLogo {
    width: 96px;
    height: 96px;
    object-fit: contain;
    border-radius: 12px;
}

.companyText {
    width: 494px;
    display: block;
}

.companyName {
    @include textrow(1);
    width: 100%;
    font-size: 30px;
    font-weight: 400;
    line-height: 42px;
    color: $text85;
}

.companyDesc {
    margin-top: 8px;
    font-size: 26px;
    font-weight: 400;
    line-height: 36px;
    color: $text65;
}

.addressContainer{
    padding: 32px 0 0 0;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
}

.address {
    background: #fff;
    height: 36px;
    word-break: keep-all;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    font-size: 26px;
    font-weight: 400;
    line-height: 36px;
    color: rgba(0,0,0,.85);
}

.distance {
    display: flex;
    flex-shrink: 0;
    font-size: 26px;
    font-weight: 400;
    line-height: 36px;
    color: rgba(0,0,0,.65);
}

.hidden {
    display: none !important;
}

.ic {
    line-height: 36px;
    transform: translateY(-2px);
}