import { View, Image, Text } from '@tarojs/components'
import { useCallback, useMemo } from 'react'
import cn from 'classnames'
import { Card } from '../Card'
import s from './index.module.scss'
import IconFont from '@/components/IconFont'
import { useSelector } from '@/core/store'
import useClick from '@/hooks/useClick'
import { haversineDistance } from '@/utils/location'

export default (props: any) => {
  const { info = {}, companyInfo = {} } = props

  const { longitude, latitude } = info.location || {}

  const { name: companyName, logo: companyLogo, companyScaleText, enterpriseBaseInfoId } = companyInfo

  const { latitude: myLat, longitude: myLon } = useSelector((state) => state.storage.userLocation)

  const jumpToEnterprise = useClick(async () => {
    $.router.push('/subpackage/company/index', { infoId: enterpriseBaseInfoId })
  })

  /** 计算距离 */
  const distance = useMemo(() => {
    if (!longitude || !latitude || !myLon || !myLat) {
      return ''
    }
    return haversineDistance(latitude, longitude, myLat, myLon)
  }, [longitude, latitude, myLon, myLat])

  const onOpenLocation = useClick(async () => {
    if (!latitude || !longitude) return undefined
    $.taro.openLocation({
      name: companyName || info.addressTitle,
      address: info.addressTitle,
      longitude: Number(longitude),
      latitude: Number(latitude),
      scale: 18,
    })

    console.log('respond click')
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve(undefined)
      }, 1000)
    })
  })

  const mapComponent = useMemo(() => {
    return <View className={s.emptyMapContainer} onClick={onOpenLocation}>
      <View className={s.ic}><IconFont type="yp-a-Repositioning_zhongxindingwei" size={28} color='rgb(134, 135, 137)'></IconFont></View>
      <Text className={s.addressText}>{info.addressTitle || ''}</Text>
    </View>
  }, [latitude, longitude, info, companyName, distance, onOpenLocation])

  return <Card>
    <View className={cn(s.companyContainer, { [s.hidden]: !companyName || info.checkDegreeStatus != 2 })} onClick={jumpToEnterprise}>
      <Image src={companyLogo} className={s.companyLogo} mode="aspectFill"></Image>
      <View className={s.companyText}>
        <View className={s.companyName}>{companyName}</View>
        <View className={cn(s.companyDesc, { [s.hidden]: !companyScaleText })}>{companyScaleText}</View>
      </View>
      <IconFont type="yp-mbxl" size={32} color="rgba(0,0,0,.45)"></IconFont>
    </View>
    {mapComponent}
  </Card>
}
