import cn from 'classnames'
import { Textarea, View as V } from '@tarojs/components'
import { useState } from 'react'
import { useLoad, useReady } from '@tarojs/taro'
import s from './index.module.scss'
import { getState } from '@/core/store'
import { isIos } from '@/core/utils'
import Page from '@/components/Page'

export default () => {
  const [data, setData] = useState({
    maxContent: 500,
    isFocus: false,
    bottomHeight: 0,
    /** 页面传入的内容 */
    contentOld: '',
  })

  /** 输入框输入的内容 */
  const [content, setContent] = useState('')
  /** 输入框默认的内容 */
  const [contentDef, setContentDef] = useState<string>('')

  useLoad(() => {
    const { content } = $.router.data || {}
    setContentDef(content)
    setContent(content)
    setData({
      ...data,
      contentOld: content,
    })
  })

  useReady(async () => {
    await $.wait(300)
    setData({ ...data, isFocus: true })
  })

  const onClear = () => {
    setContent('')

    setContentDef(' ')
    setTimeout(
      () => setContentDef(''),
      35,
    )
  }

  /** 保存编辑的内容 */
  const onSave = async () => {
    const introduce = `${content}`.trim()
    if (!saveBool()) {
      return
    }
    const { resumeUuid } = getState().storage.myResumeDetails
    try {
      const [, res] = await $.request['POST/resume/v3/perfect/introduce']({
        resumeUuid,
        introduce,
      })
      $.msg('保存成功', 2000, true).then(() => {
        $.router.event(res)
        $.router.back()
      })
    } catch (e) {
      console.error('保存个人优势失败:', e)
      $.confirm({
        title: '温馨提示',
        content: '简历修改失败，请联系客服查询具体原因************',
        cancelText: '取消',
        confirmText: '联系客服',
      }).then(() => $.taro.makePhoneCall({ phoneNumber: '4008381888' }))
    }
  }

  /** 判断输入的内容是否有效 */
  const saveBool = () => {
    if (!content || `${content}`.trim() == '') {
      $.msg('个人优势不能为空')
      return false
    }
    if (data.contentOld === content) {
      $.router.back()
      return false
    }
    if (content.length > data.maxContent) {
      $.msg('已超出最大字数限制')
      return false
    }
    return true
  }

  const onFocus = (e) => {
    const newData = {
      ...data,
      isFocus: true,
      bottomHeight: e.detail.height || 0,
    }
    setData(newData)

    // 页面回到顶部
    /* $.taro.pageScrollTo({
      scrollTop: 0,
      duration: 0,
    }) */
    if ($.taro.getEnv() === 'ALIPAY' && $.isIos()) {
      getKeyHeight(newData)
    }
  }

  const getKeyHeight = (newData, loop = 0) => {
    if (loop >= 10) {
      return
    }
    const keyboardH = $.taro.getStorageSync('keyboardH')
    if (!keyboardH || keyboardH < 10) {
      setTimeout(() => {
        getKeyHeight(newData, loop + 1)
      }, 300)
      return
    }
    setData({
      ...newData,
      bottomHeight: keyboardH,
    })
  }

  const onHeightChange = (e) => {
    setData({
      ...data,
      bottomHeight: e.detail.height || 0,
    })
  }

  /** 收起键盘 */
  const onHideKey = () => {
    setData({
      ...data,
      isFocus: false,
      bottomHeight: 0,
    })
  }

  return (<Page>
    <V className={s.body}>
      <V onClick={onHideKey} className={s.head}>
        <V className={s.title}>我的优势</V>
        <V className={s.desc}>丰富我的优势介绍，更能赢得老板青睐</V>
      </V>
      <Textarea
        className={cn(s.textarea, isIos() ? s['textarea-ios'] : '')}
        placeholder='等待输入内容'
        placeholderClass='等待输入内容'
        value={contentDef}
        maxlength={-1}
        adjustPosition
        disableDefaultPadding
        showConfirmBar={false}
        focus
        onKeyboardHeightChange={onHeightChange}
        onFocus={onFocus}
        onBlur={onHideKey}
        onInput={({ detail }) => {
          setContent(detail.value)
          if (!detail.value) {
            setContentDef('')
          }
        }}
      >
      </Textarea>

    </V>
    <V className={s.footer}>
      <V className={s['info-text']}>
        <V onClick={() => onClear()} className={cn(s.clear, content.length < 1 && s.disabled)}>清空内容</V>
        <V className={s['info-num']}>
          <V className={cn((content.length > data.maxContent) ? s['num-err'] : s.num, content.length < 1 && s['num-gray'])}>
            {content.length || 0}
          </V>
          <V className={s['num-gray']}>/{data.maxContent}</V>
        </V>
      </V>
      <V className={cn(s.btn)} onClick={onSave}>确定</V>
    </V>
  </Page>)
}
