import { useRouter } from '@tarojs/taro'
import { useRef } from 'react'

/**
 * 处理路由的参数
 */
export default <T extends Record<string, string>>(): T => {
  const pRef = useRef<T>()
  const p = useRouter()

  if (!pRef.current) {
    pRef.current = p.params as T
    // 抖音小程序，上的路径被encode 处理， 需要解码
    if (process.env.TARO_ENV === 'tt') {
      pRef.current = JSON.parse(decodeURIComponent(JSON.stringify(p.params)))
    }
  }

  return (pRef.current || {}) as T
}
