/*
 * @Date: 2024-01-04 10:58:50
 * @Description: yarn start 脚本
 */

import { spawn } from 'cross-spawn'
import inquirer from 'inquirer'

const miniConfig = {
  tt: [
    {
      name: '鱼泡网',
      value: 'tt50eb2656cfd1bd43',
    },
    {
      name: '优岗职聘',
      value: 'tt959de3200aaf5cea01',
    },
    {
      name: '宜就业',
      value: 'ttc242b4ff5d766f5b01',
    },
    {
      name: '宜宾人社',
      value: 'tt530725bacc328bc501',
    },
  ],
  alipay: [
    {
      name: '鱼泡直聘',
      value: '2021004190694160',
    },
  ],
  xhs: [
    {
      name: '萌瓜招聘',
      value: '6723389da1d0f60001dd06d3',
    },
  ],
  ks: [
    {
      name: '鱼泡网',
      value: 'ks712413168329900478',
    },
  ],
}

const envList = [
  {
    name: '测试环境',
    value: 'test',
  },
  {
    name: '开发环境',
    value: 'dev',
  },
  {
    name: '预发布环境',
    value: 'pre',
  },
  {
    name: '生产环境',
    value: 'prod',
  },
]

/** 确认用户的选项 */
function checkList() {
  const promptList = [
    {
      type: 'list',
      message: '请选择平台',
      pageSize: 100,
      name: 'platform',
      choices: [
        // {
        //   name: '支付宝',
        //   value: 'alipay',
        // },
        // {
        //   name: '抖音',
        //   value: 'tt',
        // },
        // {
        //   name: '小红书',
        //   value: 'xhs',
        // },
        {
          name: '快手',
          value: 'ks',
        },
      ],
    },
    {
      type: 'list',
      message: '请选择小程序',
      name: 'subId',
      pageSize: 100,
      choices: (answers) => {
        const { platform } = answers
        return miniConfig[platform].map((item) => {
          return {
            name: item.name,
            value: item.value,
          }
        })
      },
    },
    {
      type: 'list',
      message: '请选择操作类型',
      pageSize: 100,
      name: 'action',
      choices: [
        {
          name: '开发',
          value: 'develop',
        },
        {
          name: '打包',
          value: 'build',
        },
      ],
    },
    {
      type: 'list',
      message: '选择环境',
      pageSize: 100,
      name: 'env',
      choices: envList,
    },

  ]
  return inquirer.prompt(promptList)
}

checkList().then(({ platform, action, subId, env }) => {
  process.env.TARO_APP_ID = subId // 更换appid
  if (action == 'build') {
    spawn('npm', ['run', `build:${platform}`, '--', '--mode', `${env}`], {
      stdio: 'inherit',
    })
  } else {
    spawn('npm', ['run', `build:${platform}`, '--', '--watch', '--mode', `${env}`], {
      stdio: 'inherit',
    })
  }
})
