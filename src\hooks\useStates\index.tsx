/*
 * @Date: 2024-11-25 10:22:13
 * @Description: 赋值
 */

import { useCallback, useState } from 'react'

export default function useStates<T extends Record<string, any>>(value: T): [T, (v: Partial<T>) => void] {
  const [state, setState] = useState<T>(value)

  // 更新时使用Partial<T>来允许更新部分字段
  const onUpdate = useCallback((newValue: Partial<T>) => {
    setState(prevState => ({ ...prevState, ...newValue })) // 合并现有状态和部分新值
  }, [])

  return [state, onUpdate]
}
