/*
 * @Date: 2024-01-11 15:53:47
 * @Description: 全局内存数据
 */

import { createSlice } from '@reduxjs/toolkit'

type NationList = Models['GET/account/v1/userBase/getNation']['Res']['data']['list']

const { reducer, actions, name } = createSlice({
  name: 'global',
  initialState: {
    showLoginModel: {} as { pageId: number, success: () => void, fail: () => void },
    /** 民族信息列表 */
    nationList: [] as NationList,
  },
  reducers: {
    setState(state, { payload }) {
      Object.assign(state, payload)
    },
  },
})

const extendActions = {
  /** 获取名族列表 */
  fetchNation: (isUpload = false) => async (dispatch, getState): Promise<NationList> => {
    const { nationList } = getState().global
    if (isUpload || !$.isArrayVal(nationList, 10)) {
      const [, res] = await $.request['GET/account/v1/userBase/getNation']({}, {
        hideMsg: true,
      }).catch((err) => err)
      const list = $.getObjVal(res, 'data.list') || []
      if (list.length) {
        dispatch(actions.setState({ nationList: res.data.list }))
      }
      return list
    }
    return nationList
  },
}

export default {
  name,
  reducer,
  actions: { ...actions, ...extendActions },
}
