import { getState } from '@/core/store'
import { MyResumeDetails } from '@/store/storage/defData'
import { getTempShow } from '@/utils/helper/resume/utils'
import { getWorkStatusName } from '@/utils/helper/resumeFormat'
import formatSubJob from '@/utils/helper/resumeFormat/formatSubJob'
import formatUserInfo from '@/utils/helper/resumeFormat/formatUserInfo'

/** 处理用户找活名片详情数据
 * @param resumeDetail
 */
export const formatResumeDetails = async (detail: MyResumeDetails) => {
  const resumeDetail = $.deepClone(detail)
  // 主名片审核状态 1:待审核,2:审核通过,3:审核不通过
  const { basicResp: basicInfo,
    progressScore } = resumeDetail

  const subJob = formatSubJob(resumeDetail.basicResp.subs || [])
  return {
    /** 名片完善度 */
    progressScore,
    /** 用户信息 */
    userInfo: formatUserInfo(resumeDetail),
    /** 找活的uuid */
    resumeUuid: basicInfo.resumeUuid,
    /** 找活基础信息 */
    basicInfo,
    /** 求职状态 */
    jobStatus: {
      /** 求职状态 */
      workStatus: basicInfo.workStatus,
      /** 求职状态名称 */
      workStatusName: getWorkStatusName(basicInfo.workStatus),
    },
    /** 求职期望-子名片列表 */
    subJob,
    /** 视频简历 */
    videoResp: resumeDetail.videoResp || {},
    /** 找工作法宝 */
    resumeSkillsInfo: {
      /** 主名片审核状态 1:待审核,2:审核通过,3:审核不通过 */
      checkStatus: basicInfo.checkStatus,
      /** 加急服务信息 */
      rightTopResp: resumeDetail.rightTopResp || {},
      /** 刷新服务信息 */
      rightRefreshResp: resumeDetail.rightRefreshResp || {},
    },
  }
}

/** 处理显示规则
 * 个人优势 F_SelfIntroduction
 * 工作城市 F_City
 * 工作经历 F_WorkExperience
 * 项目经历 F_ProjectExperience
 * 教育经历 F_EducationalExperience
 * 资格证书（延后不搞） F_Credentials
 * 视频简历 F_VideoResume
 * 附件简历 F_UploadResumeAttachment
 */
export async function handlerShowDetail() {
  // 获取简历名片信息
  const { myResumeDetails } = getState().storage
  console.log('myResumeDetails', myResumeDetails)

  const res = await getTempShow(myResumeDetails.occIds)

  const showElem = {
    isIntroduce: false, // 个人优势
    isCity: false, // 工作城市
    isWork: false, // 工作经历
    isProject: false, // 项目经历
    isEdu: false, // 教育经历
    isCertificate: false, // 资格证书
    isVideo: false, // 视频简历
    isAttachment: false, // 附件简历
  }

  const controlCodeMap = {
    F_SelfIntroduction: 'isIntroduce',
    F_City: 'isCity',
    F_WorkExperience: 'isWork',
    F_ProjectExperience: 'isProject',
    F_EducationalExperience: 'isEdu',
    F_Credentials: 'isCertificate',
    F_VideoResume: 'isVideo',
    F_UploadResumeAttachment: 'isAttachment',
  }

  res.templates.forEach(({ templateInfo }) => {
    const infoList: any[] = $.getObjVal(templateInfo, 'controlInfoList') || []

    infoList.forEach(({ controlCode, status }) => {
      const key = controlCodeMap[controlCode]
      if (status == 1 && key && !showElem[key]) {
        showElem[key] = true
      }
    })
  })
  return showElem
}
