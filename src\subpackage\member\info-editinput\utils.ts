import { dispatch, actions } from '@/core/store'

/** 默认数据 */
export const defParams = {
  /** 最大文本内容, 当为-1时不限制字数长度 */
  maxContent: -1,
  /** 页面标题 */
  title: '',
  /** 输入框提示 */
  placeholder: '请输入',
  /** 自定义内容 */
  contentDiy: '',
  desc: '',
  maxLength: -1,
  type: '',
}

/** 业务场景的配置 */
const dataInfo = {
  /** 个人资料页编辑邮箱 */
  infoEmail: {
    title: '邮箱',
    placeholder: '请输入邮箱',
    maxContent: -1,
  },
  /** 个人资料页编辑微信 */
  infoWechat: {
    title: '微信号',
    placeholder: '请输入微信号',
    maxContent: 20,
    maxLength: 20,
    desc: '微信号填写要求：支持输入6-20位大小写字母、数字、下划线和减号，不支持输入中文',
  },
}

const resErr = { code: -1 }

export type IType = keyof typeof dataInfo

/** 获取路由数据 */
export function getParams() {
  const params = $.router.data || {}
  const info = $.getObjVal(dataInfo, params.type) || {}
  return {
    ...defParams,
    ...info,
    ...params,
  }
}

/** 编辑邮箱 */
export async function editEmail(val) {
  if (!val) {
    $.msg('请输入邮箱')
    return resErr
  }
  if (!$.validator.isEmail(val)) {
    $.msg('请输入正确的邮箱地址')
    return resErr
  }
  $.showLoading('修改中...')
  const [, res] = await $.request['POST/account/v1/userBase/updateEmail']({
    email: val,
  }).catch((err) => err)
  $.hideLoading()
  if (!res || res.code != 0) {
    $.msg(res.message || '修改失败')
    return res
  }
  dispatch(actions.user.fetchUserData())
  await $.msg('修改成功', 1500, true)

  return res
}

/** 编辑微信号 */
export async function editWeChat(val) {
  if (!val) {
    $.msg('请完善微信号')
    return resErr
  }
  if (!$.validator.isWeChat(val)) {
    $.msg('微信号格式不符，请重新输入')
    return resErr
  }
  $.showLoading('修改中...')
  const [, res] = await $.request['POST/account/v1/userBase/updateWechatNumber']({
    wechatNumber: val,
  }).catch((err) => err)
  $.hideLoading()
  if (!res || res.code != 0) {
    $.msg(res.message || '修改失败')
    return res
  }
  dispatch(actions.user.fetchUserData())
  await $.msg('修改成功', 1500, true)

  return res
}
