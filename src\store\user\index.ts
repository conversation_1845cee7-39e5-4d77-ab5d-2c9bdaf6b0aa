/*
 * @Date: 2024-01-11 15:53:47
 * @Description: 地址数据
 */

import { createSlice } from '@reduxjs/toolkit'
import storage from '../storage/storage'
import { actions as storeAction } from '@/core/store'

const { reducer, actions, name } = createSlice({
  name: 'user',
  initialState: {
    /** 用户中心信息 */
    userData: {
      userBaseObj: {
        userDateObj: {},
        userHeadPortraitObj: {},
        usernameObj: {},
        userTelObj: {},
      },
      userBizAggObj: {},
      userEnterpriseObj: {},
      userRealNameObj: {},
      userIntegralObj: {},
      userBossInfo: {
        userEnterpriseResp: {},
      },
      userCommonInfo: {
        userHeadPortraitResp: {
          headPortrait: '',
        },
        userNameResp: {},
        userRealNameResp: {},
        userTelResp: {},
      },
      /** 求职端用户私有信息 */
      userApplicantInfo: {
        birthday: '',
      },
      /** toAuth：去实名认证 */
      toAuth: false,
    } as Models['POST/account/v2/userBase/userCenter']['Res']['data'] & Models['POST/account/v2/userBase/userInfoByRole']['Res']['data'] & {toAuth: boolean},
  },
  reducers: {
    setState(state, { payload }) {
      Object.assign(state, payload)
    },
  },
})

// 退出清理缓存
const clearCache = () => (dispatch) => {
  const payload = { userInfo: {}, token: '' }
  dispatch(storeAction.storage.setItems(payload))
  storage.remove('pubishData')
}

const extendActions = {
  /**
   * 更新用户信息
   */
  fetchUserData: (payload = {}) => async (dispatch) => {
    const [[userCenter], [userByRole], [memberVip]] = await Promise.all([
      $.request['POST/account/v2/userBase/userCenter'](),
      $.request['POST/account/v2/userBase/userInfoByRole'](),
      $.request['POST/member/v1/vipQuery/hasVip'](),
    ])
    const hasVip = memberVip || {}

    // 实名状态
    const { userRealNameObj } = userCenter || {}
    const toAuth = !userRealNameObj || userRealNameObj.realNameStatus === 0 || userRealNameObj.realNameStatus === -1
    const userData = {
      ...payload,
      ...userCenter,
      ...userByRole,
      toAuth,
      hasVip,
    }

    dispatch(actions.setState({
      userData,
    }))
    return userData
  },
}
export default {
  name,
  reducer,
  actions: { ...actions, ...extendActions, clearCache },
}
