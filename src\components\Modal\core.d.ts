export type ActionSheet = {
  /** 菜单数据列表 */
  itemList: {
    /** 菜单显示的文本 */
    name: string,
    /** 菜单的颜色 */
    itemColor?: string,
    /** 菜单的样式 */
    style?: string,
    /** 点击按钮是否异步关闭弹框 默认跟随外层的syncClose值 */
    syncClose?: boolean,
    /** 是否属于选中状态 默认false */
    active?: boolean,
    [key: string]: any
  }[],
  /** 弹框标识-用于埋点使用-没有标识则不埋点 */
  dialogIdentify?: string,
  /** 弹框标题-默认没有标题 */
  title?: string,
  /** 每个菜单的默认颜色 */
  itemColor?: string,
  /** 取消按钮文本-默认:取消 */
  cancelText?: string,
  /** 点击遮罩是否可以关闭 默认true */
  maskClose?: boolean,
  /** 点击按钮是否异步关闭弹框 默认false */
  syncClose?: boolean,
  /** 点击按钮的回调方法: 可以将syncClose设置为false,达到同步关闭弹框, 方法必须返回Promise<boolean>才能正常关闭弹框 */
  success?: (obj: any) => Promise<boolean>,
}
