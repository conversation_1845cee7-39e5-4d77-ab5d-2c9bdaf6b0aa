const path = require('path')

module.exports = (ctx) => {
  ctx.modifyBuildAssets(({ assets }) => {
    Object.keys(assets).forEach((fileName) => {
      if (fileName.endsWith('.js')) {
        let content = assets[fileName].source()

        // 判断是否为 Taro 页面文件
        if (content.includes('Page({')) {
          // 动态插入 import 和组件使用代码
          const importStatement = 'import CommonComponent from \'@components/Page\';\n'
          const componentInsertion = `
                        const originalRender = Page.render;
                        Page.render = function() {
                            return (
                                <>
                                    {originalRender.apply(this, arguments)}
                                    <CommonComponent />
                                </>
                            );
                        };
                    `

          // 插入到文件头部
          if (!content.includes('@components/Page')) {
            content = importStatement + content
          }

          // 插入通用组件逻辑
          content = content.replace('Page({', `Page({\n${componentInsertion}`)

          // 替换回资产
          assets[fileName] = {
            source: () => content,
            size: () => content.length,
          }
        }
      }
    })
  })
}
