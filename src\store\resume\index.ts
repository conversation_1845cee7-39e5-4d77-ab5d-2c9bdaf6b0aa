/*
 * @Date: 2022-01-06 14:24:43
 * @Description: 找活相关
 */

import { createSlice, PayloadAction } from '@reduxjs/toolkit'
import type { ITemplateList } from './index.d'
import { resumeCompleteDef, globalConfigDef, resumeExistDef } from './data'
// import { getFilterData } from '@/utils/helper/resume/index'
import { actions as gActions, dispatch as gDispatch } from '@/core/store'

const { reducer, actions, name } = createSlice({
  name: 'resume',
  initialState: {
    /** 别人的找活卡片详情数据 */
    resumeDetailsOther: {} as any,
    /** 别人的找活详情requestData */
    requestData: {},
    /** 因接口不一致，仅带入部分优先级高的数据 */
    info: {
      id: '',
      // 找活数据的uuid
      uuid: '',
    },
    /** 名片全局配置接口 v4.0.0 */
    globalConfig: { ...globalConfigDef },
    /** 根据用户ID查询模板-找活通用配置模板 v4.0.0 */
    resumeTemplates: [] as ITemplateList,
    /** 完善度阶段信息 v4.0.0 */
    resumeComplete: { ...resumeCompleteDef },
    /** 是否含有找活名片 v4.0.0 resumeExist. */
    resumeExist: { ...resumeExistDef },
  },
  reducers: {
    setState(state, { payload }: PayloadAction<Record<string, any>>) {
      Object.assign(state, payload)
    },
    setInfo(state, { payload }) {
      state.info = payload
    },
    setRequestData(state, { payload }) {
      state.requestData = payload
    },
  },
})

/**
 * 存储详情需要的地址
 * @param {Object} payload 参数
 * @param {String} payload.uuid 找活uuid
 * @param {String} payload.originType '' | 'logistics' | 'factory' ,来源类型
 * @param {Number[]} payload.cities 列表筛选的城市
 * @param {Number[]} payload.occupations 列表筛选的工种
 * @param {Boolean} payload.isSlide 是否是滑动
 * @returns
 */
const fetchGetDetailOther = (payload) => (dispatch, getState) => {
  return new Promise((resolve) => {
    // const filterData = getFilterData()
    // const cities = $.isArrayVal(payload.cities) ? payload.cities : filterData.area_id
    // const occupations = $.isArrayVal(payload.occupations) ? payload.occupations : filterData.classify_id
    const cities = $.isArrayVal(payload.cities) ? payload.cities : []
    const occupations = $.isArrayVal(payload.occupations) ? payload.occupations : []
    const params = {
      resumeSubUuid: payload.uuid,
      purchaseBaseReq: {
        cities,
        occupations,
      },
    }
    $.request['POST/resume/v3/detail/app/otherDetail'](params)
      .then((res) => {
        dispatch(actions.setRequestData({ ...(payload.isSlide ? getState().resume.requestData : {}), [params.resumeSubUuid]: res }))
        resolve(res)
      })
      .catch((res) => {
        dispatch(actions.setRequestData({ ...(payload.isSlide ? getState().resume.requestData : {}), [params.resumeSubUuid]: res }))
        resolve(res)
      })
  })
}

/** 更新是否含有找活名片的数据7.0.0
 * @params isUpdate 是否更新model缓存
 */
const fetchResumeExist = (isUpdate = false) => (dispatch, getState) => {
  return new Promise((resolve) => {
    const resumeExist = getState().resume.resumeExist || { ...resumeExistDef }
    const { userId } = getState().storage.userInfo || {}
    if (!(isUpdate || !!userId || resumeExist.isDef || !!resumeExist.resumeUuid)) {
      resolve({ ...resumeExist, res: null })
      return
    }
    $.request['POST/resume/v3/base/exist']({}, { isNoToken: true, hideMsg: true }).then(([_, res]) => {
      if (res.error) {
        dispatch(actions.setState({ resumeExist: { ...resumeExistDef } }))
        resolve({ ...resumeExistDef, res })
        return
      }
      if (res && res.data && !res.data.exist) {
        // 如果简历不存在，清空简历信息
        gDispatch(gActions.storage.removeItem('myResumeDetails'))
      }
      dispatch(actions.setState({ resumeExist: res.data || { ...resumeExistDef } }))
      const data = res.data || { ...resumeExistDef }
      resolve({ ...data, res })
    }).catch((err) => {
      dispatch(actions.setState({ resumeExist: { ...resumeExistDef } }))
      resolve({ ...resumeExistDef, res: err })
    })
  }) as Promise<YModels['POST/resume/v3/base/exist']['Res']['data'] & {res: any}>
}

const fetchGlobalConfig = (isUpdate = false) => (dispatch, getState): Promise<typeof globalConfigDef> => {
  return new Promise((resolve, reject) => {
    const globalConfig = getState().global.globalConfig || {}
    if (!isUpdate && Object.keys(globalConfig).length > 3 && globalConfig.rightsConfigResp) {
      resolve(globalConfig)
      return
    }
    $.request['POST/resume/v3/common/globalConfig']({}, { hideMsg: true })
      .then(([_, res]) => {
        const { error, data } = res || {}
        if (error) {
          dispatch(actions.setState({ globalConfig: { ...globalConfigDef } }))
          reject(res)
        } else {
          dispatch(actions.setState({ globalConfig: data || { ...globalConfigDef } }))
          resolve(data || { ...globalConfigDef })
        }
      })
      .catch(([_, res]) => {
        dispatch(actions.setState({ globalConfig: { ...globalConfigDef } }))
        reject(res)
      })
  })
}

export default {
  name,
  reducer,
  actions: { ...actions, fetchGetDetailOther, fetchGlobalConfig, fetchResumeExist },
}
