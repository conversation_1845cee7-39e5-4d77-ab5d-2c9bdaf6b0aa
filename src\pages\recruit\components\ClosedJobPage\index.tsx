import { View, Image, Text, Button } from '@tarojs/components'
import classNames from 'classnames'
// import { useMemo, useRef, useState } from 'react'
// import { useDidHide, useDidShow, useUnload } from '@tarojs/taro'
import styles from './index.module.scss'

export default ({ visible, isEmployment }) => {
  const backToHome = () => {
    const pages = getCurrentPages()
    if (pages.length > 1) {
      $.router.back()
      return
    }
    if (isEmployment) {
      setTimeout(() => {
        $.router.reLaunch('/pages/index/index')
      }, 500)
      $.taro.exitMiniProgram()
      return
    }
    /** 强制回到首页 */
    $.router.reLaunch('/pages/index/index')
  }

  return <View className={classNames(styles.container, { [styles.hidden]: !visible })}>
    <View className={styles.content}>
      <Image src="https://cdn.yupaowang.com/yupao_mini/recruit_job_closed_ic.png" mode="aspectFill" className={styles.emptyIc}></Image>
      <View className={styles.text}>该职位已下架~请点击左上角按钮返回</View>
      {/* <View className={styles.subText}><Text className={styles.count}>{count}</Text> 秒后自动跳转</View> */}
      <Button className={styles.btn} onClick={() => {
        backToHome()
        // $count(0)
      }}
      >立即跳转</Button>
    </View>
  </View>
}
