.container {
    display: flex;
    flex-direction: column;
    align-items: center;
}


.content {
    background-image: url(https://cdn.yupaowang.com/yupao_mini/live_resume_dialog_bg.png);
    background-size: 100% 100%;
    object-fit: contain;
    width: 606px;
    overflow: visible;
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 0 32px 32px;
    position: relative;
}

.resumeIc {
    width: 246px;
    height: 248px;
    margin-top: -80px;
    position: absolute;
    top: 0;
    left: 50%;
    transform: translateX(-50%);
}


.title {
    font-size: 42px;
    font-weight: bold;
    line-height: 58px;
    color: rgb(51,51,51);
    margin-top: 180px;
}

.desc {
    margin-top: 32px;
    font-size: 30px;
    font-weight: 400;
    color: $text65;
}

.primary {
    color: rgb(0,146,255);
}

.footer {
    margin-top: 48px;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    width: 100%;
}

.footerBtn {
    width: 260px;
    height: 80px;
    text-align: center;
    line-height: 80px;
    font-size: 30px;
    font-weight: bold;
    color: $text65;
    background-color: rgb(245, 247, 252);
    border-radius: 16px;
}

.primaryBtn {
    color: #fff;
    background: rgba(0, 146, 255, 1);

}

.closeIc {
    width: 72px;
    height: 72px;
    margin-top: 96px;
}