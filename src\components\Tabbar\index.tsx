/*
 * @Date: 2024-11-21 17:09:11
 * @Description: tabbar
 */

import { Fragment, useEffect } from 'react'
import { tabBarList } from '@/core/router'
import s from './index.module.scss'

export default ({ active }) => {
  useEffect(() => {
    $.taro.hideTabBar()
  }, [])

  return (
    <Fragment>
      <V className={s.height} />
      <V className={s.tabbar}>
        {tabBarList.map((item) => {
          const isActive = item.text === active
          return (
            <V key={item.pagePath} className={s.item} onClick={() => $.router.push(item.path)}>
              <Img className={s.img} src={isActive ? item.activeIcon : item.icon} />
              <T className={s.text} style={{ color: isActive ? '#0092ff' : '#000000a6' }}>{item.text}</T>
            </V>
          )
        })}
      </V>
    </Fragment>
  )
}
