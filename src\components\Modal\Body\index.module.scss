.body {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  transition: opacity 0.2s ease-in-out;
  z-index: 10001;
}

.mask {
  background-color: rgba(0, 0, 0, 0.3);
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}

.content {
  position: absolute;
  z-index: 2;
}

.center {
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.bottom {
  bottom: 0;
}