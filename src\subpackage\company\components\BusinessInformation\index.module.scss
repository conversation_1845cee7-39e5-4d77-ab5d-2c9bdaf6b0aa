.wrap {
  padding: 36px 32px;
}
.title {
  font-size: 34px;
  color: #FFFFFFFF;

}

.titleBox {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32px;
}

.more {
  color: #FFFFFFA6;
  font-size: 28px;
}

.leftInner {
  font-size: 28px;
  color: #FFFFFFFF;

}

.rightInner {
  margin-left: 24px;
}

.cell {
  display: flex;
  margin-bottom: 32px;
}

.header {
  height: 112px;
  display: flex;
  align-items: center;
  padding-left: 32px;
}

.companyName {
  padding: 24px 32px;
  font-size: 42px;
  color: rgba(0, 0, 0, 0.85);
  font-weight: bold;
}

.popupWrap {
  height: 80vh;
}

.scrollView {
  height: 70vh;
  &.safeArea {
    @include safe-area(24px);
  }
}

.line {
  display: flex;
  margin-bottom: 48px;
}

.item {
  flex: 1;
}

.pTitle {
  font-size: 26px;
  color: #000000A6;
  margin-bottom: 16px;
}

.pInner {
  font-size: 26px;
  color: #000000;
  line-height: 48px;
}

.scrollView {
  padding: 0 32px;
  padding-top: 48px;
}