import { AUDIT_STATUS, CHANGE_APPLY_MAP, CHANGE_APPLY_MAP_OBJ, FINANCE_SCALE_MAP, INSURANCE_MAP, LEAVE_VACATION_MAP, LIFE_SUBSIDY_MAP, OPTION_MAP, OVERTIME_SITUATION_MAP, PROMOTION_SYSTEM_MAP, REST_TIME_MAP, SKILL_DEVELOPMENT_MAP, STAFF_SIZE_MAP, STOCK_OPTION_SALARY_MAP, TALENT_MOTIVATION_MAP } from './mapConfig'

type PageType = 'home' | 'edit' | 'preview'

/**
 * 返回对应类型的value
 */
type ReturnValue<T extends readonly any[]> = T[number]['value']

/**
 * 返回对应类型的label
 */
type ReturnLabel<T extends readonly any[]> = T[number]['label']

/**
 * 返回指定格式的 value label
 */
type ReturnObjItem<V, L> = {value: V, content: L }

/**
 * 格式化企业福利的数据
 */
export interface FormatEnterpriseOptionListType {
  /**
   * 公司的 融资规模
  */
  financeScale: ReturnObjItem<ReturnValue<typeof FINANCE_SCALE_MAP>, ReturnLabel<typeof FINANCE_SCALE_MAP>>
  /**
   * 公司的 员工规模
  */
  staffSize: ReturnObjItem<ReturnValue<typeof STAFF_SIZE_MAP>, ReturnLabel<typeof STAFF_SIZE_MAP>>
  /**
   * 公司的 工作时间
   */
  restTime: ReturnObjItem<ReturnValue<typeof REST_TIME_MAP>, ReturnLabel<typeof REST_TIME_MAP>>
  /**
   * 公司的 加班情况
   */
  overtimeSituationTime: ReturnObjItem<ReturnValue<typeof OVERTIME_SITUATION_MAP>, ReturnLabel<typeof OVERTIME_SITUATION_MAP>>
  /** 工作时间 */
  workTime: ReturnObjItem<string, string>
  /** 晋升制度 */
  promotionSystem: ReturnObjItem<ReturnValue<typeof PROMOTION_SYSTEM_MAP>[], typeof PROMOTION_SYSTEM_MAP>
  /** 人才激励 */
  talentMotivation: ReturnObjItem<ReturnValue<typeof TALENT_MOTIVATION_MAP>[], typeof TALENT_MOTIVATION_MAP>
  /** 能力培养 */
  skillDevelopment: ReturnObjItem<ReturnValue<typeof SKILL_DEVELOPMENT_MAP>[], typeof SKILL_DEVELOPMENT_MAP>
  /** 保险 */
  insurance: ReturnObjItem<ReturnValue<typeof INSURANCE_MAP>[], typeof INSURANCE_MAP>
  /** 期权薪资 */
  stockOptionSalary: ReturnObjItem<ReturnValue<typeof STOCK_OPTION_SALARY_MAP>[], typeof STOCK_OPTION_SALARY_MAP>
  /** 度假休假 */
  leaveVacation: ReturnObjItem<ReturnValue<typeof LEAVE_VACATION_MAP>[], typeof LEAVE_VACATION_MAP>
  /** 生活补贴 */
  lifeSubsidy: ReturnObjItem<ReturnValue<typeof LIFE_SUBSIDY_MAP>[], typeof LIFE_SUBSIDY_MAP>
}

/**
 * 格式化时间数据展示
 * @param date 时间是一个数字，比如后端返回 8:00 ===> 8
 * @param dateStc 时间字符串，后端返回的 19:00 ---> 19:00
 * @returns 格式化后的字符串 中午12:00 || 上午:8:00 || 下午:19:00
 */
export const formatDateText = (date: number, dateStc: string) => {
  if (date == 12) {
    return `中午${dateStc}`
  }
  return date < 12 ? `上午${dateStc}` : `下午${dateStc}`
}

/**
 * 处理企业列表的福利选项，后台返回的为数据类型，前端定义字符串。需要把后端的类型和前端的定义关联起来
 *
 * @see http://yapi.3pvr.com/project/1649/interface/api/72771
 * @param data 后台返回的数据 enterpriseOptionList， 代表 企业福利
 * @returns {FormatEnterpriseOptionListType} 返回一个格式化后的对象
 */
export const processEnterpriseOptionList = (data: Record<string, any>[]): Partial<FormatEnterpriseOptionListType> => {
  const obj = {} as FormatEnterpriseOptionListType

  data.forEach(item => {
    switch (item.type) {
    /**
       * 公司的 融资规模
      */
    case OPTION_MAP.FINANCE_SCALE: {
      const value = (item.content || [])[0]
      const content = FINANCE_SCALE_MAP.find((item) => item.value == value)?.label
      Object.assign(obj, { financeScale: { value, content } })
      break
    }

    /**
      * 人员规模
      */
    case OPTION_MAP.STAFF_SIZE: {
      const value = (item.content || [])[0]
      const content = STAFF_SIZE_MAP.find((item) => item.value == value)?.label
      Object.assign(obj, { staffSize: { value, content } })
      break
    }

    /** 休息时间 */
    case OPTION_MAP.REST_TIME: {
      const value = (item.content || [])[0]
      const content = REST_TIME_MAP.find((item) => item.value == value)?.label
      Object.assign(obj, { restTime: { value, content } })
      break
    }
    /** 加班情况 */
    case OPTION_MAP.OVERTIME_SITUATION: {
      const value = (item.content || [])[0]
      const content = OVERTIME_SITUATION_MAP.find((item) => item.value == value)?.label
      Object.assign(obj, { overtimeSituationTime: { value, content } })
      break
    }

    /** 工作时间 */
    case OPTION_MAP.WORK_TIME: {
      const [startTime, endTime] = item.content || []
      if (item.content.length <= 0) {
        return
      }
      // 处理时间样式
      const content = `${formatDateText(parseFloat(startTime), startTime)}-${formatDateText(parseFloat(endTime), endTime)}`
      Object.assign(obj, { workTime: { value: [startTime, endTime], content } })

      break
    }
    /** 晋升制度 */
    case OPTION_MAP.PROMOTION_SYSTEM: {
      const value = (item.content || [])
      const content = PROMOTION_SYSTEM_MAP.filter((item) => value.includes(item.value))
      Object.assign(obj, { promotionSystem: { value, content } })
      break
    }
    /** 人才激励 */
    case OPTION_MAP.TALENT_MOTIVATION: {
      const value = (item.content || [])
      const content = TALENT_MOTIVATION_MAP.filter((item) => value.includes(item.value))
      Object.assign(obj, { talentMotivation: { value, content } })
      break
    }
    /** 能力培养 */
    case OPTION_MAP.SKILL_DEVELOPMENT: {
      const value = (item.content || [])
      const content = SKILL_DEVELOPMENT_MAP.filter((item) => value.includes(item.value))
      Object.assign(obj, { skillDevelopment: { value, content } })
      break
    }
    /** 保险 */
    case OPTION_MAP.INSURANCE: {
      console.log(item)

      const value = (item.content || [])
      const content = INSURANCE_MAP.filter((item) => value.includes(item.value))
      console.log(content)

      Object.assign(obj, { insurance: { value, content } })
      break
    }
    /** 期权薪资 */
    case OPTION_MAP.STOCK_OPTION_SALARY: {
      const value = (item.content || [])
      const content = STOCK_OPTION_SALARY_MAP.filter((item) => value.includes(item.value))
      Object.assign(obj, { stockOptionSalary: { value, content } })
      break
    }
    /** 度假休假 */
    case OPTION_MAP.LEAVE_VACATION: {
      const value = (item.content || [])
      const content = LEAVE_VACATION_MAP.filter((item) => value.includes(item.value))
      Object.assign(obj, { leaveVacation: { value, content } })
      break
    }
    /** 生活补贴 */
    case OPTION_MAP.LIFE_SUBSIDY: {
      const value = (item.content || [])
      const content = LIFE_SUBSIDY_MAP.filter((item) => value.includes(item.value))
      Object.assign(obj, { lifeSubsidy: { value, content } })
      break
    }
    default:
    }
  })
  return obj
}

export type IntroduceList = Array<{
  /** 枚举的 key  */
  type: keyof typeof CHANGE_APPLY_MAP_OBJ
  /** 标题，展示在页面的 */
  title: typeof CHANGE_APPLY_MAP_OBJ[keyof typeof CHANGE_APPLY_MAP_OBJ];
  /** 展示的内容，也在页面展示 */
  content: string
}>

/**
 * 处理公司简介，三个不同路径到这个页面。做相应的逻辑处理
 * @param detail 接口返回的源数据
 * @param pageType 从不同的入口进入，先如今只有home，默认是首页
 * @returns {IntroduceList} 返回格式化后的数组
 */

export const processCompanyIntroduce = (detail: any, pageType: PageType = 'home'): IntroduceList => {
  const introduceList = [] as IntroduceList
  try {
    // 公司简介数据拼接
    const enterpriseProfile = (detail.enterpriseChangeApplyList || []).find(item => item.type == CHANGE_APPLY_MAP.COMPANY_DESCRIPTION) || {}
    const companyIntroduce = {
      edit: enterpriseProfile.content || detail.enterpriseBaseInfo.enterpriseProfile,
      preview: enterpriseProfile.auditStatus == AUDIT_STATUS.PROCESSING ? enterpriseProfile.content : detail.enterpriseBaseInfo.enterpriseProfile,
      home: detail.enterpriseBaseInfo.enterpriseProfile,
    }
    companyIntroduce[pageType] && introduceList.push({
      type: 'COMPANY_DESCRIPTION',
      title: CHANGE_APPLY_MAP_OBJ.COMPANY_DESCRIPTION,
      content: companyIntroduce[pageType],
    })

    // 企业文化 数据拼接
    const civilizationDisplay = (detail.enterpriseChangeApplyList || []).find(item => item.type == CHANGE_APPLY_MAP.COMPANY_CULTURE) || {}
    const companyCivilization = {
      edit: civilizationDisplay.content || detail.enterpriseBaseInfo.civilization,
      preview: civilizationDisplay.auditStatus == AUDIT_STATUS.PROCESSING ? civilizationDisplay.content : detail.enterpriseBaseInfo.civilization,
      home: detail.enterpriseBaseInfo.civilization,
    }
    companyCivilization[pageType] && introduceList.push({
      type: 'COMPANY_CULTURE',
      title: CHANGE_APPLY_MAP_OBJ.COMPANY_CULTURE,
      content: companyCivilization[pageType],
    })

    // 发展历程 数据拼接
    const developmentHistory = (detail.enterpriseChangeApplyList || []).find(item => item.type == CHANGE_APPLY_MAP.COMPANY_DEVELOPMENT_HISTORY) || {}
    const companyDevelopment = {
      edit: developmentHistory.content || detail.enterpriseBaseInfo.developmentHistory,
      preview: developmentHistory.auditStatus == AUDIT_STATUS.PROCESSING ? developmentHistory.content : detail.enterpriseBaseInfo.developmentHistory,
      home: detail.enterpriseBaseInfo.developmentHistory,
    }
    companyDevelopment[pageType] && introduceList.push({
      type: 'COMPANY_DEVELOPMENT_HISTORY',
      title: CHANGE_APPLY_MAP_OBJ.COMPANY_DEVELOPMENT_HISTORY,
      content: companyDevelopment[pageType],
    })

    // 获得荣誉 数据拼接
    const honour = (detail.enterpriseChangeApplyList || []).find(item => item.type == CHANGE_APPLY_MAP.COMPANY_ACHIEVEMENT) || {}
    const companyHonour = {
      edit: honour.content || detail.enterpriseBaseInfo.honour,
      preview: honour.auditStatus == AUDIT_STATUS.PROCESSING ? honour.content : detail.enterpriseBaseInfo.honour,
      home: detail.enterpriseBaseInfo.honour,
    }
    companyHonour[pageType] && introduceList.push({
      type: 'COMPANY_ACHIEVEMENT',
      title: CHANGE_APPLY_MAP_OBJ.COMPANY_ACHIEVEMENT,
      content: companyHonour[pageType],
    })
    return introduceList
  } catch (error) {
    console.error(error)
    return []
  }
}

type ImageType = {
  /** 数据唯一ID */
  id: number
  /** 需要的地址 */
  url: string
}

type VideoType = ImageType & {
  /** 封面图地址 */
  cover: string
  [key: string]: any
}

export type VideoImageType = {
  /** 视频数据 */
  videoList: Partial<Array<VideoType>>
  /** 图片数据 */
  imageList: Array<ImageType>
  /** 渲染页面的数据 */
  // renderList: Array<ImageType | VideoType>
}

/**
 * 处理视频和图片
 * @param detail 接口返回的数据
 * @returns {VideoImageType} 返回处理好的数据
 */
export const processVideoImage = (detail: any, pageType: PageType = 'home'): VideoImageType => {
  const videoList = {
    home: (detail.enterpriseVideoList || []).filter(item => item.auditStatus == AUDIT_STATUS.SUCCESS),
    edit: detail.enterpriseVideoList || [],
    preview: (detail.enterpriseVideoList || []).filter(item => item.auditStatus != AUDIT_STATUS.FAILED),
  }
  const imageList = {
    home: (detail.enterpriseAlbumList || []).filter(item => item.auditStatus == AUDIT_STATUS.SUCCESS),
    edit: detail.enterpriseAlbumList || [],
    preview: (detail.enterpriseAlbumList || []).filter(item => item.auditStatus != AUDIT_STATUS.FAILED),
  }
  return {
    videoList: videoList[pageType],
    imageList: imageList[pageType],
  }
}

type GetContent<T extends keyof FormatEnterpriseOptionListType> = FormatEnterpriseOptionListType[T]['content'][number]
export type TalentDevelopmentType = GetContent<'promotionSystem' | 'talentMotivation' | 'skillDevelopment'>[]
/**
 * 处理人才发展
 * @param data 格式化后的福利数据
 * @returns { TalentDevelopmentType } 返回处理后的数组
 */
export const processTalentDevelopment = (data: Partial<FormatEnterpriseOptionListType>): TalentDevelopmentType => {
  const d = [
    ...(data.promotionSystem?.content || []),
    ...(data.talentMotivation?.content || []),
    ...(data.skillDevelopment?.content || []),
  ]
  return d
}

/**
 * 处理产品介绍
 * @param detail 接口返回的源数据
 * @param pageType 从不同的入口进入，先如今只有home，默认是首页
 */
export const processProductIntroduce = (detail: any, pageType: PageType = 'home') => {
  return {
    home: (detail.enterpriseProductList || []).filter(item => item.auditStatus == AUDIT_STATUS.SUCCESS),
    edit: detail.enterpriseProductList || [],
    preview: (detail.enterpriseProductList || []).filter(item => item.auditStatus != AUDIT_STATUS.FAILED),
  }[pageType]
}
