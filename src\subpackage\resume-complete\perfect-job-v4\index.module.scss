.body {
  padding: 0 32rpx 200rpx;
}

.headv {
  padding: 32rpx 0;
}

.headTile {
  color: rgba(0, 0, 0, 0.85);
  font-weight: bold;
  font-size: 54rpx;
  line-height: 76rpx;
}

.headDesc {
  color: rgba(0, 0, 0, 0.65);
  font-size: 26rpx;
  margin-top: 12rpx;
  line-height: 160.000002%;
}

.footer {
  position: fixed;
  left: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 1);
  padding-bottom: constant(safe-area-inset-bottom); /* 兼容 IOS<11.2 */
  padding-bottom: env(safe-area-inset-bottom); /* 兼容 IOS>11.2 */
  width: 100vw;
}

.btnView {
  padding: 24rpx 32rpx;
}

.btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 96rpx;
  border-radius: 16rpx;
  background: rgba(0, 146, 255, 1);
  color: rgba(255, 255, 255, 1);
  font-weight: bold;
  font-size: 34rpx;
  line-height: 48rpx;
}
