import { Video, View as V } from '@tarojs/components'
import styles from './index.module.scss'
import useRouterParams from '@/hooks/useRouterParams'
/**
 * 预览视频页面
*/
export default () => {
  const p = useRouterParams()
  // p.url = url
  // const { url } = $.router.query || {}
  const sysInfo = $.sysInfo()

  return (
    <V className={styles.warp}>
      <V style={{ height: `${sysInfo.headerTop}px` }} />
      <Video src={p.url} controls autoplay className={styles.videoBox} style={{ height: `calc(100vh - ${sysInfo.headerTop}px)` }} />
    </V>
  )
}
