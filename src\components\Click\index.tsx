/*
 * @Date: 2024-11-20 14:04:18
 * @Description: 点击组件，带有防抖功能
 */

import { type ViewProps, View } from '@tarojs/components'
import cn from 'classnames'
import useClick from '@/hooks/useClick'
import s from './index.module.scss'

type Props = ViewProps & {
  onClick?: Function
  time?: number
  /** 按下是否有透明效果 */
  opacity?: boolean
}

export default (props: Props) => {
  const { onClick, className, time, opacity = true, ...other } = props
  const onPress = useClick(onClick, time) as any

  return (
    <View onClick={onPress} className={cn(opacity && s.click, className)} {...other} />
  )
}
