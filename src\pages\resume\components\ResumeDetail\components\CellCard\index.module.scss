.cell {
  font-size: 30rpx;
  padding: 40rpx 0;
  @include bottom-line-b();

  &.hide-line {
    &::before {
      display: none;
    }
    &::after {
      display: none;
    }
  }
}


.title-box {
  display: flex;
  align-items: center;
  padding-bottom: 40rpx;
}

.title-left{
  flex: 1;
  display: flex;
  align-items: center;
}

.title {
  color: rgba(0, 0, 0, 0.85);
  font-weight: bold;
  font-size: 34rpx;
  padding-right: 16rpx;
}

.title-desc {
  flex: 1;
  padding-right: 16rpx;
}

.title-icon {
  display: flex;
  align-items: center;
  padding-left: 16rpx;
}
