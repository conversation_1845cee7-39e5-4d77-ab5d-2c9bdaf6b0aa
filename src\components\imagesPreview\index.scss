/* components/ImagePreview/index.scss */
.preview-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.9);
  z-index: 1000;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  // 确保在快手小程序中也能正确显示
  width: 100vw;
  height: 100vh;

  .preview-swiper {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;

    .preview-swiper-item {
      display: flex;
      align-items: center;
      justify-content: center;
      height: 100%;
      width: 100%;
      // 确保在快手小程序中也能正确居中
      position: relative;
    }

    .preview-image {
      width: 100%;
      max-height: 80vh;
      // 快手小程序图片居中显示优化
      object-fit: contain;
      // 确保图片在容器中居中
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
    }
  }

  // 底部指示器样式
  .preview-indicator {
    position: absolute;
    right: 44rpx;
    bottom: 56rpx;
    z-index: 1001;
    color: #fff;
    font-size: 30rpx;
    // 确保在快手小程序中底部指示器不被遮挡
    padding-bottom: constant(safe-area-inset-bottom);
    padding-bottom: env(safe-area-inset-bottom);
  }
}

// 新增关闭按钮样式
.preview-close {
  position: absolute;
  left: 14rpx;
  top: 100rpx;
  z-index: 1001;
  width: 40rpx;
  height: 40rpx;
  border-radius: 999px;
  box-sizing: content-box;
  padding: 8rpx;
  background-color: rgba(0,0,0,0.2);
}
