import { TTreeFullVal } from './index.d'

/** 页面默认的地址配置 */
export const addrConfDef = {
  title: '请选择求职区域',
  /** 页面UI显示的最大级数 */
  level: 3,
  /** 最大可选数量 */
  maxNum: 1,
  /** 已选中的地址集合 */
  areas: [],
  /** 是否多选 */
  isMultiple: false,
  /** 需要禁用的地址id数组，禁用的地址无法被选中 */
  disabledIds: [],
  /** 是否显示定位地址 -默认显示 */
  showLocation: true,
  /** 是否隐藏全国的地址选项 */
  hideNation: false,
  /** 显示全地址的逻辑 */
  hasAllType: '',
}

/** 页面地址配置 */
const addrConf = { ...addrConfDef }

/** 设置地址页的配置数据  */
export function getAreaConfig() {
  const routerData = $.router.data
  // Test Data
  // routerData.maxNum = 20
  const isMultiple = routerData.maxNum > 1
  Object.assign(addrConf, {
    /** 是否多选 */
    isMultiple,
    areas: [],
    ...routerData,
  })
  return {
    ...addrConf,
  }
}

/** 根据addrConf配置信息处理地址数据
 * @param tree - 树形数据
 * @param level - 代表第一列第二列还是三列 默认 1
 */
export function handlerData<T extends TTreeFullVal>(tree: T, level: 1 | 2 | 3): T {
  if (!$.isArrayVal(tree)) {
    return [] as any
  }
  const pShowType = ['all', 'province'] // 全省份的显示逻辑
  const cShowType = ['all', 'city'] // 全城市的显示逻辑

  return tree.filter((item) => {
    let bool = true
    switch (level) {
    case 1:
      bool = item.id != 1 || !addrConf.hideNation
      break
    case 2:
      break
    case 3:
    }
    return bool
  })
}
