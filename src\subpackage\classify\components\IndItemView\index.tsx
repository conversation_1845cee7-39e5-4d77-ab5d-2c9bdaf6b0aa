import { Text, View } from '@tarojs/components'
import style from './index.module.scss'

type IIndItemViewProps = {
  /** 行业ID */
  occId: string
  /** 行业名称 */
  name: string
  // 选中的行业ID
  slted?: string
  // 点击方法
  onClick?: (id: string) => void
  [key: string]: any
}

const IndItemView = (props: IIndItemViewProps) => {
  const { id, occId, name, slted, onClick } = props
  return (
    <View id={id} className={`${style.indItem}`} onClick={() => onClick && onClick(occId)}>
      {`${occId}` === `${slted}` ? <View className={style.indSlted}></View> : null}
      <Text className={`${style.indTxt} ${occId === slted ? style.indSltedTxt : ''}`}>{name}</Text>
    </View>
  )
}

export default IndItemView
