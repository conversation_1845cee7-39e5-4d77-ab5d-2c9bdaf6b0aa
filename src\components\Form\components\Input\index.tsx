/*
 * @Date: 2024-10-16 15:32:57
 * @Description: 输入框
 */

import { Input, type InputProps } from '@tarojs/components'
import { type FormItemChildrenProps } from '../../index'

type Props = FormItemChildrenProps & InputProps

export default (props: Props) => {
  const { value, onChange, placeholder = '请输入', ...other } = props
  return (
    <Input
      value={value}
      onInput={(e) => onChange?.(e.detail.value)}
      placeholder={placeholder}
      {...other}
    />
  )
}
