/*
 * @Date: 2024-11-22 14:00:16
 * @Description: header
 */

import { memo } from 'react'
import { View as V, Text as T, Image as Img } from '@tarojs/components'
import IconFont from '@/components/IconFont'
import s from './index.module.scss'
import Click from '@/components/Click'

export default memo(() => {
  const sysInfo = $.sysInfo()

  const onSearch = () => {
    $.report.event('searchInput', {
      keywords: '',
      search_source_id: '1',
      search_source: '职位推荐首页顶部搜索框',
    })
    $.router.push('/subpackage/search/search-page/index')
  }
  return (
    <>
      <V className={s.header} style={{ paddingTop: sysInfo.statusBarHeight, height: sysInfo.headerTop }}>
        <V className={s.headerSearchC} style={{ height: sysInfo.menuRect.height }}>
          <Img className={s.headerImg} src='https://cdn.yupaowang.com/yupao_mini/yp_mini_zpframeName.png' />
        </V>
      </V>
      <Click className={s.headerSearch} onClick={onSearch}>
        <IconFont type='yp-search' color='#00000073' size={32} />
        <T className={s.headerSearchT}>搜索你想要的工作</T>
        <V className={s.headerSearchB}>搜索</V>
      </Click>
    </>
  )
})
