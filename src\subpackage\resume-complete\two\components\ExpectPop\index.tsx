import Popup from '@/components/Popup'
import style from './index.module.scss'
import IconFont from '@/components/IconFont'
import Click from '@/components/Click'

type IExpectPopProps = {
  // 工种数据
  occupations?: Array<any>
  // 期望薪资
  salaryObj?: any
  visible?: boolean
  cancel?: () => void
  confirm?: () => void
}
/** 期望职位 */
const ExpectPop = (props: IExpectPopProps) => {
  const { visible = false, occupations = [], salaryObj = {}, cancel, confirm } = props

  const onCancel = () => {
    cancel && cancel()
  }

  const onConfirm = () => {
    confirm && confirm()
  }
  return (
    <Popup
      visible={visible}
      position="bottom"
      onClose={onCancel}
      zIndex={9990}
      className={style.pop}
    >
      <V className={style.body}>
        <V className={style.head}>
          <V className={style.title}>
            <T>将为你添加{occupations.length}个求职期望</T>
            <IconFont onClick={onCancel} type='yp-close-small' size={48} color='rgba(0, 0, 0, 0.25)' />
          </V>
          <V className={style.desc}>
            <T>可在列表单独进行修改</T>
          </V>
        </V>
        <V className={style.content}>
          {
            occupations.map((item, index) => {
              return (
                <V className={style.item} key={index}>
                  <V className={style.label}><T>{item.name}</T></V>
                  <V><T className={style.salary}>{salaryObj.valFormat || ''}</T></V>
                </V>
              )
            })
          }
        </V>
        <V className={style.btnView}>
          <Click className={style.btn} onClick={onConfirm}>确定</Click>
        </V>
      </V>
    </Popup>
  )
}

export default ExpectPop
