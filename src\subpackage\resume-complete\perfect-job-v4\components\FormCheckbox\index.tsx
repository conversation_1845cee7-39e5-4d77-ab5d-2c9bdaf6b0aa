import { Text, View } from '@tarojs/components'
import { useEffect, useState } from 'react'
import style from './index.module.scss'

type IFormCheckboxProps = {
  control?: any
  mustKey?: string
  change?: (e: any) => void
}
const FormCheckbox = (props: IFormCheckboxProps) => {
  const { control = {}, change, mustKey = '' } = props
  const [isRadio, setIsRadio] = useState(false)
  const [labelList, setLabelList] = useState<Array<any>>([])

  useEffect(() => {
    const { controlAttr } = control || {}
    const { dataObj, labelList } = controlAttr || {}
    const { ifMulti, limitNum } = dataObj || {}
    setIsRadio(!ifMulti || limitNum == 1)
    setLabelList(labelList)
  }, [control])

  const onChange = (index) => {
    const nControl = { ...control }
    const { controlAttr } = nControl || {}
    const { dataObj, labelList } = controlAttr || {}
    const { limitNum } = dataObj || {}
    /** 选项可配置数量 */
    const maxItem = limitNum || 1
    /** 选中的数量 */
    let checkedCount = 0
    const nlabelList = labelList.map((item, i) => {
      const nItem = { ...item }
      if (i === index) { // 当前选中的
        nItem.checked = !nItem.checked
      } else if (isRadio) { // 单选
        nItem.checked = false
      }
      if (nItem.checked) { // 选中的数量
        checkedCount += 1
      }
      return nItem
    })
    if (!isRadio && checkedCount > maxItem) { // 多选
      $.msg(`${nControl.controlName} 最多只能选择${maxItem}个`)
      return
    }
    if (!$.isEmptyObject(nControl) && !$.isEmptyObject(nControl.controlAttr)) {
      nControl.controlAttr.labelList = nlabelList
      setLabelList(nlabelList)
    }
    change && change(nControl)
  }
  return (
    <View className={style.item}>
      <View className={style.head}>
        {control[mustKey] && <Text className={style.must}>*</Text>}
        <View className={style.title}><Text>{control.title}</Text></View>
        <View className={style.desc}><Text>{isRadio ? '' : '【多选】'}</Text></View>
      </View>
      <View className={style.content}>
        {
          (labelList || []).map((label, index) => {
            return (
              <View onClick={() => onChange(index)} className={`${style.gridItem} ${label.checked ? style.checked : ''}`} key={index}>
                <Text className={style.txt}>{label.name}</Text>
              </View>
            )
          })
        }
      </View>
    </View>
  )
}

export default FormCheckbox
