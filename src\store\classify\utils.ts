// 组装行业和行业对应的一级工种数据
export const processOcc = (data: any) => {
  const { occ, industryTree, indOccIdOne, occOneIdTwo, occupationsMap, industryOccMap } = data || {}
  const { id, children } = occ || {}
  if (`${id}` === '-1') return
  industryTree.push(`${id}`)
  indOccIdOne[`${id}`] = []
  const nocc = { ...occ }
  delete nocc.children
  occupationsMap[`${id}`] = nocc
  industryOccMap[`${id}`] = nocc
  if ($.isArrayVal(children)) {
    const occArr: string[] = []
    children.forEach((cocc) => {
      processOneOcc({ cocc, parentId: id, occArr, occOneIdTwo, occupationsMap, industryOccMap })
    })
    indOccIdOne[`${id}`] = occArr
  }
}

// 组装一级和一级对应的二级工种数据
const processOneOcc = (data: any) => {
  const { cocc, parentId, occArr, occOneIdTwo, occupationsMap, industryOccMap } = data || {}
  const { id: cId, children: cChildren, extType } = cocc || {}
  occArr.push(`${cId}`)
  occOneIdTwo[`${parentId}_${cId}`] = []
  const ncocc = { ...cocc }
  delete ncocc.children
  industryOccMap[`${parentId}_${cId}`] = ncocc
  if (`${extType}` !== '3') {
    occupationsMap[`${cId}`] = ncocc
  }
  if ($.isArrayVal(cChildren)) {
    const occTwoArr: string[] = []
    cChildren.forEach((ccc) => {
      processTwoOcc({ ccc, parentId, occTwoArr, occupationsMap, industryOccMap })
    })
    occOneIdTwo[`${parentId}_${cId}`] = occTwoArr
  }
}
// 组装二级工种数据
const processTwoOcc = (data) => {
  const { ccc, parentId, occTwoArr, occupationsMap, industryOccMap } = data || {}
  const { id: cccId } = ccc || {}
  occTwoArr.push(`${cccId}`)
  const nccc = { ...ccc }
  delete nccc.children
  occupationsMap[`${cccId}`] = nccc
  industryOccMap[`${parentId}_${cccId}`] = nccc
}

export const unique = <T>(array: T[], property: string): T[] => {
  const pool: Array<any> = []
  return array.filter((item: any) => {
    if (!pool.includes(item[property])) {
      pool.push(item[property])
      return true
    }
    return false
  })
}

export const treeQuery = <T = any>(tree: T[], properties: { key: string, value: any[], childKey: string }): T[] => {
  const { key, value, childKey } = properties
  return Array.isArray(tree) && tree.length ? tree.reduce((target, current) => {
    if (value.some(sub => `${sub}` === `${current[key]}`)) {
      target.push(current)
    }
    if (Array.isArray(current[childKey]) && current[childKey].length) {
      const next = treeQuery(current[childKey], properties)
      if (next.length) {
        target.push(...next)
      }
    }
    return target
  }, ([] as T[])) : []
}
