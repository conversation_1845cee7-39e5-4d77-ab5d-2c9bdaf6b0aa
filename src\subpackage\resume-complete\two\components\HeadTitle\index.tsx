import style from './index.module.scss'

type IHeadTitleViewProps = {
  /** 类型 1.添加 2.修改 */
  type?: number | string
}

const HeadTitleView = (props: IHeadTitleViewProps) => {
  const { type = '1' } = props
  return (
    <V className={style.item}>
      <V className={style.title}>
        <T>{`${type}` === '2' ? '编辑' : '添加'}求职期望</T>
      </V>
      <V className={style.desc}>
        <T>求职期望的不同，推荐的职位也会不同</T>
      </V>
    </V>
  )
}

export default HeadTitleView
