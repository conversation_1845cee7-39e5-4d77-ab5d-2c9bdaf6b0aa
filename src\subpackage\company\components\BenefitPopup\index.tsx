import React, { JSXElementConstructor, useState } from 'react'
import { ScrollView, Text as T, View as V } from '@tarojs/components'
import styles from './index.module.scss'
import IconFont from '@/components/IconFont'
import { FormatEnterpriseOptionListType } from '../../formatEnterpriseMainView'
import Popup from '@/components/Popup'

type IProps = {
  data: Partial<FormatEnterpriseOptionListType>
  benefitList: FormatEnterpriseOptionListType['insurance']['content']
  children: React.ReactElement<any, string | JSXElementConstructor<any>>
}
/**
 * 福利popup 弹窗
*/
export default (props: IProps) => {
  const { children, benefitList, data } = props
  const [show, setShow] = useState(false)

  const showDetail = () => {
    setShow(true)
  }

  const isShowWorkTime = data.workTime && data.overtimeSituationTime && data.restTime

  return (
    <>
      <V>
        {React.cloneElement(children, { onClick: () => showDetail() })}
      </V>
      <Popup
        visible={show}
        position="bottom"
        onClose={() => setShow(false)}
        catchMove={false}
        disableScroll={false}
      >
        <V className={styles.header}>
          <V className={styles.state} onClick={() => setShow(false)}>
            <IconFont type="yp-close-small" size={48} color="#000000A6" />
          </V>
        </V>
        <ScrollView scrollY className={styles.scrollView}>
          <V className={styles.warp}>
            <V className={styles.line}>
              <V className={styles.title}>工作时间与福利待遇</V>
              <T className={styles.tips}>福利信息由公司提供，可能与实际岗位有所不同。具体岗位与BOSS或HR确认</T>
            </V>
            {
              isShowWorkTime && (
                <V className={styles.line}>
                  <V className={styles.subTitle}>工作时间</V>
                  <V className={styles.workTime}>
                    { data.workTime && <T className={styles.tips}>{data.workTime.content}</T> }
                    { data.restTime && <T className={styles.tips}>{data.restTime.content}</T> }
                    { data.overtimeSituationTime && <T className={styles.tips}>{data.overtimeSituationTime.content}</T> }
                  </V>
                </V>
              )
            }

            {
              !!benefitList.length && (
                <>
                  <V className={styles.subTitle}>员工福利</V>
                  {
                    benefitList.map(item => (
                      <V className={styles.item} key={item.value}>
                        <IconFont type={item.icon} size={56} />
                        <V className={styles.itemText}>
                          <V className={styles.text}>{item.label}</V>
                          { item.description && <T className={styles.textTips}>{item.description}</T> }
                        </V>
                      </V>
                    ))
                  }
                </>
              )
            }
          </V>
        </ScrollView>
      </Popup>
    </>
  )
}
