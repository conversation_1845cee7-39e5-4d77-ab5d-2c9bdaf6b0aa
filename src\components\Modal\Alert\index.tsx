/*
 * @Date: 2024-11-21 09:46:57
 * @Description: 弹窗Alert
 */

import { View } from '@tarojs/components'
import Body from '../Body'
import Click from '@/components/Click'
import s from './index.module.scss'
import useModal from '../hooks'

export default () => {
  const { options, visible, onResolve } = useModal('alert')
  const { title, content, confirmText, zIndex } = options

  return (
    <Body visible={visible} zIndex={zIndex}>
      <View className={s.body}>
        <View className={s.title}>{title}</View>
        <View className={s.content}>{content}</View>
        <View className={s.footer}>
          <Click onClick={onResolve} className={s.sure}>{confirmText}</Click>
        </View>
      </View>
    </Body>
  )
}
