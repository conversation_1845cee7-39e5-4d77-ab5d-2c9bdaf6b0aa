.protocol {
    width: 590px;
    font-size: 26px;
    font-weight: 400;
    color: rgba(0,0,0,.45);
    line-height: 150%;
    display: flex;
    flex-direction: row;
}

.checkedIcon {
    width: 32px;
    height: 32px;
    margin-right: 8px;
}

.primary {
    color: rgba(0,146,255,1);
}

.protocolContainer {
    height: fit-content;
    min-height: 0px;
    padding: 48px 32px;
    border-top-left-radius: 16px;
    border-top-right-radius: 16px;
    width: 750px;
    box-sizing: border-box;
    // position: absolute;
    // bottom: 0 !important;
    background-color: #fff;
    // top:none;
    // transform: translate(-50%,0);
}
.pHeader {
    width: 100%;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    font-size: 38px;
    font-weight: bold;
    line-height: 54px;
    color: rgba(0,0,0,.85);
}

.pTips {
    line-height: 42px;
    font-size: 30px;
    font-weight: 400;
    color:rgba(0,0,0,.65);
}

.certainBtn {
    font-size: 34px;
    font-weight: bold;
    color: #fff;
    line-height: 48px;
    height: 96px;
    background-color: rgb(0, 146, 255);
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
    border-radius: 16px;
    width: 686px;
    margin-top:48px;
}