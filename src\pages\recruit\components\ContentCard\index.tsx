import { View } from '@tarojs/components'
import { useCallback, useEffect, useMemo, useState } from 'react'
import classNames from 'classnames'
import { nextTick } from '@tarojs/taro'
import styles from './index.module.scss'
import { Card } from '../Card'
import ClassifyCard from '../ClassifyCard'

export default (props: any) => {
  const { info = {}, resTags = [] } = props

  const [compact, $compact] = useState<any[]>([])

  const [showViewAll, $showViewAll] = useState(false)

  const [viewAllText, $viewAllText] = useState(false)

  const [showContent, $showContent] = useState(false)

  const classifyLen = info.occShowTags?.length || 0

  useMemo(() => {
    $compact(resTags)
  }, [resTags.length])

  /** 计算内容高度，以渲染查看全部按钮 */
  const contentFn = useCallback(async () => {
    const selector = $.taro.createSelectorQuery()
    const rectHeigh = await new Promise<number>((resolve) => selector.select('#content').boundingClientRect().exec(([res, ...ext]) => {
      res && resolve(res.height)
    }))
    const btnHeight = await new Promise<number>((resolve) => selector.select('#viewItem').boundingClientRect().exec((res) => {
      if (res) {
        // eslint-disable-next-line sonarjs/no-nested-functions
        const viewAll = res.find((item) => item && item.id == 'viewItem')
        resolve((viewAll.height * 15))
      }
    }))

    $showContent(true)
    // console.log(rectHeigh, btnHeight)
    if (rectHeigh > btnHeight) {
      $showViewAll(true)
    }
  }, [])

  const handleViewAllText = () => {
    if (showViewAll) {
      $viewAllText(true)
    }
  }

  useEffect(() => {
    if (info.detail) {
      nextTick(contentFn)
    }
  }, [info.detail])

  return (
    <Card>
      <View className={classNames(styles.container, { [styles.hidden]: !info.detail })}>
        <View className={styles.cardTitle}>
          <View className={styles.title}>职位详情</View>
        </View>
        { classifyLen > 1 && <ClassifyCard info={info} />}
        {classifyLen <= 1 && compact.length && <View className={styles.compact} id="compact-grid" >
          {compact.map((item, key) => <View key={key} className={styles.gridItem} id={`${key}`} style={{ opacity: 1 }} data-name={item.name}>{item.name}</View>)}
        </View>}
        <View className={classNames(styles.content, { [styles.showContent]: showContent })} id="content" style={viewAllText ? { maxHeight: 'none' } : {}}>
          {info.detail || ''}
          <View id="viewItem" style="position: absolute; opacity: 0;">高度计算</View>
          <View className={styles.viewAll} style={{ opacity: showViewAll && !viewAllText ? 1 : 0 }} id="viewAll" onClick={handleViewAllText}>查看全部</View>
        </View>
      </View>
    </Card>
  )
}
