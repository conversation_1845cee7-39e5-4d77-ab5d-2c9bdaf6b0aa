/*
 * @Date: 2024-10-11 23:32:41
 * @Description: 全局
 */

import { View as TaroView, Text as TaroText, Image } from '@tarojs/components'
import Taro from '@tarojs/taro'
import * as utils from './utils'
import * as classify from './utils/classify'
import request from './request'
import router from './router'
import * as config from './config'
import * as validator from './utils/validator'
import { alert, confirm, actionSheet, forceLogin } from '@/components/Modal/core'
import Page from '@/components/Page'
import report from './utils/report'
import { login } from './utils/login'

const taro = Taro
const options = {
  ...utils,
  ...classify,
  taro,
  request,
  router,
  config,
  alert,
  confirm,
  actionSheet,
  forceLogin,
  report,
  login,
  validator,
}

// 如果是字节需要特殊处理
if (process.env.TARO_ENV === 'kwai') {
  ks.$ = options
}

(global as any).$ = options;
(global as any).V = TaroView;
(global as any).T = TaroText;
(global as any).P = Page;
(global as any).Img = Image

// 添加全局类型提示
declare global {
  const $: typeof options
  const V: typeof TaroView
  const T: typeof TaroText
  const Img: typeof Image
  const P: typeof Page
}

export { }
