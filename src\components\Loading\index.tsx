/*
 * @Date: 2023-12-22 11:33:11
 * @Description: loading 组件
 */

import { View } from '@tarojs/components'
import cn from 'classnames'
import s from './index.module.scss'

type Props = {
  type?: 'primary' | 'white'
}

export default (props: Props) => {
  const { type = 'primary' } = props
  return (
    <View className={s.body}>
      <View
        className={cn(s.loading, {
          [s.white]: type === 'white',
          [s.primary]: type === 'primary',
        })}
      />
    </View>
  )
}
