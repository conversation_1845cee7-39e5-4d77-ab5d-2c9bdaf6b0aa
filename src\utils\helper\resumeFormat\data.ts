/*
 * @Author: ji<PERSON><PERSON>
 * @LastEditors: jianglong
 */
/** 头像审核状态 0-未申请 1-未审核 2-审核通过 3-审核失败 业务方只需要关心“未审核”的状态 */
export const avatarStatusMap = {
  0: {
    id: 0,
    text: '',
    tagClass: '',
  },
  1: {
    id: 1,
    text: '审核中',
  },
  2: {
    id: 2,
    text: '',
    tagClass: '',
  },
  3: {
    id: 3,
    text: '',
  },
}

/** 实名状态 0-未认证 1-审核中 2-已通过 -1-不通过 */
export const realNameStatusMap = {
  0: {
    id: 0,
    text: '去实名',
    tagClass: '',
  },
  1: { // 审核中也展示去实名的文案
    id: 1,
    text: '去实名',
    tagClass: '',
  },
  2: {
    id: 2,
    text: '已实名',
    tagClass: '',
  },
  '-1': {
    id: -1,
    text: '去实名',
    tagClass: '',
  },
}

/** 加急服务-默认数据 resume-skills组件使用 */
export const skillsTopInfo = {
  /** 置顶区域 */
  topArea: [],
  /** 置顶结束时间 */
  topEndTime: 0,
  /** 置顶开始时间 */
  topStartTime: 0,
  /** 置顶状态 1未置顶 2置顶中 3置顶过期 4暂停加急 5未预约加急 6已预约加急 */
  topStatus: 1,
  /** 订单id */
  orderId: '0',
}

/** 学历数据 */
export const eduData = [
  { name: '初中以下', value: 1 },
  { name: '中专/技校', value: 2, isEduMajor: true },
  { name: '高中', value: 3 },
  { name: '大专', value: 4, isEduType: true, isEduMajor: true },
  { name: '本科', value: 5, isEduType: true, isEduMajor: true },
  { name: '硕士', value: 6, isEduType: true, isEduMajor: true },
  { name: '博士', value: 7, isEduType: true, isEduMajor: true },
  // { name: 'MBA/EMBA', value: 8, isEduType: true },
]

/** 工作状态 */
export const workStatusObj = {
  1: '离职-随时到岗',
  5: '在职-考虑机会',
  4: '在职-月内到岗',
  2: '在职-暂不考虑',
}
