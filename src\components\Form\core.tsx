/*
 * @Date: 2023-08-07 15:36:08
 * @Description: 表单核心实现
 */

import React, {
  cloneElement,
  createContext,
  useCallback,
  useContext,
  useEffect,
  useState,
  useRef,
  useMemo,
} from 'react'
import Schema from 'async-validator'

const setRulesSymbol = Symbol('setRules')
const valuesListenSymbol = Symbol('valuesListen')

/** 创建form实例，可以给class组件使用 */
function createForm(target?: any, ...names: string[]) {
  /** 表单的数据 */
  const values = {}
  /** 表单验证规则 */
  const rules = {}
  /** 表单验证规则临时值 */
  const rulesValues = {}
  /** 表单监听的数组 */
  const listens: Function[] = []

  /** 获取表单数据 */
  function getFieldsValue(): Record<string, any> {
    return values
  }

  /** 设置表单数据 */
  function setFieldsValue(newValues) {
    Object.assign(values, newValues)
    listens.forEach((item) => item())
    // 如果有传入class对象，传入需要监听的name，并且有指定的name有变化，那么就更新传入的class
    if (target && names?.length) {
      // 判断更新的值是不是包含需要监听的name
      const isUpdate = Object.keys(newValues).find((item) => names.includes(item))
      if (isUpdate) {
        target.forceUpdate()
      }
    }
  }

  function resetFieldsValue() {
    // eslint-disable-next-line no-return-assign
    Object.keys(values).forEach((key) => (values[key] = null))
    listens.forEach((item) => item(values))
    // 如果有传入class对象，传入需要监听的name，并且有指定的name有变化，那么就更新传入的class
    if (target && names?.length) {
      // 判断更新的值是不是包含需要监听的name
      target.forceUpdate()
    }
  }

  /** 触发表单验证 https://www.npmjs.com/package/async-validator */
  function validateFields({ showMsg = true } = {}) {
    return new Schema(rules)
      .validate({ ...values, ...rulesValues })
      .then(() => {
        return getFieldsValue()
      })
      .catch(({ errors }) => {
        // 默认提示
        if (showMsg) {
          console.log(errors)
        }
        return Promise.reject(errors)
      })
  }

  /** 表单值的监听 */
  function valuesListen(fn) {
    listens.push(fn)
    return function unValuesListen() {
      listens.splice(listens.indexOf(fn), 1)
    }
  }

  /** 设置表单验证规则 */
  function setRules(fn) {
    fn(rules, rulesValues)
  }

  return {
    getFieldsValue,
    setFieldsValue,
    validateFields,
    resetFieldsValue,
    [valuesListenSymbol]: valuesListen,
    [setRulesSymbol]: setRules,
  }
}

export type FormStore = ReturnType<typeof createForm>

/** FormContext */
const FormContext = createContext<FormStore>(null as any)
/** FormListContext */
const FormListContext = createContext<string>(null as any)

/** formItem子组件的props */
type FormItemChildrenProps<T = any> = Partial<{
  value: T
  disabled?: boolean
  onChange: (v: T) => void
}>

/** 响应name变化 */
function useWatch(form, name) {
  /** 表单初始值 */
  const [value, setValue] = useState(() => form.getFieldsValue()[name])
  /** 缓存value值 */
  const valueRef = useRef(value)
  valueRef.current = value

  useEffect(() => {
    function updateValue() {
      const newValue = form.getFieldsValue()[name]
      // 值如果有变化就更新
      if (valueRef.current !== newValue) {
        setValue(newValue)
      }
    }
    // 挂载的时候在取一下值是否更新
    updateValue()
    // 监听表单值变化后重新赋值
    return form[valuesListenSymbol](updateValue)
  }, [form, name])

  return value
}

type FormItemProps = {
  children: React.ReactElement<FormItemChildrenProps>
  /** 字段名称 */
  name: string
  /** 表单验证规则 */
  rules?: Array<any>
  /** 在动态表单中的索引 */
  index?: number
  /** 当字段被删除时保留字段值 */
  preserve?: boolean
}

const FormItem = (props: FormItemProps) => {
  const { children, rules, index, name, preserve = false } = props
  /** 获取表单实例 */
  const form = useContext(FormContext)
  /** 获取列表 name */
  const listName = useContext(FormListContext)
  /** 表单初始值 */
  const formValue = useWatch(form, listName || name)
  const value = listName && index ? formValue?.[index]?.[name] : formValue

  useEffect(() => {
    const rulesName = listName ? `${listName}.${index}.${name}` : name
    // 更新规则
    if (rules) {
      form[setRulesSymbol]((formRules, rulesValues) => {
        formRules[rulesName] = rules
        if (listName) {
          rulesValues[rulesName] = value
        }
      })
    }
    return () => {
      // 销毁后删除对应规则
      if (rules) {
        if (!preserve) {
          form[setRulesSymbol]((formRules, rulesValues) => {
            Reflect.deleteProperty(formRules, rulesName)
            if (listName) {
              Reflect.deleteProperty(rulesValues, rulesName)
            }
          })
        }
      }
    }
  }, [rules, name, form, listName, index, value, preserve])

  useEffect(() => {
    return () => {
      if (listName) {
        // 组件销毁的时候把值重置为空
        const formValue = form.getFieldsValue()[listName]
        if (index && formValue?.[index]) {
          if (!preserve) {
            formValue[index][name] = undefined
            form.setFieldsValue({ [listName]: [...formValue] })
          }
        }
      } else {
        // 组件销毁的时候把值重置为空
        // eslint-disable-next-line
        if (!preserve) {
          form.setFieldsValue({ [name]: undefined })
        }
      }
    }
  }, [name, form, listName, index, preserve])

  /** 获取子组件本身的onChange事件 */
  const onPropsChange = useRef(children.props?.onChange)
  onPropsChange.current = children.props?.onChange

  const onChange = useCallback(
    (value) => {
      // 表单更新
      if (listName && index) {
        const formValue = form.getFieldsValue()[listName]
        formValue[index][name] = value
        form.setFieldsValue({ [listName]: [...formValue] })
      } else {
        form.setFieldsValue({ [name]: value })
      }
      onPropsChange.current?.(value)
    },
    [form, name, listName, index],
  )

  if (name === undefined) {
    return children
  }

  return cloneElement(children, {
    value,
    onChange,
  })
}

type FormListChildrenOptions = {
  /** 添加一项 */
  add: Function
  /** 根据索引删除一项 */
  remove: (index) => void
  /** 数据列表，可以用来循环生成动态表单 */
  list: Array<any>
}

type FormListProps = {
  /** 表单中list的name */
  name: string
  children: (options: FormListChildrenOptions) => React.ReactNode
}

/** 动态表单 */
const FormList = (props: FormListProps) => {
  const { children, name } = props
  /** 获取表单实例 */
  const form = useContext(FormContext)
  /** 表单值 */
  const value = useWatch(form, name)
  /** 缓存列表 */
  const [map] = useState(() => new Map())
  /** 缓存唯一key */
  const key = useRef(0)

  const options = useMemo(() => {
    return {
      add: () => {
        const value = form.getFieldsValue()[name]
        form.setFieldsValue({ [name]: [...value, {}] })
      },
      remove: (index) => {
        const value = form.getFieldsValue()[name]
        form.setFieldsValue({ [name]: value.filter((_, i) => i !== index) })
      },
      list: value?.map((item, i) => {
        if (!map.has(item)) {
          // eslint-disable-next-line no-plusplus
          map.set(item, { key: ++key.current })
        }
        return {
          listName: name,
          itemValue: item,
          index: i,
          key: map.get(item).key,
        }
      }),
    }
  }, [value, form, name, map])

  return <FormListContext.Provider value={name}>{children(options)}</FormListContext.Provider>
}

/** 函数组件中使用，获取创建form实例 */
function useForm() {
  /** 创建form实例 */
  const [form] = useState(createForm)
  return form
}

type FormProps = {
  children: React.ReactNode
  form: FormStore
  onChange?: (values: Record<string, any>) => void
}

type FormType = {
  Item: typeof FormItem
  List: typeof FormList
  createForm: typeof createForm
  useForm: typeof useForm
  useWatch: typeof useWatch
}

const Form: React.FC<FormProps> & FormType = (props: FormProps) => {
  const { children, form, onChange } = props

  // 表单值监听
  useEffect(() => {
    const unValuesListen = onChange ? form[valuesListenSymbol](onChange) : undefined
    return () => {
      unValuesListen && unValuesListen()
    }
  }, [form, onChange])

  return <FormContext.Provider value={form}>{children}</FormContext.Provider>
}

// 相当于每一项表单，需要套一下控件
Form.Item = FormItem
// 动态表单
Form.List = FormList
// 给class组件使用，创建form实例，响应字段变化两个功能
Form.createForm = createForm
// 给函数组件使用，创建form实例
Form.useForm = useForm
// 给函数组件使用，响应字段变化
Form.useWatch = useWatch

export { type FormItemChildrenProps }
export default Form
