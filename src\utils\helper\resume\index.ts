import { actions, dispatch, store } from '@/core/store'
import { MyResumeDetails, resumeDetailsDef } from '@/store/storage/defData'
import { getResumeInfoMe } from './utils'

let refreshResumeTime = 0 // 刷新找活名片时间
/** @description 获取找活名片信息 并更新本地数据
 * @param resumeUuid 找活名片的UUID-【虽然是可选,最好每次都带上】 - 如果resumeUuid===true,则会去请求fetchResumeExist接口获取
 * @param deepTime 刷新时间间隔单位分钟
 */
export function getResumeDetails(resumeUuid?: string | true, deepTime = 0): Promise<MyResumeDetails> {
  let resumeUuidDiy: any = ''
  if (resumeUuid !== true) {
    resumeUuidDiy = resumeUuid || $.getObjVal(store.getState().storage.myResumeDetails, 'resumeUuid')
  }
  const now = new Date().getTime()
  if (deepTime && deepTime > 0) { // 指定多少分钟内不重复请求
    const tempTime = Math.abs(refreshResumeTime - now)
    if (tempTime < deepTime * 60 * 1000) {
      refreshResumeTime = now
      const myResumeDetails = $.deepClone(store.getState().storage.myResumeDetails)
      return Promise.resolve(myResumeDetails)
    }
    refreshResumeTime = now
  }
  return new Promise(async (resolve) => {
    const { userInfo } = store.getState().storage
    const { resumeExist } = store.getState().resume
    if (userInfo && userInfo.userId) {
      // 这里的resumeUuid没有值的时候，就会去请求接口获取
      if (!resumeUuidDiy) {
        resumeUuidDiy = (await dispatch(actions.resume.fetchResumeExist(true))).resumeUuid
      }
      const details = await getResumeInfoMe(resumeUuidDiy)
      dispatch(actions.storage.setItem({
        key: 'myResumeDetails',
        value: details,
      }))
      if (details && details.basicResp && details.basicResp.resumeUuid) {
        const { basicResp } = details
        // 简历存在的时候
        dispatch(actions.resume.setState({
          resumeExist: {
            ...resumeExist,
            exist: true,
            workStatus: basicResp.workStatus,
            checkStatus: basicResp.checkStatus,
            resumeId: basicResp.resumeId,
            resumeUuid: basicResp.resumeUuid,
            hideStatus: details.hideStatus,
          },
        }))
      }
      //   .setItem({
      //   key: 'myResumeDetails',
      //   value: details,
      // }))
      resolve({ ...details } as any)
    } else {
      dispatch(actions.storage.removeItem('myResumeDetails'))
      resolve({ ...resumeDetailsDef } as any)
    }
  })
}

/** @description 是否含有找活名片
 * @param isUpdate 是否更新model缓存
 */
export async function fetchResumeExist(isUpdate = false) {
  return dispatch(actions.resume.fetchResumeExist(isUpdate))
}

/** @description 更新和获取名片全局配置接口 */
export async function getGlobalDefaultConfig(isUpdate = false) {
  let { globalConfig = {} } = store.getState().resume
  globalConfig = await dispatch(actions.resume.fetchGlobalConfig(isUpdate))
  return globalConfig
}
