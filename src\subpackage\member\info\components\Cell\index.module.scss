.cell {
  display: flex;
  flex-direction: column;
  position: relative;
  padding: 40px 0;
  &.line {
    @include bottom-line-b();
  }
}

.title {
  flex: 1;
  color: rgba(0, 0, 0, 0.65);
}

.content {
  flex: 1;
  display: flex;
  align-items: center;
  padding-top: 16px;
  position: relative;
}

.left {
  flex: 1;
  font-size: 34px;
  @include ellip();
  &.placeholder {
    color: rgba(0, 0, 0, 0.25);
  }
}

.right {
  display: inline-flex;
  align-items: center;
}
