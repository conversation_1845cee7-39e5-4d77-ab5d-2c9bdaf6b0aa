import { ScrollView, Swiper, SwiperItem, Image as Img, Text as T, View as V } from '@tarojs/components'

import classNames from 'classnames'
import { useEffect, useRef } from 'react'
import Popup from '@/components/Popup'
import styles from './index.module.scss'
import IconFont from '@/components/IconFont'
import useReducer from '@/hooks/useReducer'

type IProps = {
  showIndex: number
  onClose?: () => void
  data: any
}

type InitState = {
  /** 轮播图的位置 */
  swiperIndex: number
  /** 每一项的展开状态 */
  openState: boolean[]
}

export default (props: IProps) => {
  const { showIndex, onClose, data } = props

  const [{ swiperIndex, openState }, dispatch] = useReducer<InitState>({
    swiperIndex: showIndex || 0,
    openState: [],
  })

  const moreShowRef = useRef<boolean[]>([])

  const getDoms = () => {
    const SelectorQuery = $.taro.createSelectorQuery()
    SelectorQuery.selectAll('.innerContent')
      .boundingClientRect()
      .exec(([res]) => {
        if (res) {
          moreShowRef.current = res.map(it => it.height > 40)
        }
      })
  }

  /**
   * 点击展开收起
  */
  const clickOpen = (i: number) => {
    const l = [...openState]
    l[i] = !l[i]
    dispatch({ openState: l })
  }

  useEffect(() => {
    data.length && getDoms()
  }, [data])

  useEffect(() => {
    if (showIndex !== -1) {
      dispatch({ swiperIndex: showIndex })
    }
  }, [showIndex])

  return (
    <Popup visible={showIndex !== -1} onClose={() => onClose?.()} catchMove={false} disableScroll={false}>
      <V className={styles.wrap}>
        <V className={styles.header}>
          <IconFont type='yp-close-small' size={48} color='#000000A6' onClick={() => onClose?.()} />
        </V>
        <Swiper className={styles.swiper} current={swiperIndex} onChange={({ detail }) => dispatch({ swiperIndex: detail.current })} >
          {
            data.map((item, index) => {
              const moreShow = moreShowRef.current[index]
              return (
                <SwiperItem key={item.id} className={styles.swiperItem}>
                  <V className={styles.itemHeight}>
                    <Img className={styles.avatar} src={item.logo} />
                    <V className={styles.swiperTitle}>{item.name}</V>
                    <V style={{ position: 'relative' }}>
                      <ScrollView className={styles.scrollView} scrollY>
                        <V className={classNames(styles.inner, 'innerContent', !openState[index] && styles.overflow)}>
                          {item.description}
                          {
                            moreShow && openState[index] && <V className={styles.moreOpen} onClick={() => clickOpen(index)}>收起</V>
                          }
                        </V>
                      </ScrollView>
                      {
                        moreShow && !openState[index]
                        && <V className={styles.more} onClick={() => clickOpen(index)}>{ openState[index] ? '收起' : '展开' }</V>
                      }
                    </V>
                  </V>
                </SwiperItem>
              )
            })
          }
        </Swiper>
        <V className={styles.docs}>
          {
            data.map((it, i) => (
              <V
                className={classNames(styles.point, i === swiperIndex && styles.active)}
                key={it.id}
                onClick={() => dispatch({ swiperIndex: i })}
              />
            ))
          }
        </V>
      </V>
    </Popup>
  )
}
