/*
 * @Date: 2023-12-26 13:55:06
 * @Description: 本地存储 model，storage 统一管理，驱动视图更新
 */

import { createSlice, PayloadAction } from '@reduxjs/toolkit'
import initState from './state'
import storage from '@/store/storage/storage'

type State = typeof initState
type Key = keyof typeof initState
type Value<K extends Key> = typeof initState[K]

type SetItem<K extends Key> = {
  /** 设置值的key */
  key: Key
  /** 设置值的value */
  value: Value<K>
} | {
  /** 设置值的key */
  key: Key
  immer: (state: State) => void
}

/** 初始化获取本地 storage 数据 */
const getInitialState = () => {
  const state = {}
  // eslint-disable-next-line
  for (const key in initState) {
    if (Object.prototype.hasOwnProperty.call(initState, key)) {
      // 如果是要和modal关联的
      state[key] = storage.getItemSync(key as any) ?? initState[key]
    }
  }
  return state
}

const { reducer, actions, name } = createSlice({
  name: 'storage',
  initialState: getInitialState() as State,
  reducers: {
    /** 设置本地存储 不需要 JSON.stringify */
    setItem<K extends Key>(state, { payload }: PayloadAction<SetItem<K>>) {
      const { key, value, immer } = payload as any
      /** 处理 immer 手动给调用处的情况 */
      if (typeof immer === 'function') {
        immer(state)
      } else {
        state[key] = value
      }
      storage.setItem(key, state[key])
    },
    /** 设置多个本地存储 不需要 JSON.stringify */
    setItems(state, { payload }: PayloadAction<Partial<State>>) {
      Object.assign(state, payload)
      Object.keys(payload).forEach((key: any) => {
        storage.setItem(key, state[key])
      })
    },
    /** 删除本地存储 */
    removeItem(state, { payload }: PayloadAction<Key>) {
      state[payload as any] = initState[payload]
      storage.removeSync(payload as any)
    },
    /** 删除所有本地存储 */
    clear() {
      storage.clear()
      return { ...initState } as State
    },

  },
})

/** 刷新用户信息 */
const refreshUserInfo = () => {
  return async (dispatch: any, getState) => {
    const { token } = getState().storage
    if (!token) return
    const [userInfo, res] = await $.request['POST/account/v1/userBase/getLoginUserInfo']({ token })
    if (userInfo) {
      /** 保存用户信息 */
      dispatch(actions.setItem({ key: 'userInfo', value: userInfo }))
    } else {
      $.msg(res.message)
    }
  }
}

const extendActions = {
  refreshUserInfo,
}
export default {
  name,
  reducer,
  actions: { ...actions, ...extendActions },
}
