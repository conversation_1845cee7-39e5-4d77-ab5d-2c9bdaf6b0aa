import cn from 'classnames'
import { Input } from '@tarojs/components'
import { useState } from 'react'
import { useLoad } from '@tarojs/taro'
import s from './index.module.scss'
import { defParams, editEmail, editWeChat, getParams } from './utils'
import IconFont from '@/components/IconFont'
import ButtonFooter from '@/components/base/ButtonFooter'
import useStates from '@/hooks/useStates'

export default function Index() {
  const [data, setData] = useStates({
    isFocus: false,
    bottomHeight: 0,
    /** 页面传入的内容 */
    contentOld: '',
    loading: false,
    ...defParams,
  })

  /** 输入框输入的内容 */
  const [content, setContent] = useState('')
  /** 输入框默认的内容 */
  const [contentDef, setContentDef] = useState<string>('')

  useLoad(async () => {
    const { content, ...info } = getParams()
    $.taro.setNavigationBarTitle({ title: info.title || ' ' })
    const newData = {
      ...data,
      ...info,
      contentOld: content,
    }
    setContentDef(content)
    setContent(content)
    setData(newData)

    /** 获取焦点 */
    await $.wait(300)
    setData({ ...newData, isFocus: true })
  })

  /** 输入框input事件 */
  const onInput = (e) => {
    setContent(e.detail.value)
  }

  const onClear = () => {
    setContent('')

    setContentDef(' ')
    setTimeout(
      () => setContentDef(''),
      35,
    )
  }

  /** 保存编辑的内容 */
  const onSave = async () => {
    if (!saveBool()) {
      return
    }
    let res
    switch (data.type) {
    case 'infoEmail': /** 修改邮箱 */
      res = await editEmail(content)
      if (res && res.code == 0) {
        onHideKey()
        $.router.event({ content })
        $.router.back()
      }
      break
    case 'infoWechat': /** 修改微信号 */
      res = await editWeChat(content)
      if (res && res.code == 0) {
        onHideKey()
        $.router.event({ content })
        $.router.back()
      }
      break
    default:
      onHideKey()
      $.router.event({ content })
      $.router.back()
      break
    }
  }

  /** 判断输入的内容是否有效 */
  const saveBool = () => {
    if (data.contentOld === content) {
      $.router.back()
      return false
    }

    if (data.maxContent && data.maxContent > -1 && content.length > data.maxContent) {
      $.msg('已超出最大字数限制')
      return false
    }
    return true
  }

  const onFocus = (e) => {
    const newData = {
      ...data,
      isFocus: true,
      bottomHeight: e.detail.height || 0,
    }
    setData(newData)

    if ($.taro.getEnv() === 'ALIPAY') {
      getKeyHeight(newData)
    }
  }

  const getKeyHeight = (newData, loop = 0) => {
    if (loop >= 10) {
      return
    }
    const keyboardH = $.taro.getStorageSync('keyboardH')
    if (!keyboardH || keyboardH < 10) {
      setTimeout(() => {
        getKeyHeight(newData, loop + 1)
      }, 300)
      return
    }
    setData({
      ...newData,
      bottomHeight: keyboardH,
    })
  }

  /** 收起键盘 */
  const onHideKey = () => {
    setData({
      ...data,
      isFocus: false,
      bottomHeight: 0,
    })
  }

  return (<P backgroundColor='#FFF'>
    {/* {$.taro.getEnv() === 'ALIPAY' && <keyboard />} */}
    <V className={s.body}>
      <V className={s.inputBox}>
        <V className={s.inputCont}>
          <Input
            className={s.input}
            placeholder={data.placeholder || '请输入'}
            placeholderClass={s.placeholder}
            value={contentDef}
            adjustPosition={false}
            maxlength={data.maxLength || -1}
            focus
            onFocus={onFocus}
            onBlur={onHideKey}
            onInput={onInput}
          >
          </Input>
        </V>
        <V className={s.inputRight}>
          <V onClick={onClear} className={cn(s.inputClear, content.length < 1 && s.hidden)}>
            <IconFont type="yp-search-shanchu" size={32} color="rgba(0, 0, 0, 0.45)" />
          </V>
        </V>
      </V>
      { (data.type == 'infoWechat' && data.contentDiy)
        && <V className={s.weInfo}>当前微信号：{data.contentDiy}</V>
      }
      { !!data.desc
        && <V className={s.desc}>
          <IconFont type="yp-min_tousu" size={32} />
          <V style="padding-left: 8rpx">{data.desc}</V>
        </V>
      }
    </V>
    <ButtonFooter
      loading={data.loading}
      onClick={onSave}
      bottom={`${data.bottomHeight || 0}px`}
    >保存</ButtonFooter>
  </P>)
}
