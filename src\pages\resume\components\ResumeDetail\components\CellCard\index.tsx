import cn from 'classnames'
import type { StandardProps } from '@tarojs/components'
import s from './index.module.scss'
import IconFont from '@/components/IconFont'

type Props = StandardProps & {
  /** 标题 */
  title?: string,
  /** 标题style */
  titleStyle?: string,
  /** 右边的icon eg: yp-mbxl、yp-add、yp-icon-arrows、yp-icon_mbx_sd、yp-icon_edit_grzl */
  titleIcon?: string,
  /** 是否隐藏底部边线 */
  hideLine?: boolean,
  /** 标题点击事件 */
  onTitle?: () => void,
  /** onDesc */
  onDesc?: () => void,
  /** onIcon */
  onIcon?: () => void,
  /** desc */
  childrenDesc?: StandardProps['children']
}

/** 通用单元格组件 */
export default function Index(props: Props) {
  const boxClass = cn(s.cell, props.className, props.hideLine && s['hide-line'])
  return (
    <V className={boxClass}>
      <V onClick={props.onClick} className={s['title-box']} style={props.titleStyle}>
        <V className={s['title-left']}>
          <V onClick={props.onTitle} className={s.title} >{props.title}</V>
          <V onClick={props.onDesc} className={s['title-desc']} >
            {props.childrenDesc}
          </V>
        </V>
        {props.titleIcon && (
          <V onClick={props.onIcon} className={s['title-icon']}>
            <IconFont type={props.titleIcon} size={40} color="rgba(0,0,0, 0.65)" />
          </V>
        )}
      </V>
      {/* <slot></slot> */}
      {props.children}
    </V>
  )
}
