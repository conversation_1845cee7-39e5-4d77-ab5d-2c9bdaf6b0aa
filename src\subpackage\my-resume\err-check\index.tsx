import { useLoad } from '@tarojs/taro'
import { useState } from 'react'
import { View as V } from '@tarojs/components'
import s from './index.module.scss'

export default function Index() {
  const [content, setContent] = useState('')
  useLoad(() => {
    const { data } = $.router
    setContent(data.content)
  })
  return (
    <V className={s.body}>
      <V className={s.head}>审核失败原因</V>
      <V className={s.content}>{content}</V>
    </V>
  )
}
