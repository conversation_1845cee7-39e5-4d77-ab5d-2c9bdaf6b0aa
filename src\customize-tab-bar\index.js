/*
 * @Date: 2024-12-02 15:35:27
 * @Description: 自定义
 */

export const tabBarList = [
  {
    pagePath: 'pages/index/index',
    path: '/pages/index/index',
    text: '找工作',
    icon: '/customize-tab-bar/images/home.png',
    activeIcon: '/customize-tab-bar/images/home-active.png',
  },
  {
    pagePath: 'pages/resume/index',
    path: '/pages/resume/index',
    text: '简历',
    icon: '/customize-tab-bar/images/resume.png',
    activeIcon: '/customize-tab-bar/images/resume-active.png',
  },
  {
    pagePath: 'pages/mine/index',
    path: '/pages/mine/index',
    text: '我的',
    icon: '/customize-tab-bar/images/mine.png',
    activeIcon: '/customize-tab-bar/images/mine-active.png',
  },
]

Component({
  data: {
    selected: 0,
    list: tabBarList,
  },
  didMount() {
    const path = $.router.getCurrentPage().route
    this.setData({
      selected: tabBarList.findIndex(item => item.pagePath === path)
    })
  },
  methods: {
    tap(e) {
      $.taro.switchTab({
        url: e.target.dataset.value.path,
      });
    },
  },
})