import IconFont from '@/components/IconFont'
import s from './index.module.scss'

type PropsType = {
  dataSource: any
}
/** 个人信息卡片 */
export default function Index(props: PropsType) {
  const dataSource = props.dataSource || {}
  const onClick = () => {
    $.router.push('/subpackage/member/info/index')
  }
  return (
    <V className={s.content} onClick={() => onClick()}>
      <V className={s['user-cont']}>
        <V className={s['user-left']}>
          <V className={s['user-name']}>
            <V className={s['user-text']}>{dataSource.nameFormat}</V>
            {dataSource.realNameStatus == 2
              && <V className={s['user-tag']}>
                <V className={s['m-tag']}>已实名</V>
              </V>
            }
            <V className={s['user-edit-icon']}>
              <IconFont type="yp-icon_edit_grzl" size={40} />
            </V>
          </V>
          {/* 男 · 31岁 · 乌孜别克族 · 初中及以下 · 22年经验 */}
          <V className={s['user-desc']}>{dataSource.infoFormat}</V>
        </V>
        <V className={s['user-right']}>
          <Img className={s['user-avatar']} mode="aspectFill" src={dataSource.avatar} />
          {dataSource.avatarAuditText
            && <V className={s['user-avatar-text']}>
              { dataSource.avatarAuditText }
            </V>
          }
        </V>
      </V>
      <V className={s['user-footer']}>
        {dataSource.tel
          && <V className={s['footer-cont']}>
            <IconFont type="yp-tel" size={32} />
            <view className={s['cont-text']}>{dataSource.tel}</view>
          </V>
        }
        {dataSource.wechatNumber
          && <V className={s['footer-cont']}>
            <IconFont type="yp-wechat" size={32} />
            <V className={s['cont-text']}>{ dataSource.wechatNumber }</V>
          </V>
        }
      </V>
    </V>

  )
}
