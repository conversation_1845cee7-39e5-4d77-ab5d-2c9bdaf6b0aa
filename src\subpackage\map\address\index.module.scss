.page {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #FFF;
}

.section {
  display: flex;
  flex-direction: column;
  flex: 1;
  background-color: #FFF;
  overflow: hidden;
  min-height: 0;
  height: 100%;
}

.content {
  flex: 1;
  overflow: hidden;
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  min-height: 0;
  height: 100%;
  &.two-column {
    grid-template-columns: repeat(2, 1fr);
  }
}

.footer {
  position: relative;
  padding: 24px;
  border-radius: 24px 24px 0px 0px;
  box-shadow: 0px -4px 8px rgba(50, 52, 60, 0.102);
  overflow: hidden;

  @include safe-area(24px);

  .footer-head {
    @include flexRCC();
    padding-bottom: 32px;
  }
  .footer-title {
    flex: 1;
    font-weight: bold;
    font-size: 32px;
  }
  .footer-count {
    color: rgba(0, 0, 0, 0.45);
  }
  .footer-select {
    padding-bottom: 32px;
    display: flex;
    flex-wrap: wrap;
    margin: -8px;
  }
  .footer-item {
    margin: 8px;
    font-size: 26px;
    padding: 10px 16px;
    border-radius: 8px;
    color: $primary-color;
    background-color: #E0F3FF;
  }


  .footer-btn {
    display: flex;
    align-items: center;
  }

  .btn {
    @include btn-primary();
    color: rgba(0, 0, 0, 0.65);
    background-color: #F5F7FC;
    margin-right: 20px !important;
    width: 220px !important;
  }
  .btn-confirm {
    flex: 1;
    height: 96px;
    color: #FFF;
    background-color: $primary-color;
  }
}

