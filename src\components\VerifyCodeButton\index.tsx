import { View } from '@tarojs/components'
import { memo, useCallback, useState } from 'react'
import styles from './index.module.scss'

export default memo(({ tel, setVerifyToken }: VerifyCodeButtonProps) => {
/** 验证码是否请求中 */
  const [pending, $pending] = useState<boolean>(false)

  const [count, $count] = useState<number>(0)

  const queryVerifyCode = useCallback(() => {
    if (pending) {
      return
    }
    if (!/1\d{10}$/.test(String(tel))) {
      $.msg('请输入正确的手机号')
      return
    }
    $pending(true)
    const promise = $.request['POST/reach/v1/verifyCode/loginIgnore/send']({ tel, biz: 'login' })
    promise.then(([data, res]) => {
      $pending(false)
      if (data && (res.code === 0)) {
        if (data.remainingSendTimes == 2) {
          $.msg('今日获取验收码将达上限，请谨慎操作')
        }
        $count(data.sendInterval)
        setVerifyToken(data.verifyToken)
        downCounter(data.sendInterval, $count)
        return
      }
      $.msg(res.message)
    }).catch(([_, res]) => {
      $pending(false)
      if (res.message) {
        $.msg(res.message)
        return
      }
      $.msg('网络繁忙，请稍后再试~')
    })
  }, [pending, setVerifyToken, tel])

  return <View className={styles.verifyCodeContainer}>
    {!pending && count <= 0 && <View className={styles.verifyCodeBtn} onClick={queryVerifyCode}>获取验证码</View>}
    {pending && <View className={styles.pending}>获取验证码中...</View>}
    {count > 0 && <View className={styles.counter}>{count}s后重新获取</View>}
  </View>
})

const downCounter = (count: number, callback: (count: number) => void) => {
  if (count > 0) {
    setTimeout(() => {
      const newCount = count - 1
      callback(newCount)
      downCounter(newCount, callback)
    }, 1000)
  }
}

type VerifyCodeButtonProps = {
  tel: string | undefined,
  setVerifyToken: (token: string) => void
}
