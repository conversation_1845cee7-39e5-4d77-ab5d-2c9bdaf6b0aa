import dayjs from 'dayjs'

/** 只得到时间年月 */
export function getDataMonth(dateTime: string) {
  if (dateTime && dateTime.length > 8) {
    return dayjs(dateTime).format('YYYY-MM')
  }
  return dateTime
}

/** 字符串前3个和末尾2个字符显示，中间用*代替 */
export function getWeChatDiy(str) {
  if (!str) {
    return ''
  }
  const len = str.length
  return str.slice(0, 3) + '*'.repeat(len - 5) + str.slice(-2)
}

/** 如果当前时间大于或者小于最大最小值时返回对应的大小值 */
export function getTimeVal(time: string, timeMin: string, timeMax: string) {
  const nTime = dayjs(time).valueOf()
  const nTimeMin = dayjs(timeMin).valueOf()
  const nTimeMax = dayjs(timeMax).valueOf()
  if (nTime < nTimeMin) {
    return timeMin
  }
  if (nTime > nTimeMax) {
    return timeMax
  }
  if (time && time.length < 8) {
    return `${time}-01`
  }
  return time
}
