.body {
  display: flex;
  justify-content: center;
}

.loading {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  transform: translateZ(0);
  animation: load-anim 1.1s infinite linear;
}

.primary {
  border-top: 6px solid rgba(0, 146, 255, 0.3);
  border-right: 6px solid rgba(0, 146, 255, 0.3);
  border-bottom: 6px solid rgba(0, 146, 255, 0.3);
  border-left: 6px solid rgba(0, 146, 255, 0.8);
}

.white {
  border-top: 6px solid rgba(255, 255, 255, 0.3);
  border-right: 6px solid rgba(255, 255, 255, 0.3);
  border-bottom: 6px solid rgba(255, 255, 255, 0.3);
  border-left: 6px solid rgba(0, 146, 255, 0.8);
}

@keyframes load-anim {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}
