import cn from 'classnames'
import { Input } from '@tarojs/components'
import { useState } from 'react'
import { useLoad } from '@tarojs/taro'
import s from './index.module.scss'
import IconFont from '@/components/IconFont'
import ButtonFooter from '@/components/base/ButtonFooter'
import { actions, dispatch, getState } from '@/core/store'
import useStates from '@/hooks/useStates'

export default function Index() {
  const [data, setData] = useState({
    isFocus: false,
    bottomHeight: 0,
    /** 最大输入长度 */
    maxContent: 5,
    /** 页面传入的内容 */
    contentOld: '',
    loading: false,
  })

  /** 输入框输入的内容 */
  const [content, setContent] = useState('')
  /** 输入框默认的内容 */
  const [contentDef, setContentDef] = useState<string>('')

  const [nameRule, setNameRule] = useStates({
    /** 实名之后的列表信息 */
    options: [] as Models['POST/account/v1/userBase/showNameRule']['Res']['data']['showRulesList'],
    /** 姓名的code */
    code: 0,
    /** 是否禁用 */
    disabled: false,
  })

  useLoad(async () => {
    const { userNameResp, userRealNameResp } = getState().user.userData.userCommonInfo || {}
    let { username } = userNameResp || {}
    username = username || ''
    const newData = {
      ...data,
      contentOld: username,
    }
    setContentDef(username)
    setContent(username)
    setData(newData)

    if (userRealNameResp.realNameStatus == 2) {
      const [data] = await $.request['POST/account/v1/userBase/showNameRule']()
      const selectedOption = data.showRulesList.find(option => option.desc === username)
      let code = 0
      if (selectedOption) {
        code = selectedOption.code || 0
      }
      setNameRule({
        code,
        disabled: true,
        options: data.showRulesList,
      })
    } else {
      // 在未实名通过时可以自动获取焦点
      await $.wait(300)
      setData({
        ...newData,
        isFocus: true,
      })
    }
  })

  /** 输入框input事件 */
  const onInput = (e) => {
    setContent(e.detail.value)
  }

  const onClear = () => {
    setContent('')

    setContentDef(' ')
    setTimeout(
      () => setContentDef(''),
      35,
    )
  }

  /** 保存编辑的内容 */
  const onSave = async () => {
    const { realNameStatus } = getState().user.userData.userRealNameObj
    if (!saveBool()) {
      return
    }
    $.showLoading('设置中...')
    let res: any
    if (realNameStatus != 2) {
      [, res] = await $.request['POST/account/v1/userBase/changeUsername']({
        content,
      }).catch((err) => err)
    } else {
      [, res] = await $.request['POST/account/v1/userBase/updateShowName']({
        code: nameRule.code,
      }).catch((err) => err)
    }

    $.hideLoading()
    if (!res || res.code != 0) {
      const errMsg = res.message || '修改失败'
      $.msg(errMsg)
      this.setData({ err: errMsg })
      return
    }
    onHideKey()
    dispatch(actions.user.fetchUserData())
    $.router.back()
  }

  const onClick = ({ desc, code }: typeof nameRule.options[0]) => {
    setContent(desc)
    setContentDef(desc)
    setNameRule({
      ...nameRule,
      code,
    })
  }

  /** 判断输入的内容是否有效 */
  const saveBool = () => {
    const { maxContent, contentOld } = data

    if (content === contentOld) {
      $.router.back()
      return false
    }
    const errNames = ['先生', '女士']
    const tempName = `${content}`.trim()
    if (errNames.includes(tempName)) {
      $.msg(`新姓名不能是“${tempName}”`)
      return false
    }
    if (content.length > maxContent) {
      $.msg('已超出最大字数限制')
      return false
    }
    if (!$.validator.allChinese(content)) {
      $.msg('请输入2-5中文汉字姓名')
      return false
    }
    return true
  }

  const onFocus = (e) => {
    const newData = {
      ...data,
      isFocus: true,
      bottomHeight: e.detail.height || 0,
    }
    setData(newData)

    if ($.taro.getEnv() === 'ALIPAY') {
      getKeyHeight(newData)
    }
  }

  const getKeyHeight = (newData, loop = 0) => {
    if (loop >= 10) {
      return
    }
    const keyboardH = $.taro.getStorageSync('keyboardH')
    if (!keyboardH || keyboardH < 10) {
      setTimeout(() => {
        getKeyHeight(newData, loop + 1)
      }, 300)
      return
    }
    setData({
      ...newData,
      bottomHeight: keyboardH,
    })
  }

  /** 收起键盘 */
  const onHideKey = () => {
    setData({
      ...data,
      isFocus: false,
      bottomHeight: 0,
    })
  }

  return (<P backgroundColor='#FFF'>
    <V className={s.body}>
      <V className={s.inputBox}>
        <V className={s.inputCont}>
          <Input
            className={cn(s.input, nameRule.disabled && s.disabled)}
            disabled={nameRule.disabled}
            placeholder="请输入"
            placeholderClass={s.placeholder}
            value={contentDef}
            adjustPosition={false}
            maxlength={-1}
            focus={data.isFocus}
            onFocus={onFocus}
            onBlur={onHideKey}
            onInput={onInput}
          >
          </Input>
        </V>
        {!nameRule.disabled
          && <V className={s.inputRight}>
            <V onClick={onClear} className={cn(s.inputClear, content.length < 1 && s.hidden)}>
              <IconFont type="yp-search-shanchu" size={32} color="rgba(0, 0, 0, 0.45)" />
            </V>
            <V className={s.infoNum}>
              <V className={cn(content.length > data.maxContent ? s.numErr : s.num, content.length < 1 && s.numGray)}>
                {content.length || 0}
              </V>
              <V className={s.numGray}>/{data.maxContent}</V>
            </V>
          </V>
        }
      </V>

      {nameRule.disabled
        && <V>
          <V className={s.nameView}>对外显示名称</V>
          <V className={s.radio}>
            {nameRule.options.map(item => (
              <V key={item.code}
                className={cn(s.item, nameRule.code === item.code ? s.active : '')}
                onClick={() => onClick(item)}
              >
                <V className={s.label}>{item.desc}</V>
                {item.code == nameRule.code
                  ? <IconFont type="yp-icon_pay_y" size={48} color="#0092FF" />
                  : <IconFont type="yp-icon_pay_n" size={48} color="#C6CAD3" />
                }
              </V>
            ))}
          </V>
        </V>
      }
    </V>
    <ButtonFooter
      loading={data.loading}
      onClick={onSave}
      bottom={`${data.bottomHeight || 0}px`}
    >保存</ButtonFooter>
  </P>)
}
