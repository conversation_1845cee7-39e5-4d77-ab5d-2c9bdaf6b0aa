import { Text } from '@tarojs/components'
import Taro from '@tarojs/taro'
import IIconFont from './type'
import ifstyle from './index.module.scss'

const IconFont = (props: IIconFont) => {
  let { type, size = 28, color, rotate, style, className, id, onClick } = props
  let fontSize
  const svgStyle = rotate
    ? {
      msTransform: `rotate(${rotate}deg)`,
      transform: `rotate(${rotate}deg)`,
    }
    : undefined
  switch (size) {
  case 'large':
    fontSize = Taro.pxTransform(72)
    break
  case 'middle':
    fontSize = Taro.pxTransform(48)
    break
  case 'small':
    fontSize = Taro.pxTransform(24)
    break
  default:
    fontSize = Taro.pxTransform(size)
    break
  }
  style = { ...style, ...svgStyle, fontSize, color }
  return (
    <Text
      className={`${ifstyle.iconfont} ${ifstyle[type]} ${className || ''}`}
      {...{ id, style, onClick }}
    />
  )
}

export default IconFont
