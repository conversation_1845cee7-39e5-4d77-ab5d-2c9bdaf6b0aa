/** 树数据结构 */
export type TTreeVal = ILocation.TAreaData & {
  /** pid的上级id, 只会在第三级地址添加这个字段(祖级id) */
  gid?: number | string,
  /** 是否选中 */
  checked?: boolean,
  /** 是否是全地址 */
  isFull?: boolean,
}

/** 树数据集合 */
export type TTreeFullVal = TTreeVal[]

export type TSelectAddress = {
  /** 一级地址 */
  oneArea: TTreeFullVal
  /** 二级地址 */
  twoArea: TTreeFullVal
  /** 三级地址 */
  threeArea: TTreeFullVal
  /** 是否是末级地址 */
  isEnd: boolean
}

export type TAddrTreeData = {
  /** 一级地址 */
  oneArea: TTreeFullVal
  /** 二级地址 */
  twoArea: TTreeFullVal
  /** 三级地址 */
  threeArea: TTreeFullVal
}

/** state数据类型 */
export type TTreeState = {
  /** 一级地址 */
  oneArea: TTreeFullVal
  /** 二级地址 */
  twoArea: TTreeFullVal
  /** 三级地址 */
  threeArea: TTreeFullVal
  /** 已选中的地址 */
  selectAddrs: TTreeFullVal
}
