/*
 * @Date: 2024-12-16 14:00:59
 * @Description: 环境切换
 */

import { useState } from 'react'
import { Switch } from '@tarojs/components'
import Page from '@/components/Page'
import s from './index.module.scss'
import { actions, dispatch, useSelector } from '@/core/store'

export default () => {
  const list = Object.keys($.config.ENV_LIST)

  const [env, setEnv] = useState(() => {
    return $.taro.getStorageSync('DEV_TARO_APP_ENV') || process.env.TARO_APP_ENV
  })

  const onChange = (v) => {
    setEnv(v)
    $.taro.setStorageSync('DEV_TARO_APP_ENV', v)
    $.msg('环境切换成功，请退出后重新进入')
  }

  const buryDebug = useSelector((state) => state.storage.buryDebug)

  const onCheck = () => {
    dispatch(actions.storage.setItem({ key: 'buryDebug', value: !buryDebug }))
  }

  return (
    <Page>
      <V className={s.body}>
        <V key={-1} className={s.item}>
          <V>埋点日志</V>
          <V className={`${s.option} ${s.row}`}>
            <Switch checked={buryDebug} onChange={onCheck} />
          </V>
        </V>
        {list.map((item) => {
          return (
            <V key={item} className={s.item}>
              <V>{$.config.ENV_LIST[item]}</V>
              <Switch color='0092ff' checked={env === item} onChange={() => onChange(item)} />
            </V>
          )
        })}
      </V>
    </Page>
  )
}
