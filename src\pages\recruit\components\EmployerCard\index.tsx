import { View, Image, Text } from '@tarojs/components'
import classNames from 'classnames'
import { useMemo } from 'react'
import { Card } from '../Card'
import styles from './index.module.scss'

export default (props: any) => {
  const { info = {}, companyInfo = {}, applied, certApply } = props

  const replyInfo = $.getObjVal(info, 'replyInfo', {} as any)

  const showActiveDate = useMemo(
    () => !replyInfo.time && !replyInfo.count,
    [replyInfo],
  )

  const companyName = info.companyName || companyInfo.name

  return (
    <Card>
      <View className={styles.employerInfo}>
        <View className={styles.avatarBox}>
          <Image
            mode="aspectFill"
            src={info.avatarUrl}
            className={styles.avatar}
          />
          <View
            className={classNames(styles.active, {
              [styles.hidden]: !info.isActive,
            })}
          />
        </View>
        <View className={styles.textRow}>
          <View className={styles.nameRow}>
            <View className={styles.name}>{info.userName}</View>
            <View className={classNames(styles.subInfo)}>
              <Text className={classNames({ [styles.hidden]: !replyInfo.time })}>{replyInfo.time}</Text>
              <Text className={classNames({ [styles.hidden]: !replyInfo.time || !replyInfo.count })}>|</Text>
              <Text className={classNames({ [styles.hidden]: !replyInfo.count })}>{replyInfo.count}</Text>
              <Text className={classNames({ [styles.hidden]: !showActiveDate })}>{info.activeDate || ''}</Text>
            </View>
          </View>
          <View
            className={classNames(styles.companyName, {
              [styles.hidden]: !companyName || info.checkDegreeStatus != 2,
            })}
          >
            {companyName}
          </View>
        </View>

        {/* <View
          className={!applied ? styles.btn : styles.btnB}
          onClick={() => {
            certApply({ click_entry: 15 })
          }}
        >
          {!applied ? '投递简历' : '已投递'}
        </View> */}
      </View>
    </Card>
  )
}
