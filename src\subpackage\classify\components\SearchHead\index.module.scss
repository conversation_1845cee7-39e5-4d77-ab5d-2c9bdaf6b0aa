.searchView {
  position: relative;
}

.searchHead {
  border-bottom: 1rpx solid rgba(233, 237, 243, 1);
}

.searchInputView {
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 8rpx 24rpx 24rpx;
  padding: 15rpx 24rpx;
  border-radius: 16rpx;
  background: rgba(245, 247, 252, 1);
}

.searchInput {
  width: 606rpx;
  margin-left: 16rpx;
  height: 42rpx !important;
  font-size: 30rpx;
  background: rgba(245, 247, 252, 1);
}

.placeholder {
  color: rgba(0, 0, 0, 0.25);
  font-size: 30rpx;
}

.closeIcon {
  margin-left: 24rpx;
}

.searchContent {
  position: fixed;
  left: 0;
  background: rgba(255, 255, 255, 1);
  width: 100vw;
  z-index: 100;
}

.searchLabelView {
  padding: 0 24rpx;
  padding-bottom: constant(safe-area-inset-bottom); /* 兼容 IOS<11.2 */
  padding-bottom: env(safe-area-inset-bottom); /* 兼容 IOS>11.2 */
}

.searchLabel {
  padding: 34rpx 0;
  border-bottom: 1rpx solid rgba(233, 237, 243, 1);
}

.searchHtml {
  color: rgba(0, 0, 0, 0.85);
  font-size: 30rpx;
  line-height: 42rpx;
}

.labelTxt {
  color: rgba(0, 0, 0, 0.45);
  font-size: 26rpx;
  margin-top: 8rpx;
  line-height: 36rpx;
}
