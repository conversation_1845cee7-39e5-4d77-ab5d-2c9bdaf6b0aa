import Taro from '@tarojs/taro'
import { handleDataList } from '@/core/utils/publish'

/** 获取底部的高度 */
export const getBtmHeight = async (setBtmHeight) => {
  const query = Taro.createSelectorQuery()
  query.select('#fotter') // 获取组件的 UID
    .boundingClientRect()
    .exec((rect) => {
      if ($.isArrayVal(rect)) {
        const { height } = rect[0] || {}
        setBtmHeight(height)
      }
    })
}

// eslint-disable-next-line sonarjs/cognitive-complexity
export const getShowTemplate = (query, template, preferenceList) => {
  const { positionType } = query || {}
  const { templateInfo } = template || {}

  const { controlInfoList } = templateInfo || {}
  if (!$.isArrayVal(controlInfoList)) {
    return {}
  }
  const controlObj: any = {}
  const controlList = controlInfoList.map(ct => {
    if (ct && ct.status != 1) {
      return null
    }
    const { nct } = handleControl(ct, preferenceList, positionType) || {}
    console.log('nct---:', nct)

    const { controlCode, controlAttr } = nct || {}
    if (controlCode) {
      controlObj[controlCode] = nct
    }
    const res: any = { code: controlCode, cObj: {} }
    const { labelList } = controlAttr || {}
    if ($.isArrayVal(labelList)) {
      controlAttr.labelList.forEach((lb) => {
        if ($.isArrayVal(lb.controlConfigList)) {
          const { code: pcode } = lb || {}
          lb.controlConfigList = lb.controlConfigList.filter(cc => cc && cc.status == 1).map((cc) => {
            const ccItem = handleControl(cc, preferenceList, positionType)
            console.log('ccItem:', ccItem)

            const { nct: cnct } = ccItem || {}
            const { controlCode: cControlCode } = cnct || {}
            if (cControlCode) {
              controlObj[cControlCode] = cnct
            }
            if (res.cObj[pcode]) {
              res.cObj[pcode].push(cControlCode)
            } else {
              res.cObj[pcode] = [cControlCode]
            }
            return ccItem
          })
        }
      })
    }
    if (controlCode) {
      return res
    }
    return null
  }).filter(it => it)
  return { controlObj, controlList }
}

// eslint-disable-next-line sonarjs/cognitive-complexity
export const handleControl = (ct, preferenceList, positionType) => {
  const { controlNatureList } = ct || {}
  const controlNature = controlNatureList.find(cn => (positionType == 1 && cn.controlNatureCode == 'FULL_TIME') || (positionType == 2 && cn.controlNatureCode == 'PART_TIME'))
  const { controlAttrList } = controlNature || {}
  if (ct.ifStandard && $.isArrayVal(controlAttrList)) {
    const { labelList, dataList } = controlAttrList[0] || {}
    const nct = { ...ct, controlAttr: {} }
    const { controlAttr } = nct || {}
    if ($.isArrayVal(preferenceList)) {
      const item = preferenceList.find(pf => pf.controlCode == ct.controlCode)
      if (item) {
        if (['INPUT_STRING', 'INPUT_NUMBER', 'INPUT_WITH_UNIT'].includes(item.controlTypeCode)) {
          nct.inputValue = item.controlValues
        } else if ($.isArrayVal(labelList)) {
          const codeArr = item.controlValues.split(',')
          const nLabelList = labelList.map((lb: any) => {
            const nlb = { ...lb }
            nlb.checked = codeArr.includes(lb.code)
            return nlb
          })
          controlAttr.labelList = nLabelList
        }
      }
    }
    if (!$.isArrayVal(controlAttr.labelList)) {
      controlAttr.labelList = labelList
    }
    const { scenes } = ct || {}
    const scene = ($.isArrayVal(scenes) ? scenes : []).find((s) => s.displayScene == 'RESUME_EDIT_PREFERENCE_DISPLAY')
    console.log('scene:', scene)

    const { ifMust } = scene || {}
    nct.ifMust = ifMust
    console.log('nct:', nct)
    controlAttr.dataObj = handleDataList(dataList)
    nct.controlAttr = controlAttr
    return { nct }
  }
  return null
}

// 对显示的数据根据模板返回数据进行排序
export const sortTemPrefDataList = (query, template, preferenceList) => {
  const { controlList } = getShowTemplate(query, template, [])
  if ($.isArrayVal(controlList) && $.isArrayVal(preferenceList)) {
    const nPreferenceList:Array<any> = []
    controlList.forEach((ct) => {
      const { code, cObj } = ct || {}
      const item:any = preferenceList.find((item) => item.controlCode == code)
      if (item) {
        nPreferenceList.push(item)
      }
      if (!$.isEmptyObject(cObj)) {
        const coArr = Object.values(cObj).flat()
        coArr.forEach((coCode) => {
          const coItem:any = preferenceList.find((item) => item.controlCode == coCode)
          if (coItem) {
            nPreferenceList.push(coItem)
          }
        })
      }
    })
    if ($.isArrayVal(nPreferenceList)) {
      return nPreferenceList
    }
  }
  return preferenceList
}
