.body {
  width: 100vw;
  height: 100vh;
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  top: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
}

.switchLoad {
  width: 72rpx;
  height: 72rpx;
  border-radius: 100%;
  display: inline-flex;
  // css动画中心旋转，无限旋转
  animation: rotate 1s linear infinite;
}

.text {
  padding-top: 20rpx;
  text-align: center;
}


@keyframes rotate {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}