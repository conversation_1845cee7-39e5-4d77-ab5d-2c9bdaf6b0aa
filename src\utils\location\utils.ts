import storage from '@/store/storage/storage'

/** 处理对象 */
export function setAreaInfo(areaInfo, city) {
  const $areaInfo = { ...areaInfo }
  if (city.level == 1) {
    $areaInfo.province = city
  } else if (city.level == 2) {
    $areaInfo.city = city
  } else if (city.level == 3) {
    $areaInfo.district = city
  }
  return $areaInfo
}

/** 删除地址的children，会改变areaInfo源对象 */
export function handlerChildren(areaInfo) {
  if (areaInfo.province) {
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const { children, ...treeData } = areaInfo.province
    // eslint-disable-next-line no-param-reassign
    areaInfo.province = treeData
  }
  if (areaInfo.city) {
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const { children, ...treeData } = areaInfo.city
    // eslint-disable-next-line no-param-reassign
    areaInfo.city = treeData
  }
  if (areaInfo.district) {
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const { children, ...treeData } = areaInfo.district
    // eslint-disable-next-line no-param-reassign
    areaInfo.district = treeData
  }
  if (areaInfo.current) {
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const { children, ...treeData } = areaInfo.current
    // eslint-disable-next-line no-param-reassign
    areaInfo.current = treeData
  }
  return areaInfo
}

/** 保存地址到本地缓存 */
export function saveAreaStorage(areaTree: ILocation.TAreaData[]) {
  /** 将地址存储到本地缓存 分成三份 */
  storage.setItem('areaTreeA', areaTree.slice(0, 11))
  storage.setItem('areaTreeB', areaTree.slice(11, 22))
  storage.setItem('areaTreeC', areaTree.slice(22))
}

/** 获取本地缓存地址 */
export function getAreaStorage(): ILocation.TAreaData[] {
  const areaTreeA = storage.getItemSync('areaTreeA') || []

  if ($.isArrayVal(areaTreeA, 9)) {
    const areaTreeB = storage.getItemSync('areaTreeB') || []
    const areaTreeC = storage.getItemSync('areaTreeC') || []
    if (!$.isArrayVal(areaTreeB, 9)) {
      return []
    }
    if (!$.isArrayVal(areaTreeC, 9)) {
      return []
    }
    return [...areaTreeA, ...areaTreeB, ...areaTreeC]
  }
  return []
}

/** 判断是否为对象, 并且对象是否有值 */
export function isObjVal(obj, len = 1) {
  return $.typeOf(obj) === 'object' && $.isArrayVal(Object.keys(obj), len)
}

export const DIRECT_CITY_IDS = [2, 25, 27, 32, 33, 34, 35]
