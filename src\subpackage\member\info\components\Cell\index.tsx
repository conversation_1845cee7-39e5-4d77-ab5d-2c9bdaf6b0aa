import { StandardProps } from '@tarojs/components'
import cn from 'classnames'
import s from './index.module.scss'
import IconFont from '@/components/IconFont'

type Props = StandardProps & {
  title?: string
  value?: string
  placeholder?: string
  /** 右边icon箭头-默认yp-mbxl */
  icon?: string | false
  /** 右侧内容 */
  right?: StandardProps['children'] | string
  /** 是否有边框 */
  isLine?: boolean
}
export default function Index(props: Props) {
  const { right, title, icon = 'yp-mbxl', isLine = true } = props
  return (<V className={cn(s.cell, isLine && s.line)}>
    {title && <V className={s.title}>{title}</V>}
    <V className={s.content} onClick={props.onClick}>
      <V className={cn(s.left, !props.value && s.placeholder)}>{props.value || props.placeholder}</V>
      <V className={s.right}>
        {right}
        {icon && <IconFont type={icon} size={32} color="rgba(0, 0, 0, 0.25)" /> }
      </V>
      {props.children}
    </V>
  </V>)
}
