import { Input, ScrollView, Text, View } from '@tarojs/components'
import { useEffect, useRef, useState } from 'react'
import style from './index.module.scss'
import IconFont from '@/components/IconFont'
import { getScrollViewHeight } from './utils'
import { store } from '@/core/store/index'
import { sourceMap } from '@/core/utils/classify'

type ISearchHeadProps = {
  // 选中的值
  sltedItemArr?: Array<any>
  // 判断当前是订单类还是招聘类和数量限制
  modeObj?: { mode: string, modeNum: number }
  /** 判断是否过滤招聘类职位 true 是 */
  isHideZpClassify?: boolean
  placeholder?: string;
  maxSelectToastTxt?: string;
  isZpSingle?:boolean
  onSearchClick?: (search: any) => void;
  topHeadHeight?: number
  [key: string]: any
}

let timer
const SearchHead = (props: ISearchHeadProps) => {
  const {
    placeholder = '搜索职位名称',
    modeObj = { mode: '', modeNum: 5 },
    isHideZpClassify = false,
    sltedItemArr = [],
    onSearchClick,
    maxSelectToastTxt = '',
    isZpSingle = false,
    topHeadHeight = 0,
  } = props

  const [isSearchContent, setIsSearchContent] = useState(false)
  const keywords = useRef('')
  const [seachHeadHeight, setSeachHeadHeight] = useState(0)
  const [value, setValue] = useState('')
  const [labelList, setLabelList] = useState([])

  useEffect(() => {
    setTimeout(() => {
      getScrollViewHeight(setSeachHeadHeight)
    }, 100)
  }, [])
  const onSearchInput = (e) => {
    if (timer) {
      clearTimeout(timer)
    }
    const { value } = e.detail
    keywords.current = value
    setValue(value)
    timer = setTimeout(() => {
      getSeatchClassify()
    }, 200)
  }

  const onSearchClear = () => {
    keywords.current = ''
    setValue('')
    setIsSearchContent(false)
  }

  // 职位搜索
  const getSeatchClassify = async () => {
    if (!keywords.current.trim()) {
      setLabelList([])
      setIsSearchContent(false)
      return
    }
    const { source_id: sourceId = 0 } = $.router.data || {}
    const source = sourceMap[sourceId]

    const [data] = await $.request['POST/labelService/v1/search/fuzzy']({ searchKeyword: keywords.current.trim(), filterRecruitmentOcc: isHideZpClassify })
    $.report.event('workTypeSelectionSearch', {
      source,
      content_texts: keywords.current,
      search_result: $.isArrayVal(data.list) && data.list.length ? '有结果' : '无结果',
      gs_keywords_show: data.list?.map(item => item.matchKeyword),
    })

    const regKeywords = new RegExp(keywords.current.replace(/([.*+?^=!:${}()|[\]/\\])/g, '\\$1'), 'gi')
    //  替换的字符
    const repKeywords = '<span style=\'color:#0092ff\'>$&</span>'
    const nList = ($.isArrayVal(data.list) ? data.list : []).map((item: any) => {
      return { ...item, aliasName: `<div>${item.matchKeyword.replace(regKeywords, repKeywords)}</div>` }
    })
    setLabelList(nList)
    setIsSearchContent(true)
    setValue(keywords.current.trim())
  }

  const onSearchLabelClick = (event, item) => {
    const { dataset: { index } } = event.currentTarget
    const { occId, matchKeyword } = item
    const { industryTree, industryOccMap } = store.getState().classify
    let sltedItem
    let sltedIndId
    industryTree.find((indId) => {
      const indOccItem = industryOccMap[`${indId}_${occId}`]
      if (!sltedItem && indOccItem) {
        sltedItem = { ...indOccItem }
        sltedIndId = indId
        return true
      }
      return false
    })
    const { source_id: sourceId } = $.router.data || {}
    $.report.event('workTypeSelectionSearchLabelsClick', {
      source_id: `${sourceId}`,
      keywords_list: labelList.map((item: any) => item.matchKeyword),
      keywords: matchKeyword,
      keywords_position: `${index}`,
    })
    if (sltedItem) {
      const { mode } = sltedItem || {}
      if ((`${mode}` === '1' || !isZpSingle) && sltedItemArr.length >= modeObj.modeNum) {
        $.msg(`最多选择${modeObj.modeNum}个${maxSelectToastTxt}职位`)
        return
      }
      onSearchClick && onSearchClick({ occId, indId: sltedIndId, matchKeyword })
      onSearchClear()
    } else {
      $.msg('当前职位不存在,请重新选择')
    }
  }
  return (
    <View className={style.searchView}>
      <View id="searchHeadHeight" className={style.searchHead}>
        <View className={style.searchInputView}>
          <IconFont color='rgba(0, 0, 0, 0.65)' size={32} type='yp-search' />
          <Input
            className={style.searchInput}
            type='text'
            placeholder={placeholder}
            placeholderClass={style.placeholder}
            onInput={onSearchInput}
            value={value}
          />
          {
            value ? (
              <View onClick={onSearchClear}>
                <IconFont className={style.closeIcon} type="yp-close-one" size={32} color='rgba(0, 0, 0, 0.25)' />
              </View>
            ) : null
          }
        </View>
      </View>
      {
        isSearchContent ? (
          <ScrollView className={style.searchContent} scrollY style={{ height: `calc(100vh - ${seachHeadHeight + topHeadHeight}px)`, top: `${seachHeadHeight + topHeadHeight}px` }}>
            <View className={style.searchLabelView}>
              {
                labelList.map((label: any, index) => {
                  return (
                    <View className={style.searchLabel} key={`${label.occId}_${index}`} data-index={index + 1} onClick={(event) => onSearchLabelClick(event, label)}>
                      <View className={style.searchHtml} dangerouslySetInnerHTML={{ __html: label.aliasName }}></View>
                      <Text className={style.labelTxt}>{label.occName}</Text>
                    </View>
                  )
                })
              }
            </View>
          </ScrollView>
        ) : null
      }
    </View>
  )
}

export default SearchHead
