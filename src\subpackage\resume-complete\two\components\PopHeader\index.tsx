import style from './index.module.scss'

type ISalaryPickerProps = {
  /** 是否显示底部边线 */
  bottomLine?: boolean,
  title?: string,
  cancelText?: string,
  confirmText?: string,
  cancel?:()=>void,
  confirm?:()=>void
}
const PopHeader = (props: ISalaryPickerProps) => {
  const { bottomLine = false, title = '', cancelText = '取消', confirmText = '确定', cancel, confirm } = props

  const onCancel = () => {
    cancel && cancel()
  }

  const onSubmit = () => {
    confirm && confirm()
  }
  return (
    <V className={`${style.popBox} ${bottomLine ? style.line : ''}`}>
      <V className={`${style.popBtn} ${style.popCancel}`} onClick={onCancel} >
        <T>{cancelText }</T>
      </V>
      <V className={style.popTitle} >{ title }</V>
      <V className={`${style.popBtn} ${style.popConfirm}`} onClick={onSubmit}>
        <T>{ confirmText }</T>
      </V>
    </V>
  )
}

export default PopHeader
