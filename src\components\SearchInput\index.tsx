import { View, Input, Text } from '@tarojs/components'
import s from './index.module.scss'
import IconFont from '../IconFont'

// 定义组件的属性类型
interface SearchInputProps {
  placeholder?: string;
  value?: string;
  onInput?: (newValue: string) => void;
  onClear?: () => void;
  onSearch?: (value: string) => void;
  leftSlot?: React.ReactNode;
  rightSlot?: React.ReactNode;
  disabled?: boolean;
  focus?: boolean;
  maxlength?: number;
}

const SearchInput: React.FC<SearchInputProps> = ({
  placeholder = ' ',
  value = '',
  disabled = false,
  onInput,
  onClear,
  onSearch,
  leftSlot,
  rightSlot,
  maxlength = 140,
}) => {
  const handleInput = (e: any) => {
    const newValue = e.detail.value
    if (onInput) onInput(newValue) // 将输入值传递给父组件
  }

  const handleClear = () => {
    if (onClear) onClear() // 通知父组件清空
  }

  const handleSearch = () => {
    if (onSearch) onSearch(value) // 搜索时传递当前 value
  }

  const handleConfirm = () => {
    // 触发搜索事件
    handleSearch()
  }
  return (
    <View className={s.searchInput}>
      <View className={s.inputBox}>
        {/* 左插槽 */}
        <View className={s.leftSlot}>
          {leftSlot || <IconFont color='rgba(0, 0, 0, 0.65)' size={32} type='yp-search' />}
        </View>

        {/* 输入框 */}
        {disabled ? (
          <V className={s.disableInput}>
            <View className={s.ttText}>{value || <View className={s.disablePlaceHolder}>{placeholder}</View>}</View>
          </V>
        ) : <Input
          type="text"
          className={s.input}
          placeholder={placeholder}
          maxlength={maxlength}
          value={value} // 受控 value
          onInput={handleInput}
          disabled={disabled}
          onConfirm={handleConfirm}
        />}

        {/* 清除按钮 */}
        {value && !disabled && (
          <View className={s.clearIcon} onClick={handleClear}>
            <IconFont type="yp-close-one" size={32} color='rgba(0, 0, 0, 0.25)' />
          </View>
        )}
      </View>
      {/* 右插槽 */}
      {
        rightSlot || (
          <View className={s.rightSlot} onClick={handleSearch}>
            {rightSlot || <Text className={s.defaultSearch}>搜索</Text>}
          </View>
        )
      }

    </View>
  )
}

export default SearchInput
