import classNames from 'classnames'
import { useEffect, useState } from 'react'
import { View as V } from '@tarojs/components'
import s from './index.module.scss'
import { isHonorKwaiDevice, getMaskZIndex } from '@/utils/deviceDetection'

export type MaskProps = {
  /** 是否显示 */
  visible: boolean
  /** 渲染的内容 */
  children: React.ReactNode
  /** 关闭事件 */
  onClose?: () => void
  /** 点击遮罩是否触发关闭，默认true */
  maskClose?: boolean
  /** 层级 默认10000 */
  zIndex?: number
  /** 样式 */
  className?: string
  /** 行内样式 */
  style?: React.CSSProperties
  /** 禁止滑动 ，仅微信有效 */
  catchMove?: boolean
  /** 禁止滑动 */
  disableScroll?: boolean
}

/**
 * mask 蒙尘
*/
export default (props: MaskProps) => {
  const { children, visible, onClose, maskClose = true, zIndex = 10000, style, className, disableScroll = false, catchMove = true } = props
  const [sysInfo, setSysInfo] = useState<any>(null)

  // 获取系统信息，用于设备检测
  useEffect(() => {
    const info = $.sysInfo()
    setSysInfo(info)
  }, [])

  // 检测是否是荣耀设备的快手小程序
  const isHonorKwai = isHonorKwaiDevice(sysInfo)

  // 获取动态z-index值
  const getDynamicZIndex = () => {
    return getMaskZIndex(sysInfo, zIndex)
  }

  // 获取动态CSS类名
  const getDynamicClassName = () => {
    const baseClasses = [s.mask, className]
    if (isHonorKwai) {
      baseClasses.push('honor-kwai-mask')
    }
    return classNames(...baseClasses.filter(Boolean))
  }

  const onClickMask = () => {
    if (maskClose) {
      onClose?.()
    }
  }

  return (
    <V
      className={getDynamicClassName()}
      style={{ visibility: visible ? 'visible' : 'hidden', opacity: visible ? 1 : 0, zIndex: getDynamicZIndex(), ...style }}
      onClick={onClickMask}
      catchMove={catchMove}
      disableScroll={disableScroll}
    >
      {children}
    </V>
  )
}
