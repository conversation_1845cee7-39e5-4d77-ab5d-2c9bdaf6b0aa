.item {
  padding: 32rpx 0;
}

.title {
  color: rgba(0, 0, 0, 0.85);
  font-weight: bold;
  font-size: 50rpx;
  line-height: 70rpx;
}

.label {
  display: flex;
  align-items: center;
  color: rgba(0, 0, 0, 0.85);
  font-family: "PingFang SC";
  font-weight: bold;
  font-size: 38rpx;
  line-height: 54rpx;
}

.desc {
  color: rgba(0, 0, 0, 0.45);
}

.input {
  padding: 32rpx 24rpx;
  border-radius: 16rpx;
  border: 2rpx solid rgba(233, 237, 243, 1);
  margin-top: 24rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 34rpx;
  line-height: 48rpx;
}

.inputTxt {
  color: rgba(0, 0, 0, 0.85);
  width: 100%;
}

.inputPlaceholder {
  color: rgba(0, 0, 0, 0.25);
  width: 100%;
}

.icon {
  margin-left: 32rpx;
}
