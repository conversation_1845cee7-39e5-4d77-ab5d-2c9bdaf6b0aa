import { defineConfig, type UserConfigExport } from '@tarojs/cli'
import TsconfigPathsPlugin from 'tsconfig-paths-webpack-plugin'
import path from 'node:path'
import webpack from 'webpack'
import { writeFileSync } from 'node:fs'
import devConfig from './dev'
import prodConfig from './prod'
import MiniConfig from './mini-config'

/** 当前运行平台 */
const miniPlatform = process.env.TARO_ENV?.trim()
/** 当前运行平台 */
const currentMiniConfig = MiniConfig[process.env.TARO_APP_ID]

const EnvTpl = `/** 该文件为自动生成，用于记录马甲包标识等信息 */
export const MiniConfig: MiniConfigType = ${JSON.stringify(currentMiniConfig, null, 2)}
  `
writeFileSync(path.join(__dirname, '../src/core/env.ts'), EnvTpl, { encoding: 'utf-8' })

// https://taro-docs.jd.com/docs/next/config#defineconfig-辅助函数
export default defineConfig(async (merge, { command, mode }) => {
  const baseConfig: UserConfigExport = {
    projectName: 'yp-taro-mini',
    date: '2024-11-14',
    designWidth(input: any) {
      return 750
    },
    deviceRatio: {
      640: 2.34 / 2,
      750: 1,
      375: 2,
      828: 1.81 / 2,
    },
    sourceRoot: 'src',
    outputRoot: `dist/${miniPlatform}`,
    plugins: [
      '@tarojs/plugin-platform-kwai',
    ],
    // plugins: [path.join(process.cwd(), 'config/plugin.ts')],
    defineConstants: {
    },
    sass: {
      resource: [
        path.join(process.cwd(), 'src/assets/style/variables.scss'),
        path.join(process.cwd(), 'src/assets/style/mixins.scss'),
        // path.resolve(__dirname, '..', 'src/assets/style/variables.scss'),
        // path.resolve(__dirname, '..', 'src/assets/style/mixins.scss'),
      ],
      // ['src/assets/style/variables.scss', 'src/assets/style/mixins.scss'],
    },
    copy: {
      patterns: [
        // { from: 'src/mini.project.json', to: `dist/${miniPlatform}/mini.project.json` },
        { from: 'src/assets/images', to: `dist/${miniPlatform}/assets/images` },
        // { from: 'src/customize-tab-bar', to: `dist/${miniPlatform}/customize-tab-bar` },
        // { from: 'src/pages/index/components/join-group', to: `dist/${miniPlatform}/pages/index/components/join-group` },
      ],
      options: {
      },
    },
    framework: 'react',
    compiler: {
      type: 'webpack5',
      // prebundle: { enable: false },
      prebundle: {
        enable: false,
      },
    },
    alias: {
      '@': path.resolve(__dirname, '..', 'src'),
    },
    cache: {
      enable: false, // Webpack 持久化缓存配置，建议开启。默认配置请参考：https://docs.taro.zone/docs/config-detail#cache
    },
    mini: {
      postcss: {
        pxtransform: {
          enable: true,
          config: {

          },
        },
        url: {
          enable: true,
          config: {
            limit: 1024, // 设定转换尺寸上限
          },
        },
        cssModules: {
          enable: true, // 默认为 false，如需使用 css modules 功能，则设为 true

          config: {
            namingPattern: 'module', // 转换模式，取值为 global/module
            generateScopedName: process.env.NODE_ENV === 'development' ? '[name]__[local]___[hash:base64:5]' : '[hash:base64:5]',
          },
        },
      },
      miniCssExtractPluginOption: {
        ignoreOrder: true,
      },
      webpackChain(chain) {
        chain.resolve.plugin('tsconfig-paths').use(TsconfigPathsPlugin)
      },
    },
    h5: {
      publicPath: '/',
      staticDirectory: 'static',
      output: {
        filename: 'js/[name].[hash:8].js',
        chunkFilename: 'js/[name].[chunkhash:8].js',
      },
      miniCssExtractPluginOption: {
        ignoreOrder: true,
        filename: 'css/[name].[hash].css',
        chunkFilename: 'css/[name].[chunkhash].css',
      },
      postcss: {
        autoprefixer: {
          enable: true,
          config: {},
        },
        cssModules: {
          enable: false, // 默认为 false，如需使用 css modules 功能，则设为 true
          config: {
            namingPattern: 'module', // 转换模式，取值为 global/module
            generateScopedName: '[name]__[local]___[hash:base64:5]',
          },
        },
      },
      webpackChain(chain) {
        if (process.env.TARO_ENV === 'kwai') {
          chain
            .plugin('define-plugin')
            .use(webpack.DefinePlugin, [
              {
                $: 'ks.$',
              },
            ])
        }
        chain.resolve.plugin('tsconfig-paths').use(TsconfigPathsPlugin)
      },
    },
    rn: {
      appName: 'taroDemo',
      postcss: {
        cssModules: {
          enable: false, // 默认为 false，如需使用 css modules 功能，则设为 true
        },
      },
    },
  }
  if (process.env.NODE_ENV === 'development') {
    // 本地开发构建配置（不混淆压缩）
    return merge({}, baseConfig, devConfig)
  }
  // 生产构建配置（默认开启压缩混淆等）
  return merge({}, baseConfig, prodConfig)
})
