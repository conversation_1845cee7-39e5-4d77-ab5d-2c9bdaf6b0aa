import { View } from '@tarojs/components'
import s from './index.module.scss'
import IconFont from '@/components/IconFont'
import CellCard from '../CellCard'
import { store } from '@/core/store'

type PropsType = {
  dataSource: any
  onRefresh: (data) => void
  onClick: (e) => void
}
/** 工作城市 */
export default function Index(props: PropsType) {
  const dataSource = props.dataSource || {}

  /** 设置工作城市 */
  const settingAddr = async (areas) => {
    const { resumeUuid } = dataSource
    const areaIdList = areas.map((item) => item.id)
    const [data, res] = await $.request['POST/resume/v3/perfect/hopeArea']({
      resumeUuid,
      areaIdList,
    }).catch((res) => res)
    /* const { code, popup } = res || {}
    if (code != 0 && popup) {
      $.showModal({
        ...popup,
      })
      return
    } */
    if (res.code == 0) {
      props.onRefresh && props.onRefresh(data)
    } else {
      $.msg(res.msg || '工作城市修改失败')
    }
  }

  const onTap = (e) => {
    const { hopeAreaCity } = dataSource
    const { maxCityNum } = store.getState().resume.globalConfig.basicConfigResp || {}
    const areas: number[] = []
    props.onClick && props.onClick(e)
    if ($.isArrayVal(hopeAreaCity)) {
      hopeAreaCity.forEach((item) => {
        areas.push(+item.id)
      })
    }

    const buttonName = Array.isArray(hopeAreaCity) ? hopeAreaCity.map(item => item.name).join('、') : ''
    $.openAddress({
      areas,
      title: '选择城市',
      level: 2,
      hideNation: true,
      maxNum: maxCityNum,
    }, { source_id: '5', source: '编辑找活名片', button_name: buttonName }, settingAddr)
  }
  return (
    <CellCard
      title="工作城市"
    >
      <View onClick={onTap} className={s.content}>
        {$.isArrayVal(dataSource.hopeAreaCity)
          ? <View className={s['c-text']}>
            {
              dataSource.hopeAreaCity.map((item, index) => {
                return index > 0 ? `、${item.name}` : item.name
              })
            }
          </View>
          : <View className={s.placeholder}>添加你想在哪个城市工作，简历推荐更精准</View>
        }
        <IconFont
          type="yp-mbxl"
          size={32}
          color="rgba(0, 0, 0, 0.45)"
        />
      </View>
    </CellCard>

  )
}
