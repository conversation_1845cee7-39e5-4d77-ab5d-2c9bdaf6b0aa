/*
 * @Date: 2021-12-15 11:31:46
 * @Description: 校验、正则相关工具，所有函数名均以is开头
 */

/**
 * @name: randIntNumber for jsxin
 * @params: min: number 最小区间(包括)
 * @params: max: number 最大区间(不包括)
 * @return: number 生成的随机数
 * @description: 生成一个在 [min-max) 该区间的整数
 */
export const randIntNumber = (min = 0, max = 20): number => {
  return Math.floor(Math.random() * (max - min)) + min
}

/**
 * @name: isRequire for jsxin
 * @params: val: string 传入需要被验证字符串
 * @params: min: 最小必须达到多少字
 * @param: max:最多不超过多少字 0为不验证最大字数
 * @return: boolean
 * @description: 验证内容 是否必须有汉字 且不少于 min 字 不大于max字
 */
export const isVaildVal = (value: string, min = 15, max = 0): boolean => {
  const reg = /[\u4E00-\u9FFF]+/
  let val = `${value}`
  if (val?.length > 1000) {
    val = value.substring(0, 1000)
  }
  return max ? reg.test(val) && (val.length >= min) && (val.length <= max) : reg.test(val) && (val.length >= min)
}

/**
 * 判断字符串中是否包含指定数量的中文字符，并且长度在指定范围内
 * @param value 要检查的字符串
 * @param min 最小允许的字符长度
 * @param max 最大允许的字符长度
 * @param zhLen 字符中至少需要的中文字符数，不传入默认为min的长度
 * @returns 符合条件返回true，否则返回false
 */
export const isChineseVal = (value: string, min = 15, max = 0, zhLeng = null): boolean => {
  const reg = /^[\u4E00-\u9FFF]+$/
  const val = `${value}`
  // eslint-disable-next-line no-param-reassign
  const zhLen = zhLeng == null ? min : zhLeng
  if (min && val.length < min) {
    return false
  }
  if (max && val.length > max) {
    return false
  }
  const arr = val.split('')
  let count = 0
  for (let i = 0; i < arr.length; i += 1) {
    if (reg.test(arr[i])) {
      count += 1
    }
  }
  return count >= zhLen
}

/**
 * @return: boolean
 * @description: 验证内容 传入的val数字是否在指定的区间内，包含最大最小区间
 */
export const isVaildNum = (val: number | string, min: number, max: number): boolean => {
  const ValNum = Number(val)
  // 如果不是数字
  if (Number.isNaN(ValNum)) {
    return false
  }
  return ValNum >= min && ValNum <= max
}

/**
 * @description: 验证内容 传入的val数字是否在指定的区间内，包含最大最小区间
 * 如果通过验证回数字类型，否则返回false
 * @returns number | false
 * @info 判断是否通过验证的时候，应该用 !== false 来判断 不能用 !isVaiNum
 * @example const num = isVaiNum('019', 1, 20)
 *  if(num !== false) { // num值为数字类型的19
 *    // 通过验证的代码
 *  }
 */
export const isVaiNum = (val: string | number, min: number, max: number): number | false => {
  const num = Number(val)
  if (Number.isNaN(num)) {
    return false
  }
  if (num >= min && num <= max) {
    return num
  }
  return false
}

/**
 * @name: allChinese for jsxin
 * @params: str: string 需要被验证的字符串
 * @return: boolean
 * @description: 当前字符串是否是2-5个全中文
 */
export const allChinese = (str: string): boolean => {
  // eslint-disable-next-line prefer-regex-literals
  const reg = new RegExp('^[\u4E00-\u9FA5]{2,5}$')
  return reg.test(str)
}

/**
 * @name: validPassWord for jsxin
 * @params: str: string 需要被验证的密码
 * @return: boolean
 * @description: 检测当前密码是否符合规范
 */
export const validPassWord = (str: string): boolean => {
  // eslint-disable-next-line no-useless-escape
  const reg = /([a-zA-Z0-9_\.@#$%^&*]){6,20}/
  return reg.test(str)
}

/** 判断时间是否是同一天 */
export const isSameDay = (timeStampA, timeStampB) => {
  const dateA = new Date(timeStampA)
  const dateB = new Date(timeStampB)
  return dateA.setHours(0, 0, 0, 0) === dateB.setHours(0, 0, 0, 0)
}

/** 正则匹配手机号 */
export function matchContentPhone(content: string) {
  const regexp = /1[3-9]\d{9}/g
  const phone = typeof content == 'string' && content.match(regexp)
  if (Array.isArray(phone)) {
    return phone[0]
  }
  return ''
}

/** 正则邮箱 */
export function isEmail(email) {
  const reg = /^[a-zA-Z0-9][a-zA-Z0-9._-]*@[a-zA-Z0-9][a-zA-Z0-9-]*\.[a-zA-Z]{2,}$/
  return reg.test(email)
}

/** 微信号验证 6～10位字符长度 */
export function isWeChat(weChat) {
  // const reg = /^[a-zA-Z_][-_a-zA-Z0-9]{5,19}$/
  const reg = /^[-_a-zA-Z0-9]{6,20}$/
  return reg.test(weChat)
}

/** 正则判断是否是金额 */
export const isMoney = (value: string) => {
  const reg = /^(([1-9]\d*)|\d)(\.\d{1,2})?$/
  return reg.test(value)
}

/** 正则判断小数点位数
 * @param value 需要验证的数字
 * @param decimal 小数点位数 默认0
 */
export const isDecimal = (value: number | string, decimal = 0) => {
  const reg = new RegExp(`^\\d+(\\.\\d{1,${decimal}})?$`)
  return reg.test(`${value}`)
}

/** 字符串是否包含汉字 */
export const isChinese = (value: string) => {
  const reg = /[\u4e00-\u9fa5]/
  return reg.test(value)
}

/** 找活的工龄逻辑判断
 * @param workYear 工龄
 * @param age 年龄
 */
export const isWorkYear = (workYear: string, age: number | string) => {
  const interval = 15
  const ageNum = Number(age)
  const workYearNum = Number(workYear)
  if (ageNum < 1 || !ageNum || !workYearNum) {
    // 这里代表不需要判断工龄
    return { error: false }
  }

  const diff = ageNum - workYearNum - interval
  const msg = `所输入工龄不能超过${ageNum - interval}年`
  if (diff < 0) {
    return { error: true, diff: Math.abs(diff), msg }
  }
  return { error: false, diff, msg }
}
