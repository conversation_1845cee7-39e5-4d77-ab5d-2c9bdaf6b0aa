// 主题色
$base-color: rgba(0, 0, 0, 0.85);
$primary-color: #0092ff;
// 通用颜色
$title-color: #333333; // 标题颜色
$red-color: #ff6363; // 红色
$white-color: #ffffff; // 白色
$orange-color: #FF8904; // 橙色
$gray-color: #8c8c8c; // 灰色
$error-color: #E8362E; // 错误颜色
$error-color-85: rgba(232, 54, 46, 0.85); // 错误背景颜色
$line-color: #E9EDF3; // 边线颜色

// $description: 辅助色-杂项
// 徽标文本的颜色
$badge-text-color: #fffefe;
// 边框颜色
$border-color: #f5f5f5;
// 阴影颜色
$shadow-color: #e7e6e4;
// 输入框文本颜色
$input-color: #262628;
$input-placeholder: #bfbfbf;

// $description: 辅助色-状态色
// 提示
$tip-color: #00cbff;
// 警示
$pro-color: #ff9800;
// 成功
$suc-color: #46db7a;
// 警告
$warn-color: #f74742;

// $description: 辅助色-文字色
// 文字
$text-color: rgba(0, 0, 0, 0.85);
// 常规文字
$text-secondary-color: rgba(0, 0, 0, 0.65);
// 次要文字
$text-third-color: rgba(0, 0, 0, 0.45);
// 占位文字
$text-fourth-color: rgba(0, 0, 0, 0.25);
// 深色背景文字
$fill-text-color: rgba(255, 255, 255, 0.95);
// 分隔符
$separator-color: rgba(0, 0, 0, 0.24);
// 次级分隔符
$separator-secondary-color: rgba(0, 0, 0, 0.12);
$text85: rgba(0, 0, 0, 0.85);
$text65: rgba(0, 0, 0, 0.65);
$text45: rgba(0, 0, 0, 0.45);
$text25: rgba(0, 0, 0, 0.25);

// 背景色
$bg-color: #f5f6fa;
// 点击-background
$press-bg: rgba(255, 255, 255, 0.2);
// 不可点击-background
$disable-bg: rgba(77, 11, 11, 0.6);

// $description 常规-字体大小
// 小号字体
$font-small-size: 24rpx;
// 中号字体
$font-medium-size: 28rpx;

// $description: 基准像素
// 屏幕宽度
$base-width: 750rpx;
// header高度
$header-height: 88rpx;
// footer高度
$footer-height: 100rpx;
// tips高度
$tips-height: 96rpx;
// 外边距
$base-margin: 16rpx;
// 下边距
$bottom-margin: 16rpx;
// 内边距
$base-padding: 16rpx;
// 左右内边距
$padding: 32rpx;
// 主页底部内边距
$padding-bottom: 150rpx;
// 圆角
$base-border-radius: 8rpx;
// 圆角
$border-radius: 12rpx;
// 文字加粗
$font-bold: bold;

// 动画
// transition 动画持续时间
$base-trans-time: 0.24s;
