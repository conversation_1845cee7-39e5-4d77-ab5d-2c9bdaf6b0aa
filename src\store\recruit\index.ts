/*
 * @Date: 2022-01-06 14:24:43
 * @Description: 职位相关
 */

import { createSlice, PayloadAction } from '@reduxjs/toolkit'

const { reducer, actions, name } = createSlice({
  name: 'recruit',
  initialState: {
    /** 列表传入详情所需埋点数据 */
    buryingPoint: {
      id: '',
      info: {
        /** 请求id（随机生成唯一标识） */
        request_id: '-99999',
        /** 是否置顶 */
        topping: '-99999',
        /** 排序时间 */
        sort_time: '-99999',
        /** 排序筛选项（企业认证、最新排序、实名认证、智能推荐） */
        sort_filter: '-99999',
        /** 搜索结果 */
        search_result: '',
        /** 算法ID */
        backend_id: null,
      },
    } as any,
  },
  reducers: {
    setState(state, { payload }: PayloadAction<Record<string, any>>) {
      Object.assign(state, payload)
    },
  },
})

export default {
  name,
  reducer,
  actions: { ...actions },
}
