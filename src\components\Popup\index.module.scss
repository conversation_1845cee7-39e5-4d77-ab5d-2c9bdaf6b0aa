.content {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  transition: 0.2s;
  transform: translateY(100%);
  background-color: #fff;
  overflow: hidden;
  border-top-left-radius: 32px;
  border-top-right-radius: 32px;
  z-index: 10001;
}

// 荣耀设备快手小程序特殊处理
.content.honor-kwai-fix {
  z-index: 99999 !important;
  position: fixed !important;
  // 确保在所有原生组件之上
  isolation: isolate;
  // 创建新的层叠上下文
  will-change: transform;
}

.header {
  position: relative;
  border-top-left-radius: 32px;
  border-top-right-radius: 32px;
  height: 112px;
  padding: 0 32px;
}

.title {
  font-weight: bold;
  text-align: center;
  line-height: 112px;
  font-size: 34px;
  color: rgba(0, 0, 0, 0.85);
}

.right {
  position: absolute;
  right: 32px;
  top: 20px;
}
