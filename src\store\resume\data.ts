/*
 * @Author: ji<PERSON><PERSON>
 * @LastEditors: xia<PERSON><PERSON><PERSON><PERSON> <EMAIL>
 */
export const resumeCompleteDef: YModels['POST/resume/v1/resumePerfect/getProgressStageInfo']['Res']['data'] = {
  /** 完善度按钮 */
  button: '',
  /** 完善度跳转页面 */
  jump: 'EditResumeInfo',
  /** 完善度得分 */
  score: 0,
  /** 完善度提示信息 */
  tips: '简历完善度过低，仅1%老板感兴趣',
}

/* 名片全局配置的默认数据 */
export const globalConfigDef: YModels['POST/resume/v3/common/globalConfig']['Res']['data'] = {
  /** 基础配置 */
  basicConfigResp: {
    eduMaxNum: 5,
    maxCityNum: 3,
    maxFTPositionNum: 5,
    maxPTPositionNum: 5,
    /** 子名片名片新增最大可选工种数量 */
    occupationMaxNum: 10,
    /** 项目经历最大值 */
    projectExpMaxNum: 5,
    /** 最大子名片数量 */
    resumeSubMaxNum: 10,
    videoMaxNum: 10,
    /** 工作经历最大值 */
    workMaxNum: 5,
  },
  resumeAttachUploadResp: {
    /** 附件简历最大值 */
    attachFileMax: 3,
    wxUpload: 1,
    pcUpload: 0,
    mobileUpload: 0,
  },
}

/* 找活名片三步完善基础信息 */
export const resumeThreePerfectStepDef = {
  /** 期望所在地 */
  hopeArea: '',
  /** 自我介绍 */
  introduce: '',
  /** 工种ids */
  occupations: [],
  /** 以完善回显数据 */
  resumeSubJson: [],
  /**  */
  resumeSubUuid: '',
  /** 模板id */
  resumeTemplateIdList: [],
  /** 完善步数 */
  step: 0,
  /** 组件ids */
  templateIds: { first: [], second: [] },
  /** 自我介绍页面是否展示项目经验提示 */
  tips: false,
  /** 是否展示上传按钮 */
  uploadSwitch: false,
}

/* 找活名片是否存在 */
export const resumeExistDef = {
  /** 名片是否存在 true:存在 false:不存在 */
  exist: false,
  resumeId: 0,
  /** 名片uuid */
  resumeUuid: '',
  /** 简历审核状态 1:待审核,2:审核成功,3:审核失败 */
  checkStatus: 1,
  workStatus: 1,
  /** 0: 不隐藏, 1隐藏 */
  hideStatus: 0,
  /** 是否是默认数据 */
  isDef: true,
} as Models['POST/resume/v3/base/exist']['Res']['data'] & { isDef: boolean }
