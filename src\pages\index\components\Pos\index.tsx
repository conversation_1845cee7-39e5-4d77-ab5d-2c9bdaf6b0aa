/*
 * @Date: 2024-11-22 14:05:58
 * @Description: 职位选择
 */

import { ScrollView, View as V, Text as T } from '@tarojs/components'
import cn from 'classnames'
import IconFont from '@/components/IconFont'
import s from './index.module.scss'
import Click from '@/components/Click'
import { dispatch, actions, getState } from '@/core/store'

export default ({ homeClassify, allClassify, activeClassify, setActiveClassify, onRefResh }) => {
  const onAdd = () => {
    const { recruitWorkerSearchFindJobPublishOccCnt } = getState().classify.classifyConfig
    $.openClassify({ value: homeClassify, title: '选择期望职位（可多选）', maxSelectNum: recruitWorkerSearchFindJobPublishOccCnt || 5, source_id: 9 }, async (data: any) => {
      dispatch(actions.storage.setItem({ key: 'homeClassify', value: data }))
      const active = -1
      setActiveClassify(active)
      onRefResh({ page: 1 }, { nextActiveClassify: active, nextHomeClassify: data })
    })
  }

  const onSubmit = (item) => {
    const active = item.id == -1 ? -1 : item.id
    if (active == activeClassify) {
      return
    }
    setActiveClassify(active)
    onRefResh({ page: 1 }, { nextActiveClassify: active })
  }

  return (
    <V className={s.posBox}>
      <ScrollView scrollX className={cn(s.posScroll, homeClassify.length > 4 && s.posScrollFull)}>
        <V className={s.posList}>
          {allClassify?.map((item) => {
            return (
              <Click
                onClick={() => onSubmit(item)}
                key={item.occId}
                className={cn(s.posItem, item.id == activeClassify && s.active)}
              >
                {item.name.length > 7 ? `${item.name.slice(0, 7)}...` : item.name}
              </Click>
            )
          })}
        </V>
      </ScrollView>
      <V className={s.pos}>
        <Click onClick={onAdd}>
          {homeClassify.length <= 4 ? <V className={s.posAdd}><T className={s.posText}>添加职位</T></V> : <IconFont type='yp-tianjia' color='#000000a6' size={48} />}
          {/* {homeClassify.length <= 4 ? <T className={s.posText}>添加职位</T> : <IconFont type='yp-tianjia' color='#0092FF' size={32} />} */}
        </Click>
      </V>
    </V>
  )
}
