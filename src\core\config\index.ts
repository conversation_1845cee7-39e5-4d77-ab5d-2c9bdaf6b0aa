/*
 * @Date: 2024-11-18 10:14:50
 * @Description: 项目配置
 */

import Taro from '@tarojs/taro'

/** 小红书 appid */
export const appid = 'ks712413168329900478'
/** 支付宝 token 小程序的标识符，通常是马甲包（即同一小程序的不同版本或变体）名字的拼音简写 */
export const miniToken = 'ks'

/** 小程序运行时 */
export const runtime = 'KS_MINI'

const ENV = process.env.TARO_APP_ENV !== 'prod' ? (Taro.getStorageSync('DEV_TARO_APP_ENV') || process.env.TARO_APP_ENV) : process.env.TARO_APP_ENV

/** 环境名称 */
export const ENV_LIST = {
  dev: '开发环境',
  test: '测试环境',
  pre: '预发环境',
  prod: '正式环境',
}

/** 环境名称 */
export const ENV_NAME = ENV_LIST[ENV]

// * JAVA全局请求接口域名
export const javaDomain = {
  // * 开发站
  dev: 'https://yupao-develop.yupaowang.com',
  // 测试站
  test: 'https://yupao-test.yupaowang.com',
  // 预发布
  pre: 'https://yupao-master.yupaowang.com',
  // * 正式站
  prod: 'https://yupao-prod.yupaowang.com',
}[ENV]

// * M端地址（用于小程序直接打开H5协议规则页面）---版本1
//! 勿删,线上也在用
export const MOBILE_TERMINAL_LIST = {
  // 开发站
  dev: 'https://mtest.yupaowang.com/',
  //  测试站
  test: 'https://mtest.yupaowang.com/',
  //  预发布
  pre: 'https://rm.kkbbi.com/',
  //  预发布正式站
  rel: 'https://rm.kkbbi.com/',
  //  正式站
  prod: 'https://m.yupao.com/',
}

// * M端地址（用于小程序直接打开混合开发的落地页）---版本2 这个用于m端的混合开发落地页
export const MOBILE_TERMINAL_NEW_LIST: { [key: string]: string } = {
  // * 开发站
  dev: 'https://h5hybridtest.yupaowang.com/',
  // * 测试站
  test: 'https://h5hybridtest.yupaowang.com/',
  // * 预发布
  pre: 'https://h5hybridmaster.yupaowang.com/',
  // * 预发布正式站
  rel: 'https://h5hybridmaster.yupaowang.com/',
  // * 正式站
  prod: 'https://h5hybridprod.yupaowang.com/',
}

// * 埋点应用ID
export const REPORT_APP_ID_LIST: { [key: string]: number } = {
  // 开发站
  dev: 10000016,
  // 测试站
  test: 10000002,
  // 预发布
  pre: 10000017,
  // 预发布正式站
  rel: 10000017,
  // 正式站
  prod: 10000001,
}

// * M端地址（用于小程序直接打开H5协议规则页面）
export const MOBILE_TERMINAL_URL: string = MOBILE_TERMINAL_LIST[ENV]

// * M端地址（用于小程序直接打开H5协议规则页面 ---版本2
export const MOBILE_TERMINAL_NEW_URL = MOBILE_TERMINAL_NEW_LIST[ENV]

// * 小程序当前版本号(关联本地存储)
export const VERSION = '8.6.0'

// * 后端当前版本号
export const requestVersion = '8.6.0'

// * 小程序加密密钥
export const nonceKey = ['dev', 'test'].includes(ENV) ? 'ZYEr8cHa4U4Lq2f70Ccjl9VFmbbmCTDA' : 'TJViHTOMPexr7OG0W8qhStZy42H7Fikw'

/** 图片域名 */
export const IMAGE_CDN = 'https://cdn.yupaowang.com/yp_mini/images/'
// * 火山SDK APP_ID
export const FINDER_APP_ID = REPORT_APP_ID_LIST[ENV]
// * 高德地区key
export const MAP_KEY = '20f12aae660c04de86f993d3eff590a0'
