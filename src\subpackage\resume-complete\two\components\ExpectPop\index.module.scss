.pop {
  border-radius: 16rpx 16rpx 0rpx 0rpx;
}

.body {
  background: rgba(255, 255, 255, 1);
  padding-bottom: constant(safe-area-inset-bottom); /* 兼容 IOS<11.2 */
  padding-bottom: env(safe-area-inset-bottom); /* 兼容 IOS>11.2 */
}

.head {
  padding: 24rpx 32rpx;
}

.title {
  display: flex;
  align-items: center;
  justify-content: space-between;
  color: rgba(0, 0, 0, 0.85);
  font-weight: bold;
  font-size: 34rpx;
  line-height: 48rpx;
}

.desc {
  margin-top: 8rpx;
  color: rgba(0, 0, 0, 0.45);
  font-size: 28rpx;
  line-height: 160.000002%;
}

.content {
  padding: 0 32rpx;
}

.item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 40rpx 0;
  border-bottom: 2rpx solid rgba(233, 237, 243, 1);
}

.label {
  width: 100%;
  color: rgba(0, 0, 0, 0.85);
  font-weight: bold;
  font-size: 34rpx;
  line-height: 48rpx;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.salary {
  color: rgba(0, 0, 0, 0.85);
  font-size: 28rpx;
  line-height: 160.000002%;
  margin-left: 24rpx;
  white-space: nowrap;
}

.btnView {
  padding: 24rpx 32rpx;
}

.btn {
  width: 100%;
  padding: 24rpx 0rpx;
  border-radius: 16rpx;
  background: rgba(0, 146, 255, 1);
  color: rgba(255, 255, 255, 1);
  font-weight: bold;
  font-size: 34rpx;
  line-height: 48rpx;
  text-align: center;
}
