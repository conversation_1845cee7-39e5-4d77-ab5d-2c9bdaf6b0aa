{"name": "yp-taro-mini", "version": "1.0.0", "private": true, "description": "", "type": "module", "templateInfo": {"name": "default", "typescript": true, "css": "Sass", "framework": "React"}, "scripts": {"start": "node ./script/index.js", "postinstall": "patch-package", "ts-api-doc": "node ./script/apidoc/index.js", "build:weapp": "taro build --type weapp", "build:swan": "taro build --type swan", "build:alipay": "taro build --type alipay", "build:alipay:production": "cross-env NODE_ENV=production taro build --type alipay", "build:tt": "taro build --type tt", "build:h5": "taro build --type h5", "build:rn": "taro build --type rn", "build:qq": "taro build --type qq", "build:jd": "taro build --type jd", "build:xhs": "taro build --type xhs", "build:ks": "taro build --type kwai", "build:quickapp": "taro build --type quickapp", "build:harmony-hybrid": "taro build --type harmony-hybrid", "dev:weapp": "npm run build:weapp -- --watch", "dev:swan": "npm run build:swan -- --watch", "dev:alipay": "npm run build:alipay -- --watch", "dev:tt": "npm run build:tt -- --watch", "dev:h5": "npm run build:h5 -- --watch", "dev:rn": "npm run build:rn -- --watch", "dev:qq": "npm run build:qq -- --watch", "dev:jd": "npm run build:jd -- --watch", "dev:xhs": "npm run build:xhs -- --watch", "dev:ks": "npm run build:ks -- --watch", "dev:quickapp": "npm run build:quickapp -- --watch", "dev:harmony-hybrid": "npm run build:harmony-hybrid -- --watch", "test": "jest"}, "browserslist": ["last 3 versions", "Android >= 4.1", "ios >= 8"], "author": "", "dependencies": {"@babel/runtime": "^7.21.5", "@datarangers/sdk-mp": "^2.14.0", "@reduxjs/toolkit": "^2.3.0", "@tarojs/components": "3.6.35", "@tarojs/helper": "3.6.35", "@tarojs/plugin-framework-react": "3.6.35", "@tarojs/plugin-html": "3.6.35", "@tarojs/plugin-platform-alipay": "3.6.35", "@tarojs/plugin-platform-h5": "3.6.35", "@tarojs/plugin-platform-harmony-hybrid": "3.6.35", "@tarojs/plugin-platform-jd": "3.6.35", "@tarojs/plugin-platform-kwai": "^6.0.1", "@tarojs/plugin-platform-qq": "3.6.35", "@tarojs/plugin-platform-swan": "3.6.35", "@tarojs/plugin-platform-tt": "3.6.35", "@tarojs/plugin-platform-weapp": "3.6.35", "@tarojs/plugin-platform-xhs": "^1.2.2", "@tarojs/react": "3.6.35", "@tarojs/runtime": "3.6.35", "@tarojs/shared": "3.6.35", "@tarojs/taro": "3.6.35", "async-validator": "^4.2.5", "classnames": "^2.5.1", "crypto-js": "^4.2.0", "dayjs": "^1.11.13", "qs": "^6.13.0", "react": "^18.0.0", "react-dom": "^18.0.0", "react-redux": "^9.1.2"}, "devDependencies": {"@babel/core": "^7.8.0", "@mini-types/alipay": "^3.0.14", "@pmmmwh/react-refresh-webpack-plugin": "^0.5.5", "@tarojs/cli": "3.6.35", "@tarojs/taro-loader": "3.6.35", "@tarojs/test-utils-react": "^0.1.1", "@tarojs/webpack5-runner": "3.6.35", "@types/jest": "^29.3.1", "@types/node": "^18.15.11", "@types/react": "^18.0.0", "@types/webpack-env": "^1.13.6", "@typescript-eslint/eslint-plugin": "^6.2.0", "@typescript-eslint/parser": "^6.2.0", "babel-plugin-import": "^1.13.8", "babel-preset-taro": "3.6.35", "cross-env": "^7.0.3", "cross-spawn": "^7.0.5", "eslint": "^8.2.0", "eslint-config-airbnb-base": "15.0.0", "eslint-config-taro": "3.6.35", "eslint-plugin-import": "^2.25.2", "eslint-plugin-react": "^7.8.2", "eslint-plugin-react-hooks": "^4.2.0", "eslint-plugin-sonarjs": "^2.0.4", "inquirer": "^12.1.0", "jest": "^29.3.1", "jest-environment-jsdom": "^29.5.0", "patch-package": "^8.0.0", "postcss": "^8.4.18", "react-refresh": "^0.11.0", "stylelint": "^14.4.0", "ts-node": "^10.9.1", "tsconfig-paths-webpack-plugin": "^4.1.0", "typescript": "^5.1.0", "webpack": "5.78.0"}}