.resultFilter {

  display: flex;
  justify-content: space-between;
  align-items: center;

  .filterBox {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px 0px;
  }

  .filterItem,
  .filterItemActive {
    --color: #000000A6;
    text-align: center;
    color: var(--color);
    font-size: 28px;
    line-height: 40px;
    position: relative;
    padding-right: 18px;
    max-width: 240px;

    :active {
      color: #0092FFFF;
      opacity: 0.8;
    }


    ::after {
      content: '';
      position: absolute;
      bottom: 0;
      right: 0;
      border-top: 5px solid transparent;
      border-right: 5px solid var(--color);
      border-bottom: 5px solid var(--color);
      border-left: 5px solid transparent;
    }
  }

  .filterItemActive {
    --color: #0092FFFF
  }

}