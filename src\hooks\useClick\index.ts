/*
 * @Date: 2024-11-20 14:04:50
 * @Description: 事件触发完了才能触发下一次
 */

import { useRef } from 'react'

export default function useClick<T extends(...args: any[]) => Promise<any>>(fn?: T, time = 300): T {
  const promiseRef = useRef<Promise<any> | null>(null)

  return ((...args: Parameters<T>) => {
    if (promiseRef.current) {
      return promiseRef.current
    }

    // 创建一个新的Promise，防止下一个点击触发
    promiseRef.current = new Promise(async (resolve, reject) => {
      try {
        const result = fn ? await fn(...args) : undefined
        resolve(result)
      } catch (e) {
        reject(e)
      } finally {
        // 重置Promise引用，允许下次点击
        setTimeout(() => {
          promiseRef.current = null
        }, time)
      }
    })

    return promiseRef.current
  }) as T
}
