import { Input } from '@tarojs/components'
import style from './index.module.scss'
import IconFont from '@/components/IconFont'

type INameViewProps = {
  value: string
  disabled?: boolean
  mustObj?: any
  onInputClick?: () => void
  onChanges?: (e: any, type: string) => void
  onClear?: () => void
  [key: string]: any
}

const NameView = (props: INameViewProps) => {
  const { value, mustObj = {}, disabled = false, onInputClick, onChanges, onClear } = props
  const { status, must } = mustObj || {}
  return status ? (
    <V className={style.item}>
      <V className={style.label}>
        {must && <T className={style.must}>*</T>}<T>您的姓名</T>
      </V>
      <V className={style.inpView}>
        <Input
          maxlength={5}
          disabled={disabled}
          value={value}
          adjustPosition
          className={style.inp}
          placeholderClass={style.inpPlace}
          placeholder='请输入您的姓名'
          onClick={onInputClick}
          onInput={(e) => onChanges && onChanges(e, 'userName')}
        />
        {
          value && !disabled && <IconFont onClick={onClear} className={style.clear} type="yp-close-one" size={32} color='rgba(0, 0, 0, 0.25)' />
        }
        <V className={style.inpNumView}>
          <T className={`${value.length > 0 ? style.inpNumGray : ''}`}>{value.length}</T>
          <T>/5</T>
        </V>
      </V>
    </V>
  ) : null
}

export default NameView
