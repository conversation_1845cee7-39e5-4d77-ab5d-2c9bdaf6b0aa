.body {
  padding: 0 24px 24px 24px;
}

.card {
  width: 702px;
  height: 236px;
  padding: 24px;
  border-radius: 24px;
  background: #ffffff;
  margin-bottom: 24px;
}

@mixin animation() {
  background: linear-gradient(
    90deg,
    #f5f7fc 25%,
    #f4f4f4 50%,
    #f5f7fc 75%
  );
  background-size: 200% 100%;
  animation: skeleton-animation 1.5s infinite;
}

.a {
  height: 48px;
  border-radius: 8px;
  @include animation();
}

.b {
  display: flex;
  align-items: center;
  margin: 24px 0;
  justify-content: space-between;
}

.c {
  width: 124.4px;
  height: 44px;
  border-radius: 8px;
  background: #f5f7fc;
  @include animation();
}

.d {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.e {
  width: 472px;
  height: 48px;
  border-radius: 8px;
  background: #f5f7fc;
  @include animation();
}

.f {
  width: 134px;
  height: 48px;
  border-radius: 8px;
  background: #f5f7fc;
  @include animation();
}

/* 骨架屏动画 */
@keyframes skeleton-animation {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}