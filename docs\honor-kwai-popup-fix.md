# 荣耀设备快手小程序弹窗层级问题解决方案

## 问题描述

在荣耀某些机型上，快手小程序的底部tab导航会遮挡弹窗组件，导致用户无法正常操作弹窗内容。

## 问题原因

1. **快手小程序原生组件层级**：快手小程序的底部tabBar是原生组件，具有最高的渲染层级
2. **设备特异性**：荣耀某些机型在渲染层级处理上有特殊表现
3. **z-index限制**：普通的z-index设置无法覆盖原生组件

## 解决方案

### 1. 设备检测工具函数

创建了 `src/utils/deviceDetection.ts` 文件，提供以下功能：

- `isHonorDevice()`: 检测是否是荣耀设备
- `isHonorKwaiDevice()`: 检测是否是荣耀设备的快手小程序
- `needsSpecialPopupZIndex()`: 检测是否需要特殊的弹窗层级处理
- `getPopupZIndex()`: 获取适合当前设备的弹窗z-index值
- `getMaskZIndex()`: 获取适合当前设备的遮罩层z-index值

### 2. Popup组件优化

更新了 `src/components/Popup/index.tsx`：

- 自动检测荣耀设备的快手小程序环境
- 动态调整z-index值（99999）
- 添加特殊CSS类名处理
- 强制使用fixed定位

### 3. Mask组件优化

更新了 `src/components/Popup/Mask/index.tsx`：

- 同步设备检测逻辑
- 动态调整遮罩层z-index值（99998）
- 添加特殊CSS类名处理

### 4. CSS样式优化

在 `src/app.scss` 中添加全局样式：

```scss
.honor-kwai-fix {
  z-index: 99999 !important;
  position: fixed !important;
  isolation: isolate;
  will-change: transform;
  transform: translateZ(0);
}

.honor-kwai-mask {
  z-index: 99998 !important;
  isolation: isolate;
  transform: translateZ(0);
}
```

### 5. ResumePublishPop组件适配

更新了 `src/components/ResumePublishPop/index.tsx`：

- 集成设备检测功能
- 自动应用特殊z-index值

## 使用方法

### 自动处理

所有现有的Popup组件会自动应用这个修复，无需额外代码修改。

### 手动使用设备检测

```typescript
import { isHonorKwaiDevice, getPopupZIndex } from '@/utils/deviceDetection'

// 在组件中使用
const sysInfo = $.sysInfo()
const isHonorKwai = isHonorKwaiDevice(sysInfo)
const zIndex = getPopupZIndex(sysInfo)

// 应用到自定义弹窗
<CustomPopup zIndex={zIndex} />
```

## 技术细节

### 设备检测逻辑

```typescript
// 检测荣耀设备的多种标识
const brand = sysInfo.systemInfo?.brand?.toLowerCase() || ''
const model = sysInfo.systemInfo?.model?.toLowerCase() || ''

return brand.includes('honor') || 
       model.includes('honor') || 
       brand.includes('荣耀') || 
       model.includes('荣耀') ||
       (brand.includes('huawei') && model.includes('honor'))
```

### z-index策略

- 普通设备：10001（弹窗）、10000（遮罩）
- 荣耀/华为设备的快手小程序：99999（弹窗）、99998（遮罩）

### CSS增强

- `isolation: isolate`: 创建新的层叠上下文
- `will-change: transform`: 提示浏览器优化渲染
- `transform: translateZ(0)`: 强制硬件加速

## 测试建议

1. **荣耀设备测试**：在荣耀手机上的快手小程序中测试弹窗显示
2. **其他设备验证**：确保修改不影响其他设备的正常显示
3. **性能测试**：验证高z-index值不会影响渲染性能

## 注意事项

1. 这个解决方案专门针对快手小程序平台
2. 高z-index值仅在检测到荣耀设备时应用
3. 如果发现其他品牌设备有类似问题，可以扩展设备检测逻辑

## 后续维护

如果发现其他设备品牌有类似问题，可以在 `deviceDetection.ts` 中添加相应的检测逻辑：

```typescript
export const needsSpecialPopupZIndex = (sysInfo?: any): boolean => {
  if (process.env.TARO_ENV !== 'kwai') return false
  
  return isHonorDevice(sysInfo) || 
         isHuaweiDevice(sysInfo) ||
         // 可以在这里添加其他需要特殊处理的设备
         isOtherProblematicDevice(sysInfo)
}
```
