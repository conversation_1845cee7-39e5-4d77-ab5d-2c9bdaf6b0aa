/*
 * @Date: 2024-11-20 14:04:18
  * @Description: 底部悬浮按钮
 */

import cn from 'classnames'
import { Button, StandardProps } from '@tarojs/components'
import { useEffect, useState } from 'react'
import s from './index.module.scss'

type Props = StandardProps & {
  /** loading  */
  loading?: boolean,
  /** 悬浮层级  */
  zIndex?: number | string,
  /** 是否自定义内容  */
  isCustom?: boolean,
  /** 是否置灰禁用按钮 */
  disabled?: boolean,
  /** 地步定位值 */
  bottom?: string,
  /** 自定义按钮class样式 */
  customBtn?: StandardProps['className']
  /** 自定义底部slot */
  childrenBottom?: StandardProps['children']
  /** 按钮类型 - 默认-primary  */
  type?: 'primary' | 'default' | 'ghost'
}

export default (props: Props) => {
  const { loading = false,
    zIndex = 10,
    isCustom = false,
    disabled = false,
    bottom = '',
    type = 'primary',
    ...other
  } = props

  const [bottomNum, setBottomNum] = useState(0)
  useEffect(() => {
    setBottomNum(parseInt(bottom, 10) || 0)
  }, [bottom])

  const onClick = (e) => {
    if (loading || disabled) {
      return
    }
    props.onClick && props.onClick(e)
  }

  return (
    <V className={s.footerContainer}>
      <V
        className={cn(s.footerBtn, props.className)}
        style={{ bottom: bottom || '0px', zIndex }}
      >
        {isCustom
          ? other.children
          : <Button onClick={onClick}
            loading={loading}
            type={type}
            className={cn(s.footerCont, s[type], props.customBtn, disabled && s.disabled)}
          >
            {other.children}
          </Button>
        }
        {other.childrenBottom}
        {bottomNum < 10 && <V className={s.footerStripes}></V>}
      </V>
      <V className={s.footerFill}>
        <V className={s.footerCont}></V>
        <V className={s.footerStripes}></V>
      </V>
    </V>
  )
}
