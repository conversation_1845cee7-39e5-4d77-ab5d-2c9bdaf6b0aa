diff --git a/node_modules/@tarojs/plugin-platform-kwai/dist/index.js b/node_modules/@tarojs/plugin-platform-kwai/dist/index.js
new file mode 100644
index 0000000..07fd018
--- /dev/null
+++ b/node_modules/@tarojs/plugin-platform-kwai/dist/index.js
@@ -0,0 +1,293 @@
+'use strict';
+
+Object.defineProperty(exports, '__esModule', { value: true });
+
+var service = require('@tarojs/service');
+var shared = require('@tarojs/shared');
+var template = require('@tarojs/shared/dist/template');
+
+/*! *****************************************************************************
+Copyright (c) Microsoft Corporation.
+
+Permission to use, copy, modify, and/or distribute this software for any
+purpose with or without fee is hereby granted.
+
+THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH
+REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY
+AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,
+INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM
+LOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR
+OTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR
+PERFORMANCE OF THIS SOFTWARE.
+***************************************************************************** */
+
+function __awaiter(thisArg, _arguments, P, generator) {
+    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
+    return new (P || (P = Promise))(function (resolve, reject) {
+        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
+        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
+        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
+        step((generator = generator.apply(thisArg, _arguments || [])).next());
+    });
+}
+
+class Template extends template.RecursiveTemplate {
+    constructor() {
+        super(...arguments);
+        this.flattenViewLevel = 8;
+        this.flattenCoverViewLevel = 8;
+        this.flattenTextLevel = 3;
+        this.supportXS = false;
+        this.Adapter = {
+            if: 'ks:if',
+            else: 'ks:else',
+            elseif: 'ks:elif',
+            for: 'ks:for',
+            forItem: 'ks:for-item',
+            forIndex: 'ks:for-index',
+            key: 'ks:key',
+            type: 'kwai'
+        };
+        this.buildFlattenView = (level = this.flattenViewLevel) => {
+            if (level === 0) {
+                return `<template is="{{'tmpl_0_' + item.nn}}" data="{{i:item}}" />`;
+            }
+            const child = this.buildFlattenView(level - 1);
+            const componentsAlias = this.componentsAlias;
+            const viewAlias = componentsAlias.view._num;
+            const textAlias = componentsAlias.text._num;
+            const staticTextAlias = componentsAlias['static-text']._num;
+            const buttonAlias = componentsAlias.button._num;
+            const inputAlias = componentsAlias.input._num;
+            const swiperAlias = componentsAlias.swiper._num;
+            const template = `<view ks:if="{{item.nn==='${viewAlias}'&&(item.st||item.cl)}}" id="{{item.uid}}" ${this.buildFlattenNodeAttributes('view')}>
+  <block ks:for="{{item.cn}}" ks:key="uid">
+    ${shared.indent(child, 4)}
+  </block>
+</view>
+<text ks:elif="{{item.nn==='${textAlias}'&&(item.st||item.cl)}}" id="{{item.uid}}" ${this.buildFlattenNodeAttributes('text')}>
+  <block ks:for="{{item.cn}}" ks:key="uid">
+    <block>{{item.v}}</block>
+  </block>
+</text>
+<text ks:elif="{{item.nn==='${staticTextAlias}'&&(item.st||item.cl)}}" id="{{item.uid}}" ${this.buildFlattenNodeAttributes('static-text')}>
+  <block ks:for="{{item.cn}}" ks:key="uid">
+    <block>{{item.v}}</block>
+  </block>
+</text>
+<button ks:elif="{{item.nn==='${buttonAlias}'&&(item.st||item.cl)}}" id="{{item.uid}}" ${this.buildFlattenNodeAttributes('button')}>
+  <block ks:for="{{item.cn}}" ks:key="uid">
+    <template is="{{'tmpl_0_' + item.nn}}" data="{{i:item}}" />
+  </block>
+</button>
+<input ks:elif="{{item.nn==='${inputAlias}'&&(item.st||item.cl)}}" id="{{item.uid}}" ${this.buildFlattenNodeAttributes('input')} />
+<swiper ks:elif="{{item.nn==='${swiperAlias}'&&(item.st||item.cl)}}" id="{{item.uid}}" ${this.buildFlattenNodeAttributes('swiper')}>
+  <block ks:for="{{item.cn}}" ks:key="uid">
+    <template is="{{'tmpl_0_' + item.nn}}" data="{{i:item}}" />
+  </block>
+</swiper>
+<block ks:else>
+  <template is="{{'tmpl_0_' + item.nn}}" data="{{i:item}}" />
+</block>`;
+            return template;
+        };
+        this.buildFlattenCoverView = (level = this.flattenCoverViewLevel) => {
+            if (level === 0) {
+                return '';
+            }
+            const child = this.buildFlattenCoverView(level - 1);
+            const componentsAlias = this.componentsAlias;
+            const coverViewAlias = componentsAlias['cover-view']._num;
+            const coverImageAlias = componentsAlias['cover-image']._num;
+            const buttonAlias = componentsAlias.button._num;
+            const contentAlias = componentsAlias['#text']._num;
+            const template = `${level - 1 !== 0 ? `<cover-view ks:if="{{item.nn==='${coverViewAlias}'}}" ${this.buildFlattenNodeAttributes('cover-view')}>
+  <block ks:for="{{item.cn}}" ks:key="uid">
+    ${shared.indent(child, 4)}
+  </block>
+</cover-view>` : ''}
+<button ks:elif="{{item.nn==='${buttonAlias}'}}" ${this.buildFlattenNodeAttributes('button')} >
+  <block ks:for="{{item.cn}}" ks:key="uid">
+    <template is="{{'tmpl_0_' + item.nn}}" data="{{i:item}}" />
+  </block>
+</button>
+<cover-image ks:elif="{{item.nn==='${coverImageAlias}'}}" ${this.buildFlattenNodeAttributes('cover-image')} />
+<block ks:elif="{{item.nn==='${contentAlias}'}}">{{item.v}}</block>`;
+            return template;
+        };
+        this.buildFlattenText = (level = this.flattenTextLevel) => {
+            if (level === 0) {
+                return `<block>{{i.${"cn" /* Shortcuts.Childnodes */}[index].${"v" /* Shortcuts.Text */}}}</block>`;
+            }
+            const child = this.buildFlattenText(level - 1);
+            const componentsAlias = this.componentsAlias;
+            const contentAlias = componentsAlias['#text']._num;
+            const template = `<block ks:if="item.nn === '${contentAlias}'">{{item.v}}</block><text ks:else id="{{item.uid}}" ${this.buildFlattenNodeAttributes('text')}>
+  <block ks:for="{{item.cn}}" ks:key="uid">
+    ${shared.indent(child, 4)}
+  </block>
+</text>`;
+            return template;
+        };
+        this.modifyLoopBody = (child, nodeName) => {
+            switch (nodeName) {
+                case 'view':
+                    return this.buildFlattenView();
+                case 'text':
+                case 'static-text':
+                    return this.buildFlattenText();
+                case 'cover-view':
+                    return this.buildFlattenCoverView();
+                default:
+                    return child;
+            }
+        };
+    }
+    createMiniComponents(components) {
+        const result = super.createMiniComponents(components);
+        delete result['pure-view'];
+        delete result['static-view'];
+        return result;
+    }
+    buildFlattenNodeAttributes(nodeName) {
+        const component = this.miniComponents[nodeName];
+        return Object.keys(component)
+            .map(k => {
+                const value = component[k];
+                if (k.startsWith('bind') || k.startsWith('on') || k.startsWith('catch')) {
+                    return `${k}="${value}"`;
+                } else {
+                    // 修复属性替换逻辑，确保 item. 后面有属性名
+                    const safeValue = value.replace(/i\./g, 'item.');
+                    return `${k}="{{${safeValue}}}"`;
+                }
+            })
+            .join(' ') + ' id="{{item.uid||item.sid}}" data-sid="{{item.sid}}"';
+    }
+}
+
+const components = {
+    // ======== 调整属性 ========
+    Button: {
+        bindGetPhoneNumber: '',
+        bindGetUserInfo: '',
+        bindContact: '',
+        bindError: '',
+        bindOpenSetting: '',
+        bindLaunchApp: '',
+    },
+    Slider: {
+        color: shared.singleQuote('#e9e9e9'),
+        'selected-color': shared.singleQuote('#1aad19')
+    },
+    Ad: {
+        type: ''
+    },
+    Swiper: {
+        'easing-function': shared.singleQuote('default'),
+    },
+    RichText: {
+        space: ''
+    }
+};
+
+const PACKAGE_NAME = '@tarojs/plugin-platform-kwai';
+class Kwai extends service.TaroPlatformBase {
+    /**
+     * 1. setupTransaction - init
+     * 2. setup
+     * 3. setupTransaction - close
+     * 4. buildTransaction - init
+     * 5. build
+     * 6. buildTransaction - close
+     */
+    constructor(ctx, config) {
+        super(ctx, config);
+        this.platform = 'kwai';
+        this.globalObject = 'ks';
+        this.runtimePath = `${PACKAGE_NAME}/dist/runtime`;
+        this.fileType = {
+            templ: '.ksml',
+            style: '.css',
+            config: '.json',
+            script: '.js'
+        };
+        this.template = new Template();
+        this.setupTransaction.addWrapper({
+            close() {
+                this.modifyTemplate();
+                this.generateProjectConfig('project.ks.json');
+                this.generateProjectConfig('project.kwai.json');
+            }
+        });
+    }
+    /**
+     * 增加组件或修改组件属性
+     */
+    modifyTemplate() {
+        const template = this.template;
+        template.mergeComponents(this.ctx, components);
+        this.modifyInput(template.internalComponents.Input);
+    }
+    /**
+     * 修改 Input 组件属性
+     */
+    modifyInput(input) {
+        delete input['placeholder-class'];
+        delete input['cursor-spacing'];
+        delete input['confirm-hold'];
+        delete input['cursor'];
+        delete input['selection-start'];
+        delete input['selection-end'];
+    }
+}
+
+let registedModifyPageTemplate = false;
+var index = (ctx) => {
+    ctx.registerPlatform({
+        name: 'kwai',
+        useConfigName: 'mini',
+        fn({ config }) {
+            return __awaiter(this, void 0, void 0, function* () {
+                !registedModifyPageTemplate && modifyPageTemplate(ctx);
+                const program = new Kwai(ctx, config);
+                yield program.start();
+            });
+        }
+    });
+};
+// 和支付宝小程序一样，快手小程序中，如果某个页面依赖了原生小程序组件，
+// 那么这个页面不能使用公共模板 base.axml，
+// 而需要把公共模板的内容在此页面的模板中复制一份, 。
+function modifyPageTemplate(ctx) {
+    registedModifyPageTemplate = true;
+    ctx.modifyBuildAssets(({ assets, miniPlugin }) => {
+        const pages = [];
+        // 筛选出使用了自定义组件的页面
+        miniPlugin.pages.forEach(page => {
+            const config = miniPlugin.filesConfig[miniPlugin.getConfigFilePath(page.name)].content;
+            if (!page.isNative && (config === null || config === void 0 ? void 0 : config.hasOwnProperty('usingComponents')) && Object.keys(config.usingComponents).length) {
+                pages.push(page.name);
+            }
+        });
+        if (!pages.length)
+            return;
+        const baseXml = assets['base.ksml'].source();
+        pages.forEach(page => {
+            const templateName = `${page}.ksml`;
+            const assetsItem = assets[templateName];
+            const src = assetsItem._value ? assetsItem._value.toString() : assetsItem.source();
+            const templateCaller = src.replace(/<import src="(.*)base\.ksml"\/>/, '');
+            const res = `${templateCaller}
+${baseXml}`;
+            assets[templateName] = {
+                size: () => res.length,
+                source: () => res
+            };
+        });
+    });
+}
+
+exports.Kwai = Kwai;
+exports["default"] = index;
+//# sourceMappingURL=index.js.map
diff --git a/node_modules/@tarojs/plugin-platform-kwai/dist/index.js.map b/node_modules/@tarojs/plugin-platform-kwai/dist/index.js.map
new file mode 100644
index 0000000..2a63bc2
--- /dev/null
+++ b/node_modules/@tarojs/plugin-platform-kwai/dist/index.js.map
@@ -0,0 +1 @@
+{"version":3,"file":"index.js","sources":["../src/template.ts","../src/components.ts","../src/program.ts","../src/index.ts"],"sourcesContent":["import { indent, Shortcuts } from '@tarojs/shared'\nimport { RecursiveTemplate } from '@tarojs/shared/dist/template'\n\nexport class Template extends RecursiveTemplate {\n  flattenViewLevel = 8\n  flattenCoverViewLevel = 8\n  flattenTextLevel = 3\n  supportXS = false\n  Adapter = {\n    if: 'ks:if',\n    else: 'ks:else',\n    elseif: 'ks:elif',\n    for: 'ks:for',\n    forItem: 'ks:for-item',\n    forIndex: 'ks:for-index',\n    key: 'ks:key',\n    type: 'kwai'\n  }\n\n  createMiniComponents (components): any {\n    const result = super.createMiniComponents(components)\n\n    delete result['pure-view']\n    delete result['static-view']\n\n    return result\n  }\n\n  buildFlattenNodeAttributes (nodeName: string): string {\n    const component = this.miniComponents[nodeName]\n\n    return Object.keys(component)\n      .map(k => `${k}=\"${k.startsWith('bind') || k.startsWith('on') || k.startsWith('catch') ? component[k] : `{{${component[k].replace(/i./g, 'item.')}}}`}\"`)\n      .join(' ') + ' id=\"{{item.uid||item.sid}}\" data-sid=\"{{item.sid}}\"'\n  }\n\n  buildFlattenView = (level = this.flattenViewLevel): string => {\n    if (level === 0) {\n      return `<template is=\"{{'tmpl_0_' + item.nn}}\" data=\"{{i:item}}\" />`\n    }\n\n    const child = this.buildFlattenView(level - 1)\n\n    const componentsAlias = this.componentsAlias\n    const viewAlias = componentsAlias.view._num\n    const textAlias = componentsAlias.text._num\n    const staticTextAlias = componentsAlias['static-text']._num\n    const buttonAlias = componentsAlias.button._num\n    const inputAlias = componentsAlias.input._num\n    const swiperAlias = componentsAlias.swiper._num\n\n    const template =\n`<view ks:if=\"{{item.nn==='${viewAlias}'&&(item.st||item.cl)}}\" id=\"{{item.uid}}\" ${this.buildFlattenNodeAttributes('view')}>\n  <block ks:for=\"{{item.cn}}\" ks:key=\"uid\">\n    ${indent(child, 4)}\n  </block>\n</view>\n<text ks:elif=\"{{item.nn==='${textAlias}'&&(item.st||item.cl)}}\" id=\"{{item.uid}}\" ${this.buildFlattenNodeAttributes('text')}>\n  <block ks:for=\"{{item.cn}}\" ks:key=\"uid\">\n    <block>{{item.v}}</block>\n  </block>\n</text>\n<text ks:elif=\"{{item.nn==='${staticTextAlias}'&&(item.st||item.cl)}}\" id=\"{{item.uid}}\" ${this.buildFlattenNodeAttributes('static-text')}>\n  <block ks:for=\"{{item.cn}}\" ks:key=\"uid\">\n    <block>{{item.v}}</block>\n  </block>\n</text>\n<button ks:elif=\"{{item.nn==='${buttonAlias}'&&(item.st||item.cl)}}\" id=\"{{item.uid}}\" ${this.buildFlattenNodeAttributes('button')}>\n  <block ks:for=\"{{item.cn}}\" ks:key=\"uid\">\n    <template is=\"{{'tmpl_0_' + item.nn}}\" data=\"{{i:item}}\" />\n  </block>\n</button>\n<input ks:elif=\"{{item.nn==='${inputAlias}'&&(item.st||item.cl)}}\" id=\"{{item.uid}}\" ${this.buildFlattenNodeAttributes('input')} />\n<swiper ks:elif=\"{{item.nn==='${swiperAlias}'&&(item.st||item.cl)}}\" id=\"{{item.uid}}\" ${this.buildFlattenNodeAttributes('swiper')}>\n  <block ks:for=\"{{item.cn}}\" ks:key=\"uid\">\n    <template is=\"{{'tmpl_0_' + item.nn}}\" data=\"{{i:item}}\" />\n  </block>\n</swiper>\n<block ks:else>\n  <template is=\"{{'tmpl_0_' + item.nn}}\" data=\"{{i:item}}\" />\n</block>`\n\n    return template\n  }\n\n  buildFlattenCoverView = (level = this.flattenCoverViewLevel): string => {\n    if (level === 0) {\n      return ''\n    }\n\n    const child = this.buildFlattenCoverView(level - 1)\n\n    const componentsAlias = this.componentsAlias\n    const coverViewAlias = componentsAlias['cover-view']._num\n    const coverImageAlias = componentsAlias['cover-image']._num\n    const buttonAlias = componentsAlias.button._num\n    const contentAlias = componentsAlias['#text']._num\n\n    const template =\n  `${level - 1 !== 0 ? `<cover-view ks:if=\"{{item.nn==='${coverViewAlias}'}}\" ${this.buildFlattenNodeAttributes('cover-view')}>\n  <block ks:for=\"{{item.cn}}\" ks:key=\"uid\">\n    ${indent(child, 4)}\n  </block>\n</cover-view>` : ''}\n<button ks:elif=\"{{item.nn==='${buttonAlias}'}}\" ${this.buildFlattenNodeAttributes('button')} >\n  <block ks:for=\"{{item.cn}}\" ks:key=\"uid\">\n    <template is=\"{{'tmpl_0_' + item.nn}}\" data=\"{{i:item}}\" />\n  </block>\n</button>\n<cover-image ks:elif=\"{{item.nn==='${coverImageAlias}'}}\" ${this.buildFlattenNodeAttributes('cover-image')} />\n<block ks:elif=\"{{item.nn==='${contentAlias}'}}\">{{item.v}}</block>`\n\n    return template\n  }\n\n  buildFlattenText = (level = this.flattenTextLevel): string => {\n    if (level === 0) {\n      return `<block>{{i.${Shortcuts.Childnodes}[index].${Shortcuts.Text}}}</block>`\n    }\n\n    const child = this.buildFlattenText(level - 1)\n\n    const componentsAlias = this.componentsAlias\n    const contentAlias = componentsAlias['#text']._num\n\n    const template =\n`<block ks:if=\"item.nn === '${contentAlias}'\">{{item.v}}</block><text ks:else id=\"{{item.uid}}\" ${this.buildFlattenNodeAttributes('text')}>\n  <block ks:for=\"{{item.cn}}\" ks:key=\"uid\">\n    ${indent(child, 4)}\n  </block>\n</text>`\n    return template\n  }\n\n  modifyLoopBody = (child: string, nodeName: string): string => {\n    switch (nodeName) {\n      case 'view':\n        return this.buildFlattenView()\n\n      case 'text':\n      case 'static-text':\n        return this.buildFlattenText()\n\n      case 'cover-view':\n        return this.buildFlattenCoverView()\n\n      default:\n        return child\n    }\n  }\n}\n","import { singleQuote } from '@tarojs/shared'\n\nexport const components = {\n  // ======== 调整属性 ========\n  Button: {\n    bindGetPhoneNumber: '',\n    bindGetUserInfo: '',\n    bindContact: '',\n    bindError: '',\n    bindOpenSetting: '',\n    bindLaunchApp: '',\n  },\n  Slider: {\n    color: singleQuote('#e9e9e9'),\n    'selected-color': singleQuote('#1aad19')\n  },\n  Ad: {\n    type: ''\n  },\n  Swiper: {\n    'easing-function': singleQuote('default'),\n  },\n  RichText: {\n    space: ''\n  }\n}\n","import { TaroPlatformBase } from '@tarojs/service'\nimport { Template } from './template'\nimport { components } from './components'\n\nconst PACKAGE_NAME = '@tarojs/plugin-platform-kwai'\n\nexport default class Kwai extends TaroPlatformBase {\n  platform = 'kwai'\n  globalObject = 'ks'\n  runtimePath = `${PACKAGE_NAME}/dist/runtime`\n  fileType = {\n    templ: '.ksml',\n    style: '.css',\n    config: '.json',\n    script: '.js'\n  }\n\n  template = new Template()\n\n  /**\n   * 1. setupTransaction - init\n   * 2. setup\n   * 3. setupTransaction - close\n   * 4. buildTransaction - init\n   * 5. build\n   * 6. buildTransaction - close\n   */\n  constructor (ctx, config) {\n    super(ctx, config)\n\n    this.setupTransaction.addWrapper({\n      close () {\n        this.modifyTemplate()\n        this.generateProjectConfig('project.ks.json')\n        this.generateProjectConfig('project.kwai.json')\n      }\n    })\n  }\n\n  /**\n   * 增加组件或修改组件属性\n   */\n  modifyTemplate () {\n    const template = this.template\n    template.mergeComponents(this.ctx, components)\n    this.modifyInput(template.internalComponents.Input)\n  }\n\n  /**\n   * 修改 Input 组件属性\n   */\n  modifyInput (input) {\n    delete input['placeholder-class']\n    delete input['cursor-spacing']\n    delete input['confirm-hold']\n    delete input['cursor']\n    delete input['selection-start']\n    delete input['selection-end']\n  }\n}\n","import Kwai from './program'\nimport type { IPluginContext } from '@tarojs/service'\n\n// 让其它平台插件可以继承此平台\nexport { Kwai }\n\nlet registedModifyPageTemplate = false\n\nexport default (ctx: IPluginContext) => {\n  ctx.registerPlatform({\n    name: 'kwai',\n    useConfigName: 'mini',\n    async fn ({ config }) {\n      !registedModifyPageTemplate && modifyPageTemplate(ctx)\n      const program = new Kwai(ctx, config)\n      await program.start()\n    }\n  })\n}\n\n// 和支付宝小程序一样，快手小程序中，如果某个页面依赖了原生小程序组件，\n// 那么这个页面不能使用公共模板 base.axml，\n// 而需要把公共模板的内容在此页面的模板中复制一份, 。\nfunction modifyPageTemplate (ctx: IPluginContext) {\n  registedModifyPageTemplate = true\n  ctx.modifyBuildAssets(({ assets, miniPlugin }) => {\n    const pages: string[] = []\n\n    // 筛选出使用了自定义组件的页面\n    miniPlugin.pages.forEach(page => {\n      const config = miniPlugin.filesConfig[miniPlugin.getConfigFilePath(page.name)].content\n      if (!page.isNative && config?.hasOwnProperty('usingComponents') && Object.keys(config.usingComponents).length) {\n        pages.push(page.name)\n      }\n    })\n\n    if (!pages.length) return\n\n    const baseXml = assets['base.ksml'].source()\n\n    pages.forEach(page => {\n      const templateName = `${page}.ksml`\n      const assetsItem = assets[templateName]\n      const src = assetsItem._value ? assetsItem._value.toString() : assetsItem.source()\n      const templateCaller = src.replace(/<import src=\"(.*)base\\.ksml\"\\/>/, '')\n      const res = `${templateCaller}\n${baseXml}`\n\n      assets[templateName] = {\n        size: () => res.length,\n        source: () => res\n      }\n    })\n  })\n}\n\n"],"names":["RecursiveTemplate","indent","singleQuote","TaroPlatformBase"],"mappings":";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAGM,MAAO,QAAS,SAAQA,0BAAiB,CAAA;AAA/C,IAAA,WAAA,GAAA;;QACE,IAAgB,CAAA,gBAAA,GAAG,CAAC,CAAA;QACpB,IAAqB,CAAA,qBAAA,GAAG,CAAC,CAAA;QACzB,IAAgB,CAAA,gBAAA,GAAG,CAAC,CAAA;QACpB,IAAS,CAAA,SAAA,GAAG,KAAK,CAAA;AACjB,QAAA,IAAA,CAAA,OAAO,GAAG;AACR,YAAA,EAAE,EAAE,OAAO;AACX,YAAA,IAAI,EAAE,SAAS;AACf,YAAA,MAAM,EAAE,SAAS;AACjB,YAAA,GAAG,EAAE,QAAQ;AACb,YAAA,OAAO,EAAE,aAAa;AACtB,YAAA,QAAQ,EAAE,cAAc;AACxB,YAAA,GAAG,EAAE,QAAQ;AACb,YAAA,IAAI,EAAE,MAAM;SACb,CAAA;QAmBD,IAAgB,CAAA,gBAAA,GAAG,CAAC,KAAK,GAAG,IAAI,CAAC,gBAAgB,KAAY;YAC3D,IAAI,KAAK,KAAK,CAAC,EAAE;AACf,gBAAA,OAAO,6DAA6D,CAAA;AACrE,aAAA;YAED,MAAM,KAAK,GAAG,IAAI,CAAC,gBAAgB,CAAC,KAAK,GAAG,CAAC,CAAC,CAAA;AAE9C,YAAA,MAAM,eAAe,GAAG,IAAI,CAAC,eAAe,CAAA;AAC5C,YAAA,MAAM,SAAS,GAAG,eAAe,CAAC,IAAI,CAAC,IAAI,CAAA;AAC3C,YAAA,MAAM,SAAS,GAAG,eAAe,CAAC,IAAI,CAAC,IAAI,CAAA;YAC3C,MAAM,eAAe,GAAG,eAAe,CAAC,aAAa,CAAC,CAAC,IAAI,CAAA;AAC3D,YAAA,MAAM,WAAW,GAAG,eAAe,CAAC,MAAM,CAAC,IAAI,CAAA;AAC/C,YAAA,MAAM,UAAU,GAAG,eAAe,CAAC,KAAK,CAAC,IAAI,CAAA;AAC7C,YAAA,MAAM,WAAW,GAAG,eAAe,CAAC,MAAM,CAAC,IAAI,CAAA;YAE/C,MAAM,QAAQ,GAClB,CAAA,0BAAA,EAA6B,SAAS,CAAA,2CAAA,EAA8C,IAAI,CAAC,0BAA0B,CAAC,MAAM,CAAC,CAAA;;AAErH,IAAA,EAAAC,aAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAA;;;AAGQ,4BAAA,EAAA,SAAS,8CAA8C,IAAI,CAAC,0BAA0B,CAAC,MAAM,CAAC,CAAA;;;;;AAK9F,4BAAA,EAAA,eAAe,8CAA8C,IAAI,CAAC,0BAA0B,CAAC,aAAa,CAAC,CAAA;;;;;AAKzG,8BAAA,EAAA,WAAW,8CAA8C,IAAI,CAAC,0BAA0B,CAAC,QAAQ,CAAC,CAAA;;;;;AAKnG,6BAAA,EAAA,UAAU,8CAA8C,IAAI,CAAC,0BAA0B,CAAC,OAAO,CAAC,CAAA;AAC/F,8BAAA,EAAA,WAAW,8CAA8C,IAAI,CAAC,0BAA0B,CAAC,QAAQ,CAAC,CAAA;;;;;;;SAOzH,CAAA;AAEL,YAAA,OAAO,QAAQ,CAAA;AACjB,SAAC,CAAA;QAED,IAAqB,CAAA,qBAAA,GAAG,CAAC,KAAK,GAAG,IAAI,CAAC,qBAAqB,KAAY;YACrE,IAAI,KAAK,KAAK,CAAC,EAAE;AACf,gBAAA,OAAO,EAAE,CAAA;AACV,aAAA;YAED,MAAM,KAAK,GAAG,IAAI,CAAC,qBAAqB,CAAC,KAAK,GAAG,CAAC,CAAC,CAAA;AAEnD,YAAA,MAAM,eAAe,GAAG,IAAI,CAAC,eAAe,CAAA;YAC5C,MAAM,cAAc,GAAG,eAAe,CAAC,YAAY,CAAC,CAAC,IAAI,CAAA;YACzD,MAAM,eAAe,GAAG,eAAe,CAAC,aAAa,CAAC,CAAC,IAAI,CAAA;AAC3D,YAAA,MAAM,WAAW,GAAG,eAAe,CAAC,MAAM,CAAC,IAAI,CAAA;YAC/C,MAAM,YAAY,GAAG,eAAe,CAAC,OAAO,CAAC,CAAC,IAAI,CAAA;AAElD,YAAA,MAAM,QAAQ,GAChB,CAAA,EAAG,KAAK,GAAG,CAAC,KAAK,CAAC,GAAG,CAAA,gCAAA,EAAmC,cAAc,CAAQ,KAAA,EAAA,IAAI,CAAC,0BAA0B,CAAC,YAAY,CAAC,CAAA;;AAEvH,IAAA,EAAAA,aAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAA;;cAER,GAAG,EAAE,CAAA;AACa,8BAAA,EAAA,WAAW,QAAQ,IAAI,CAAC,0BAA0B,CAAC,QAAQ,CAAC,CAAA;;;;;AAKvD,mCAAA,EAAA,eAAe,QAAQ,IAAI,CAAC,0BAA0B,CAAC,aAAa,CAAC,CAAA;AAC3E,6BAAA,EAAA,YAAY,yBAAyB,CAAA;AAEhE,YAAA,OAAO,QAAQ,CAAA;AACjB,SAAC,CAAA;QAED,IAAgB,CAAA,gBAAA,GAAG,CAAC,KAAK,GAAG,IAAI,CAAC,gBAAgB,KAAY;YAC3D,IAAI,KAAK,KAAK,CAAC,EAAE;AACf,gBAAA,OAAO,CAAc,WAAA,EAAA,IAAA,4BAA+B,QAAA,EAAA,GAAA,iCAA0B,CAAA;AAC/E,aAAA;YAED,MAAM,KAAK,GAAG,IAAI,CAAC,gBAAgB,CAAC,KAAK,GAAG,CAAC,CAAC,CAAA;AAE9C,YAAA,MAAM,eAAe,GAAG,IAAI,CAAC,eAAe,CAAA;YAC5C,MAAM,YAAY,GAAG,eAAe,CAAC,OAAO,CAAC,CAAC,IAAI,CAAA;YAElD,MAAM,QAAQ,GAClB,CAAA,2BAAA,EAA8B,YAAY,CAAA,qDAAA,EAAwD,IAAI,CAAC,0BAA0B,CAAC,MAAM,CAAC,CAAA;;AAEnI,IAAA,EAAAA,aAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAA;;QAEd,CAAA;AACJ,YAAA,OAAO,QAAQ,CAAA;AACjB,SAAC,CAAA;AAED,QAAA,IAAA,CAAA,cAAc,GAAG,CAAC,KAAa,EAAE,QAAgB,KAAY;AAC3D,YAAA,QAAQ,QAAQ;AACd,gBAAA,KAAK,MAAM;AACT,oBAAA,OAAO,IAAI,CAAC,gBAAgB,EAAE,CAAA;AAEhC,gBAAA,KAAK,MAAM,CAAC;AACZ,gBAAA,KAAK,aAAa;AAChB,oBAAA,OAAO,IAAI,CAAC,gBAAgB,EAAE,CAAA;AAEhC,gBAAA,KAAK,YAAY;AACf,oBAAA,OAAO,IAAI,CAAC,qBAAqB,EAAE,CAAA;AAErC,gBAAA;AACE,oBAAA,OAAO,KAAK,CAAA;AACf,aAAA;AACH,SAAC,CAAA;KACF;AAnIC,IAAA,oBAAoB,CAAE,UAAU,EAAA;QAC9B,MAAM,MAAM,GAAG,KAAK,CAAC,oBAAoB,CAAC,UAAU,CAAC,CAAA;AAErD,QAAA,OAAO,MAAM,CAAC,WAAW,CAAC,CAAA;AAC1B,QAAA,OAAO,MAAM,CAAC,aAAa,CAAC,CAAA;AAE5B,QAAA,OAAO,MAAM,CAAA;KACd;AAED,IAAA,0BAA0B,CAAE,QAAgB,EAAA;QAC1C,MAAM,SAAS,GAAG,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAA;AAE/C,QAAA,OAAO,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC;aAC1B,GAAG,CAAC,CAAC,IAAI,GAAG,CAAC,CAAA,EAAA,EAAK,CAAC,CAAC,UAAU,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,UAAU,CAAC,OAAO,CAAC,GAAG,SAAS,CAAC,CAAC,CAAC,GAAG,CAAK,EAAA,EAAA,SAAS,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,KAAK,EAAE,OAAO,CAAC,CAAI,EAAA,CAAA,CAAA,CAAA,CAAG,CAAC;AACxJ,aAAA,IAAI,CAAC,GAAG,CAAC,GAAG,sDAAsD,CAAA;KACtE;AAoHF;;ACpJM,MAAM,UAAU,GAAG;;AAExB,IAAA,MAAM,EAAE;AACN,QAAA,kBAAkB,EAAE,EAAE;AACtB,QAAA,eAAe,EAAE,EAAE;AACnB,QAAA,WAAW,EAAE,EAAE;AACf,QAAA,SAAS,EAAE,EAAE;AACb,QAAA,eAAe,EAAE,EAAE;AACnB,QAAA,aAAa,EAAE,EAAE;AAClB,KAAA;AACD,IAAA,MAAM,EAAE;AACN,QAAA,KAAK,EAAEC,kBAAW,CAAC,SAAS,CAAC;AAC7B,QAAA,gBAAgB,EAAEA,kBAAW,CAAC,SAAS,CAAC;AACzC,KAAA;AACD,IAAA,EAAE,EAAE;AACF,QAAA,IAAI,EAAE,EAAE;AACT,KAAA;AACD,IAAA,MAAM,EAAE;AACN,QAAA,iBAAiB,EAAEA,kBAAW,CAAC,SAAS,CAAC;AAC1C,KAAA;AACD,IAAA,QAAQ,EAAE;AACR,QAAA,KAAK,EAAE,EAAE;AACV,KAAA;CACF;;ACrBD,MAAM,YAAY,GAAG,8BAA8B,CAAA;AAE9B,MAAA,IAAK,SAAQC,wBAAgB,CAAA;AAahD;;;;;;;AAOG;IACH,WAAa,CAAA,GAAG,EAAE,MAAM,EAAA;AACtB,QAAA,KAAK,CAAC,GAAG,EAAE,MAAM,CAAC,CAAA;QArBpB,IAAQ,CAAA,QAAA,GAAG,MAAM,CAAA;QACjB,IAAY,CAAA,YAAA,GAAG,IAAI,CAAA;AACnB,QAAA,IAAA,CAAA,WAAW,GAAG,CAAA,EAAG,YAAY,CAAA,aAAA,CAAe,CAAA;AAC5C,QAAA,IAAA,CAAA,QAAQ,GAAG;AACT,YAAA,KAAK,EAAE,OAAO;AACd,YAAA,KAAK,EAAE,MAAM;AACb,YAAA,MAAM,EAAE,OAAO;AACf,YAAA,MAAM,EAAE,KAAK;SACd,CAAA;AAED,QAAA,IAAA,CAAA,QAAQ,GAAG,IAAI,QAAQ,EAAE,CAAA;AAavB,QAAA,IAAI,CAAC,gBAAgB,CAAC,UAAU,CAAC;YAC/B,KAAK,GAAA;gBACH,IAAI,CAAC,cAAc,EAAE,CAAA;AACrB,gBAAA,IAAI,CAAC,qBAAqB,CAAC,iBAAiB,CAAC,CAAA;AAC7C,gBAAA,IAAI,CAAC,qBAAqB,CAAC,mBAAmB,CAAC,CAAA;aAChD;AACF,SAAA,CAAC,CAAA;KACH;AAED;;AAEG;IACH,cAAc,GAAA;AACZ,QAAA,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAA;QAC9B,QAAQ,CAAC,eAAe,CAAC,IAAI,CAAC,GAAG,EAAE,UAAU,CAAC,CAAA;QAC9C,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,kBAAkB,CAAC,KAAK,CAAC,CAAA;KACpD;AAED;;AAEG;AACH,IAAA,WAAW,CAAE,KAAK,EAAA;AAChB,QAAA,OAAO,KAAK,CAAC,mBAAmB,CAAC,CAAA;AACjC,QAAA,OAAO,KAAK,CAAC,gBAAgB,CAAC,CAAA;AAC9B,QAAA,OAAO,KAAK,CAAC,cAAc,CAAC,CAAA;AAC5B,QAAA,OAAO,KAAK,CAAC,QAAQ,CAAC,CAAA;AACtB,QAAA,OAAO,KAAK,CAAC,iBAAiB,CAAC,CAAA;AAC/B,QAAA,OAAO,KAAK,CAAC,eAAe,CAAC,CAAA;KAC9B;AACF;;ACrDD,IAAI,0BAA0B,GAAG,KAAK,CAAA;AAEtC,YAAe,CAAC,GAAmB,KAAI;IACrC,GAAG,CAAC,gBAAgB,CAAC;AACnB,QAAA,IAAI,EAAE,MAAM;AACZ,QAAA,aAAa,EAAE,MAAM;QACf,EAAE,CAAE,EAAE,MAAM,EAAE,EAAA;;AAClB,gBAAA,CAAC,0BAA0B,IAAI,kBAAkB,CAAC,GAAG,CAAC,CAAA;gBACtD,MAAM,OAAO,GAAG,IAAI,IAAI,CAAC,GAAG,EAAE,MAAM,CAAC,CAAA;AACrC,gBAAA,MAAM,OAAO,CAAC,KAAK,EAAE,CAAA;aACtB,CAAA,CAAA;AAAA,SAAA;AACF,KAAA,CAAC,CAAA;AACJ,CAAC,CAAA;AAED;AACA;AACA;AACA,SAAS,kBAAkB,CAAE,GAAmB,EAAA;IAC9C,0BAA0B,GAAG,IAAI,CAAA;IACjC,GAAG,CAAC,iBAAiB,CAAC,CAAC,EAAE,MAAM,EAAE,UAAU,EAAE,KAAI;QAC/C,MAAM,KAAK,GAAa,EAAE,CAAA;;AAG1B,QAAA,UAAU,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,IAAG;AAC9B,YAAA,MAAM,MAAM,GAAG,UAAU,CAAC,WAAW,CAAC,UAAU,CAAC,iBAAiB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,OAAO,CAAA;YACtF,IAAI,CAAC,IAAI,CAAC,QAAQ,KAAI,MAAM,KAAA,IAAA,IAAN,MAAM,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAN,MAAM,CAAE,cAAc,CAAC,iBAAiB,CAAC,CAAA,IAAI,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,eAAe,CAAC,CAAC,MAAM,EAAE;AAC7G,gBAAA,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;AACtB,aAAA;AACH,SAAC,CAAC,CAAA;QAEF,IAAI,CAAC,KAAK,CAAC,MAAM;YAAE,OAAM;QAEzB,MAAM,OAAO,GAAG,MAAM,CAAC,WAAW,CAAC,CAAC,MAAM,EAAE,CAAA;AAE5C,QAAA,KAAK,CAAC,OAAO,CAAC,IAAI,IAAG;AACnB,YAAA,MAAM,YAAY,GAAG,CAAG,EAAA,IAAI,OAAO,CAAA;AACnC,YAAA,MAAM,UAAU,GAAG,MAAM,CAAC,YAAY,CAAC,CAAA;YACvC,MAAM,GAAG,GAAG,UAAU,CAAC,MAAM,GAAG,UAAU,CAAC,MAAM,CAAC,QAAQ,EAAE,GAAG,UAAU,CAAC,MAAM,EAAE,CAAA;YAClF,MAAM,cAAc,GAAG,GAAG,CAAC,OAAO,CAAC,iCAAiC,EAAE,EAAE,CAAC,CAAA;YACzE,MAAM,GAAG,GAAG,CAAA,EAAG,cAAc,CAAA;AACjC,EAAA,OAAO,EAAE,CAAA;YAEL,MAAM,CAAC,YAAY,CAAC,GAAG;AACrB,gBAAA,IAAI,EAAE,MAAM,GAAG,CAAC,MAAM;AACtB,gBAAA,MAAM,EAAE,MAAM,GAAG;aAClB,CAAA;AACH,SAAC,CAAC,CAAA;AACJ,KAAC,CAAC,CAAA;AACJ;;;;;"}
\ No newline at end of file
diff --git a/node_modules/@tarojs/plugin-platform-kwai/dist/runtime-utils.js b/node_modules/@tarojs/plugin-platform-kwai/dist/runtime-utils.js
new file mode 100644
index 0000000..187cb55
--- /dev/null
+++ b/node_modules/@tarojs/plugin-platform-kwai/dist/runtime-utils.js
@@ -0,0 +1,41 @@
+import { processApis, singleQuote } from '@tarojs/shared';
+
+function initNativeApi(taro) {
+    processApis(taro, ks);
+    taro.cloud = ks.cloud;
+}
+
+const components = {
+    // ======== 调整属性 ========
+    Button: {
+        bindGetPhoneNumber: '',
+        bindGetUserInfo: '',
+        bindContact: '',
+        bindError: '',
+        bindOpenSetting: '',
+        bindLaunchApp: '',
+    },
+    Slider: {
+        color: singleQuote('#e9e9e9'),
+        'selected-color': singleQuote('#1aad19')
+    },
+    Ad: {
+        type: ''
+    },
+    Swiper: {
+        'easing-function': singleQuote('default'),
+    },
+    RichText: {
+        space: ''
+    }
+};
+
+const hostConfig = {
+    initNativeApi,
+    getSpecialNodes() {
+        return ['text', 'image'];
+    },
+};
+
+export { components, hostConfig, initNativeApi };
+//# sourceMappingURL=runtime-utils.js.map
diff --git a/node_modules/@tarojs/plugin-platform-kwai/dist/runtime-utils.js.map b/node_modules/@tarojs/plugin-platform-kwai/dist/runtime-utils.js.map
new file mode 100644
index 0000000..c82409b
--- /dev/null
+++ b/node_modules/@tarojs/plugin-platform-kwai/dist/runtime-utils.js.map
@@ -0,0 +1 @@
+{"version":3,"file":"runtime-utils.js","sources":["../src/apis.ts","../src/components.ts","../src/runtime-utils.ts"],"sourcesContent":["import { processApis } from '@tarojs/shared'\n\ndeclare const ks: any\n\nexport function initNativeApi (taro) {\n  processApis(taro, ks)\n  taro.cloud = ks.cloud\n}\n","import { singleQuote } from '@tarojs/shared'\n\nexport const components = {\n  // ======== 调整属性 ========\n  Button: {\n    bindGetPhoneNumber: '',\n    bindGetUserInfo: '',\n    bindContact: '',\n    bindError: '',\n    bindOpenSetting: '',\n    bindLaunchApp: '',\n  },\n  Slider: {\n    color: singleQuote('#e9e9e9'),\n    'selected-color': singleQuote('#1aad19')\n  },\n  Ad: {\n    type: ''\n  },\n  Swiper: {\n    'easing-function': singleQuote('default'),\n  },\n  RichText: {\n    space: ''\n  }\n}\n","import { initNativeApi } from './apis'\n\nexport { initNativeApi }\nexport * from './components'\nexport const hostConfig = {\n  initNativeApi,\n  getSpecialNodes (): string[] {\n    return ['text', 'image']\n  },\n}\n"],"names":[],"mappings":";;AAIM,SAAU,aAAa,CAAE,IAAI,EAAA;AACjC,IAAA,WAAW,CAAC,IAAI,EAAE,EAAE,CAAC,CAAA;AACrB,IAAA,IAAI,CAAC,KAAK,GAAG,EAAE,CAAC,KAAK,CAAA;AACvB;;ACLa,MAAA,UAAU,GAAG;;AAExB,IAAA,MAAM,EAAE;AACN,QAAA,kBAAkB,EAAE,EAAE;AACtB,QAAA,eAAe,EAAE,EAAE;AACnB,QAAA,WAAW,EAAE,EAAE;AACf,QAAA,SAAS,EAAE,EAAE;AACb,QAAA,eAAe,EAAE,EAAE;AACnB,QAAA,aAAa,EAAE,EAAE;AAClB,KAAA;AACD,IAAA,MAAM,EAAE;AACN,QAAA,KAAK,EAAE,WAAW,CAAC,SAAS,CAAC;AAC7B,QAAA,gBAAgB,EAAE,WAAW,CAAC,SAAS,CAAC;AACzC,KAAA;AACD,IAAA,EAAE,EAAE;AACF,QAAA,IAAI,EAAE,EAAE;AACT,KAAA;AACD,IAAA,MAAM,EAAE;AACN,QAAA,iBAAiB,EAAE,WAAW,CAAC,SAAS,CAAC;AAC1C,KAAA;AACD,IAAA,QAAQ,EAAE;AACR,QAAA,KAAK,EAAE,EAAE;AACV,KAAA;;;ACpBU,MAAA,UAAU,GAAG;IACxB,aAAa;IACb,eAAe,GAAA;AACb,QAAA,OAAO,CAAC,MAAM,EAAE,OAAO,CAAC,CAAA;KACzB;;;;;"}
\ No newline at end of file
diff --git a/node_modules/@tarojs/plugin-platform-kwai/dist/runtime.js b/node_modules/@tarojs/plugin-platform-kwai/dist/runtime.js
new file mode 100644
index 0000000..4f21eb3
--- /dev/null
+++ b/node_modules/@tarojs/plugin-platform-kwai/dist/runtime.js
@@ -0,0 +1,48 @@
+import { processApis, singleQuote, mergeReconciler, mergeInternalComponents } from '@tarojs/shared';
+
+function initNativeApi(taro) {
+    processApis(taro, ks);
+    taro.cloud = ks.cloud;
+}
+
+const components = {
+    // ======== 调整属性 ========
+    Button: {
+        bindGetPhoneNumber: '',
+        bindGetUserInfo: '',
+        bindContact: '',
+        bindError: '',
+        bindOpenSetting: '',
+        bindLaunchApp: '',
+    },
+    Slider: {
+        color: singleQuote('#e9e9e9'),
+        'selected-color': singleQuote('#1aad19')
+    },
+    Ad: {
+        type: ''
+    },
+    Swiper: {
+        'easing-function': singleQuote('default'),
+    },
+    RichText: {
+        space: ''
+    }
+};
+
+const hostConfig = {
+    initNativeApi,
+    getSpecialNodes() {
+        return ['text', 'image'];
+    },
+};
+
+mergeReconciler(hostConfig);
+const internalComponents = mergeInternalComponents(components);
+delete internalComponents.Input.cursor;
+delete internalComponents.Input['placeholder-class'];
+delete internalComponents.Input['cursor-spacing'];
+delete internalComponents.Input['confirm-hold'];
+delete internalComponents.Input['selection-start'];
+delete internalComponents.Input['selection-end'];
+//# sourceMappingURL=runtime.js.map
diff --git a/node_modules/@tarojs/plugin-platform-kwai/dist/runtime.js.map b/node_modules/@tarojs/plugin-platform-kwai/dist/runtime.js.map
new file mode 100644
index 0000000..8119266
--- /dev/null
+++ b/node_modules/@tarojs/plugin-platform-kwai/dist/runtime.js.map
@@ -0,0 +1 @@
+{"version":3,"file":"runtime.js","sources":["../src/apis.ts","../src/components.ts","../src/runtime-utils.ts","../src/runtime.ts"],"sourcesContent":["import { processApis } from '@tarojs/shared'\n\ndeclare const ks: any\n\nexport function initNativeApi (taro) {\n  processApis(taro, ks)\n  taro.cloud = ks.cloud\n}\n","import { singleQuote } from '@tarojs/shared'\n\nexport const components = {\n  // ======== 调整属性 ========\n  Button: {\n    bindGetPhoneNumber: '',\n    bindGetUserInfo: '',\n    bindContact: '',\n    bindError: '',\n    bindOpenSetting: '',\n    bindLaunchApp: '',\n  },\n  Slider: {\n    color: singleQuote('#e9e9e9'),\n    'selected-color': singleQuote('#1aad19')\n  },\n  Ad: {\n    type: ''\n  },\n  Swiper: {\n    'easing-function': singleQuote('default'),\n  },\n  RichText: {\n    space: ''\n  }\n}\n","import { initNativeApi } from './apis'\n\nexport { initNativeApi }\nexport * from './components'\nexport const hostConfig = {\n  initNativeApi,\n  getSpecialNodes (): string[] {\n    return ['text', 'image']\n  },\n}\n","import { mergeReconciler, mergeInternalComponents } from '@tarojs/shared'\nimport { hostConfig, components } from './runtime-utils'\n\nmergeReconciler(hostConfig)\nconst internalComponents = mergeInternalComponents(components)\ndelete internalComponents.Input.cursor\ndelete internalComponents.Input['placeholder-class']\ndelete internalComponents.Input['cursor-spacing']\ndelete internalComponents.Input['confirm-hold']\ndelete internalComponents.Input['selection-start']\ndelete internalComponents.Input['selection-end']\n"],"names":[],"mappings":";;AAIM,SAAU,aAAa,CAAE,IAAI,EAAA;AACjC,IAAA,WAAW,CAAC,IAAI,EAAE,EAAE,CAAC,CAAA;AACrB,IAAA,IAAI,CAAC,KAAK,GAAG,EAAE,CAAC,KAAK,CAAA;AACvB;;ACLO,MAAM,UAAU,GAAG;;AAExB,IAAA,MAAM,EAAE;AACN,QAAA,kBAAkB,EAAE,EAAE;AACtB,QAAA,eAAe,EAAE,EAAE;AACnB,QAAA,WAAW,EAAE,EAAE;AACf,QAAA,SAAS,EAAE,EAAE;AACb,QAAA,eAAe,EAAE,EAAE;AACnB,QAAA,aAAa,EAAE,EAAE;AAClB,KAAA;AACD,IAAA,MAAM,EAAE;AACN,QAAA,KAAK,EAAE,WAAW,CAAC,SAAS,CAAC;AAC7B,QAAA,gBAAgB,EAAE,WAAW,CAAC,SAAS,CAAC;AACzC,KAAA;AACD,IAAA,EAAE,EAAE;AACF,QAAA,IAAI,EAAE,EAAE;AACT,KAAA;AACD,IAAA,MAAM,EAAE;AACN,QAAA,iBAAiB,EAAE,WAAW,CAAC,SAAS,CAAC;AAC1C,KAAA;AACD,IAAA,QAAQ,EAAE;AACR,QAAA,KAAK,EAAE,EAAE;AACV,KAAA;CACF;;ACrBM,MAAM,UAAU,GAAG;IACxB,aAAa;IACb,eAAe,GAAA;AACb,QAAA,OAAO,CAAC,MAAM,EAAE,OAAO,CAAC,CAAA;KACzB;CACF;;ACND,eAAe,CAAC,UAAU,CAAC,CAAA;AAC3B,MAAM,kBAAkB,GAAG,uBAAuB,CAAC,UAAU,CAAC,CAAA;AAC9D,OAAO,kBAAkB,CAAC,KAAK,CAAC,MAAM,CAAA;AACtC,OAAO,kBAAkB,CAAC,KAAK,CAAC,mBAAmB,CAAC,CAAA;AACpD,OAAO,kBAAkB,CAAC,KAAK,CAAC,gBAAgB,CAAC,CAAA;AACjD,OAAO,kBAAkB,CAAC,KAAK,CAAC,cAAc,CAAC,CAAA;AAC/C,OAAO,kBAAkB,CAAC,KAAK,CAAC,iBAAiB,CAAC,CAAA;AAClD,OAAO,kBAAkB,CAAC,KAAK,CAAC,eAAe,CAAC"}
\ No newline at end of file
