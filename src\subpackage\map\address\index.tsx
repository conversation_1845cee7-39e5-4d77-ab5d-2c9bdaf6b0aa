import { View, Button } from '@tarojs/components'
import { useLoad } from '@tarojs/taro'
import { useEffect, useState } from 'react'
import cn from 'classnames'
import style from './index.module.scss'

import type { TTreeFullVal, TTreeVal } from './utils/index.d'
import ListView from './components/ListView'
import HeadLocation from './components/HeadLocation'
import IconFont from '@/components/IconFont'

import { addrConfDef } from './utils/tools'
import {
  getAreaConf,
  initAddress,
  selectAddress,
  selectArrHandler,
  selectClear,
  selectHandler,
} from './utils/index'

export default function Index() {
  const [oneArea, setOneArea] = useState<TTreeFullVal>([]) // 一级地址列表
  const [twoArea, setTwoArea] = useState<TTreeFullVal>([]) // 二级地址列表
  const [threeArea, setThreeArea] = useState<TTreeFullVal>([]) // 三级地址列表
  const [selectAddrs, setSelectAddrs] = useState<TTreeFullVal>([]) // 已选中的地址信息
  const [isFooter, setIsFooter] = useState(false) // 是否显示底部按钮
  const [addrConf, setAddConf] = useState({ ...addrConfDef }) // 地址的页面的配置信息
  const [listViewHeight, setListViewHeight] = useState(0) // ListView的固定高度

  useEffect(() => {
    setIsFooter(addrConf.isMultiple && selectAddrs.length > 0)
  }, [selectAddrs.length])

  // 计算ListView高度 - 使用DOM查询获取实际高度
  useEffect(() => {
    const processHeightResults = (res: any[]) => {
      let headLocationHeight = 0
      let footerHeight = 0

      // 遍历结果，根据id来获取对应组件的高度
      res.forEach((item) => {
        if (item && item.id) {
          if (item.id === 'head-location') {
            headLocationHeight = item.height || 0
          } else if (item.id === 'footer') {
            footerHeight = item.height || 0
          }
        }
      })

      return { headLocationHeight, footerHeight }
    }

    const calculateHeight = () => {
      const systemInfo = $.taro.getSystemInfoSync()
      const { windowHeight } = systemInfo

      // 获取实际组件高度
      let headLocationHeight = 0
      let footerHeight = 0

      // 使用Taro的createSelectorQuery获取组件高度
      const query = $.taro.createSelectorQuery()

      // 获取HeadLocation高度
      if (addrConf.showLocation) {
        query.select('#head-location').boundingClientRect()
      }

      // 获取Footer高度
      if (isFooter) {
        query.select('#footer').boundingClientRect()
      }

      query.exec((res) => {
        if (res && res.length > 0) {
          const heights = processHeightResults(res)
          headLocationHeight = heights.headLocationHeight
          footerHeight = heights.footerHeight
        }

        // 计算可用高度
        const availableHeight = windowHeight - headLocationHeight - footerHeight
        setListViewHeight(availableHeight)
      })
    }

    // 延迟执行，确保DOM已经渲染
    const timer = setTimeout(calculateHeight, 100)
    return () => clearTimeout(timer)
  }, [isFooter, addrConf.showLocation])

  useLoad(() => {
    const conf = getAreaConf()
    setAddConf({ ...conf })

    $.taro.setNavigationBarTitle({ title: conf.title })

    $.showLoading('加载中...')
    // console.time('initAddress')
    initAddress(conf.areas).then(data => {
      setOneArea(data.oneArea)
      setTwoArea(data.twoArea)
      setThreeArea(data.threeArea)
      setSelectAddrs(data.selectAddrs)
      // console.timeEnd('initAddress')
    }).finally(() => {
      $.hideLoading()
    })
  })

  /** 选择事件 */
  const onSelect = async (area: TTreeVal) => {
    // console.time('onSelect')
    let selectArr: TTreeVal[] = $.deepClone(selectAddrs)
    const { isEnd, ...setArea } = selectAddress(area, selectArr)
    if (addrConf.isMultiple && isEnd) {
      // 多选的情况
      selectArr = selectArrHandler(selectArr, area)
      if (selectArr.length > addrConf.maxNum) {
        $.msg(`最多只能选择${addrConf.maxNum}个城市`)
        return
      }

      setSelectAddrs(selectArr)
    } else if (!addrConf.isMultiple && isEnd) {
      // 单选的情况
      $.router.event([{
        ...area,
      }])
      $.router.back(1)
      return
    }

    // const selectAreaInfos = await getAreaSearches(selectArr.map(item => item.id), 'id')
    const data = selectHandler({ ...setArea, selectAddrs: selectArr })

    // console.timeEnd('onSelect')
    setOneArea(data.oneArea)
    setTwoArea(data.twoArea)
    setThreeArea(data.threeArea)
  }

  /** 清除事件 */
  const onCancel = () => {
    const data = selectClear({
      oneArea,
      twoArea,
      threeArea,
    })

    setOneArea(data.oneArea)
    setTwoArea(data.twoArea)
    setThreeArea(data.threeArea)
    setSelectAddrs([])
  }

  /** 底部按钮的删除地址逻辑 */
  const onDelete = (index: number) => {
    const selectArr = $.deepClone(selectAddrs) as TTreeVal[]
    selectArr.splice(index, 1)
    setSelectAddrs(selectArr)
    if (selectArr.length < 1) {
      onCancel()
      return
    }
    const data = selectHandler({
      oneArea,
      twoArea,
      threeArea,
      selectAddrs: selectArr,
    })

    setOneArea(data.oneArea)
    setTwoArea(data.twoArea)
    setThreeArea(data.threeArea)
  }

  /** 确定事件 */
  const onConfirm = () => {
    if ($.isArrayVal(selectAddrs)) {
      const data = $.deepClone(selectAddrs)
      $.router.event(data)
      $.router.back(1)
      return
    }
    $.msg('请选择地址')
  }

  /** 监听定位地址变化 */
  const onLocationChange = ({ value }) => {
    onSelect(value)
  }

  return (<V className={style.page}>
    {addrConf.showLocation && (
      <View id='head-location'>
        <HeadLocation levelUi={addrConf.level} onChange={onLocationChange} />
      </View>
    )}
    <View className={style.section}>
      {/* {selectAddrs.map(item => JSON.stringify(item))} */}
      <View className={cn(style.content, addrConf.level == 2 && style['two-column'])}>
        {/* 一级 */}
        <ListView
          disabledIds={addrConf.disabledIds}
          levelUI={addrConf.level}
          level={1}
          selectAddrs={selectAddrs}
          data={oneArea}
          isFooter={isFooter}
          isMultiple={addrConf.isMultiple}
          click={onSelect}
          height={listViewHeight}
        />
        {/* 二级 */}
        {twoArea && twoArea.length > 0 && (
          <ListView
            level={2}
            levelUI={addrConf.level}
            selectAddrs={selectAddrs}
            disabledIds={addrConf.disabledIds}
            data={twoArea}
            isFooter={isFooter}
            isMultiple={addrConf.isMultiple}
            click={onSelect}
            height={listViewHeight}
          />
        )}
        {/* 三级 */}
        {addrConf.level == 3 && threeArea && threeArea.length > 0 && (
          <ListView
            level={3}
            levelUI={addrConf.level}
            selectAddrs={selectAddrs}
            disabledIds={addrConf.disabledIds}
            data={threeArea}
            isFooter={isFooter}
            isMultiple={addrConf.isMultiple}
            click={onSelect}
            height={listViewHeight}
          />
        )}
      </View>
      { isFooter && (
        <View className={style.footer} id='footer'>
          <View className={style['footer-head']}>
            <View className={style['footer-title']}>我选择的地区</View>
            <View className={style['footer-count']}>已选{selectAddrs.length}/{addrConf.maxNum}</View>
          </View>
          <View className={style['footer-select']}>
            {selectAddrs.map((item, index) => (
              <View key={item.id} className={style['footer-item']} onClick={() => onDelete(index)}>
                {item.name}
                <IconFont type='yp-close-small' size={24} color='rgba(0, 146, 255, 1)' />
              </View>
            ))}
          </View>
          <View className={style['footer-btn']}>
            <Button className={style.btn} onClick={onCancel}>清除</Button>
            <Button className={cn(style.btn, style['btn-confirm'])} onClick={onConfirm}>确定选择</Button>
          </View>
        </View>
      )}
    </View>
  </V>)
}
