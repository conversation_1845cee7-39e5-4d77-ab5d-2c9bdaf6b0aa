import { useEffect, useState } from 'react'
import Popup from '@/components/Popup'
import { actions, dispatch, useSelector } from '@/core/store/index'
import { IDefValProps } from '@/pages/resume/data'
import ResumePublish from '@/pages/resume/components/ResumePublish'
import { savePublishData } from '@/core/utils/publish'
import { getLocationByApi, getAreaByAdcode, getLocation } from '@/utils/location'
import { getPopupZIndex } from '@/utils/deviceDetection'

type IProps = {
  visible: boolean
  /** 城市ID，格式:[id,id,id] */
  hopeAreas?: Array<any>
  /** 工种，格式:[{industry: 行业, occIds: [工种ID,工种ID]}],可传空数组[] */
  occupations?: Array<any>
  onClose?: () => void
  onConfirmCbFn?: () => void
}

// 发布简历弹窗
export default (props: IProps) => {
  const { visible, hopeAreas, onClose, onConfirmCbFn } = props
  const [show, setShow] = useState(false)
  const [isShowGpsTips, setShowGpsTips] = useState(true)
  const [defVal, setDefVal] = useState<IDefValProps>({ hopeAreas: [], occupations: [] })
  const [sysInfo, setSysInfo] = useState<any>(null)
  const userInfo = useSelector((state) => state.storage.userInfo)

  // 获取系统信息，用于设备检测
  useEffect(() => {
    const info = $.sysInfo()
    setSysInfo(info)
  }, [])

  useEffect(() => {
    if (visible) {
      initData()
      const page = $.router.getCurrentPage()

      if (page.$taroPath.indexOf('pages/index/index') >= 0) {
        const reportData = {
          name: '简历发布前置弹窗',
          page_name: '首页',
        }
        $.report.event('userPopupExposure', reportData)
      }
    }
    setShow(visible)
  }, [visible])

  useEffect(() => {
    if (userInfo.userId) {
      initData()
    }
  }, [userInfo.userId])

  const initData = async () => {
    // 没登录不允许发布
    if (!userInfo.userId) {
      return
    }

    const params: any = {}
    let level
    let pid
    let id
    try {
      const gpsData = await getLocation().catch(() => {
        return {}
      })
      const { latitude, longitude } = gpsData || {}
      level = gpsData.level
      pid = gpsData.pid
      id = gpsData.id

      if (longitude && latitude) {
        params.longitude = longitude
        params.latitude = latitude
        setShowGpsTips(false)
      }
    } catch {
      setShowGpsTips(true)
    }
    const areaId = level == 3 ? pid : id
    let nHopeAreas: Array<string> = []
    if (areaId) {
      nHopeAreas = [`${areaId}`]
    } else if ($.isArrayVal(hopeAreas)) {
      nHopeAreas = [...(hopeAreas || [])]
    }

    setDefVal(pre => ({ ...pre, hopeAreas: nHopeAreas }))
  }

  const onHidePop = () => {
    /** 关闭埋点 */
    const reportData = {
      name: '简历发布前置弹窗',
      page_name: '首页',
    }
    $.report.event('closePopupClick', reportData)
    setShow(false)
    onClose && onClose()
  }

  /** 拉取定位授权 - 适配快手小程序 */
  const onClickGps = async () => {
    // 快手小程序需要特殊处理
    if (process.env.TARO_ENV === 'kwai') {
      try {
        // 先检查权限状态
        const setting = await new Promise<any>((resolve, reject) => {
          ks.getSetting({
            success: (res: any) => resolve(res),
            fail: (err: any) => reject(err),
          })
        })

        // 如果用户之前拒绝了权限，需要引导用户去设置页面开启
        if (setting.authSetting && setting.authSetting['scope.userLocation'] === false) {
          const modalRes = await new Promise<any>((resolve) => {
            ks.showModal({
              title: '位置权限',
              content: '需要位置权限才能自动定位，请在设置中开启位置权限',
              confirmText: '去设置',
              cancelText: '取消',
              success: (res: any) => resolve(res),
            })
          })

          if (modalRes.confirm) {
            // 打开设置页面
            await new Promise<any>((resolve, reject) => {
              ks.openSetting({
                success: () => resolve(true),
                fail: () => reject(new Error('打开设置失败')),
              })
            })
            // 重新调用定位
            onClickGps()
            return
          }
          $.msg('需要位置权限才能自动定位')
          return
        }

        getLocation().then(({ city, province, latitude, longitude }) => {
          const value = city || province
          const isValue = !!(value && value.id)

          if (isValue) {
            dispatch(actions.storage.setItem({ key: 'homeLocation',
              value: {
                success: true,
                data: {
                  latitude: String(latitude),
                  longitude: String(longitude),
                  areaId: value?.id,
                  name: value?.name,
                },
              } }))
            // 设置定位回显
            savePublishData('hopeAreas', [value?.id])
            setDefVal(pre => ({ ...pre, hopeAreas: [value?.id] }))
            // 隐藏定位提示
            setShowGpsTips(false)
            $.msg('定位成功')
          } else {
            $.msg('无法识别当前位置，请手动选择城市')
          }
        }).catch((err) => {
          if (!err || err.code != 401) {
            $.msg('定位失败，请稍后再试')
          }
        })
      } catch (error) {
        handleLocationError(error)
      }
    }
  }

  /** 处理定位失败 */
  const handleLocationError = (err: any) => {
    if (err.errMsg?.includes('auth deny') || err.errMsg?.includes('authorize no response')) {
      // 打开设置页面进行授权
      $.taro.openSetting({
        success(settingRes) {
          const { authSetting } = settingRes
          if (authSetting['scope.userLocation']) {
            // 用户同意授权，重新获取定位
            onClickGps()
          } else {
            $.msg('需要位置权限才能自动定位')
          }
        },
        fail() {
          $.msg('无法打开设置页面')
        },
      })
      return
    }

    if (!err || err.code !== 401) {
      $.msg('定位失败，请稍后再试')
    }
  }

  return (
    <Popup
      visible={show}
      position="bottom"
      zIndex={getPopupZIndex(sysInfo)}
      disableScroll
      catchMove
    >
      <ResumePublish
        value={defVal}
        isPopStyle
        isShowGpsTips={isShowGpsTips}
        cityLabel="工作城市"
        occLabel='期望职位'
        cityPlaceholder='请选择'
        occPlaceholder='请选择'
        onHidePop={onHidePop}
        onClickGps={onClickGps}
        onConfirmCbFn={onConfirmCbFn}
      />

    </Popup>
  )
}
