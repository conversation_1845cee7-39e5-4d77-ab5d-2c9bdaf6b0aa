/// <reference types="@tarojs/taro" />

declare module '*.png';
declare module '*.gif';
declare module '*.jpg';
declare module '*.jpeg';
declare module '*.svg';
declare module '*.css';
declare module '*.less';
declare module '*.scss';
// declare module '*.sass';
declare module '*.styl';
declare module '*.module.scss'

declare module 'sass' {
  const sass: any
  export default sass
}

/** 将对象的所有类型变成必选 */
declare type DeepPartial<T> = Required<T> extends Function
  ? T
  : T extends Required<object>
  ? Required<{ [P in keyof T]: DeepPartial<Required<T[P]>> }>
  : T;

declare namespace ILocation {
  /** @name 地址数据 */
  interface TAreaData {
    /** 地址名 */
    name: string,
    /** 地址唯一id */
    id: number | string,
    /** 地址编码 */
    ad_code: string,
    /** 地址父级id */
    pid: number | string,
    /** 地址等级 */
    level: number,
    /** 地址拼音 */
    letter: string,
    /** 地址全名 */
    ad_name: string
    /** 子集数据 */
    children?: TAreaData[]
  }
}

declare const ks: any

declare namespace NodeJS {
  interface ProcessEnv {
    TARO_APP_ENV: 'dev' | 'test' | 'pre' | 'prod'
    /** NODE 内置环境变量, 会影响到最终构建生成产物 */
    NODE_ENV: 'development' | 'production',
    /** 当前构建的平台 */
    TARO_ENV: 'weapp' | 'swan' | 'alipay' | 'h5' | 'rn' | 'tt' | 'quickapp' | 'qq' | 'jd' | 'xhs' | 'kwai'
    /**
     * 当前构建的小程序 appid
     * @description 若不同环境有不同的小程序，可通过在 env 文件中配置环境变量`TARO_APP_ID`来方便快速切换 appid， 而不必手动去修改 dist/project.config.json 文件
     * @see https://taro-docs.jd.com/docs/next/env-mode-config#特殊环境变量-taro_app_id
     */
    TARO_APP_ID: string
  }
}

type TextLiteral = string | number

type Nullable<T> = T | null

/**
 * 邀请注册参数
 */
interface ShareReq {
  /** 推广用户ID */
  refTenantId: string;
  /** 推广端 */
  shareSource: string;
  /** 推广唯一标识 */
  trackSeed: string;
}
