/* eslint-disable no-plusplus */
/*
 * @Date: 2024-11-21 11:31:43
 * @Description: hook
 */

import { useState, useEffect, useRef } from 'react'
import { publishSubscribe } from './core'

let zIndex = 10001

export default function useModal<T extends Record<string, any>>(modalType) {
  const [options, setOptions] = useState<T>({} as T)
  const [visible, setVisible] = useState(false)
  const res = useRef<Function>()
  const rej = useRef<Function>()

  useEffect(() => {
    const page = $.router.getCurrentPage()
    const unsubscribe = publishSubscribe.subscribe((data) => {
      
      const { type, options, resolve, reject } = data
      if (page !== $.router.getCurrentPage()) {
        return
      }
      if (type !== modalType) {
        return
      }
      res.current = resolve
      rej.current = reject
      setOptions({ ...options, zIndex: ++zIndex })
      setVisible(true)
    })
    return () => {
      unsubscribe()
    }
  }, [modalType])

  const onResolve = (data?: any) => {
    setVisible(false)
    res.current && res.current(data)
  }

  const onReject = (data?: any) => {
    setVisible(false)
    rej.current && rej.current(data)
  }

  return {
    visible,
    options,
    onResolve,
    onReject,
  }
}
