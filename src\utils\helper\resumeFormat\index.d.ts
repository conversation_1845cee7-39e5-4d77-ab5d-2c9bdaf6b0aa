/*
 * @Author: jiang<PERSON>
 * @LastEditors: jianglong
 */

/** 用户的找活信息 */
export type IRresumeInfoMe = YModels['POST/resume/v3/detail/app/myDetailFirstScreen']['Res']['data']

export interface IFmtTemplateTextOptions {
  /**
   * 替换积分的文本文案
   * @default '{}''
   */
  replaceStr: string
  /**
   * 自定义的文案分割标识
   * @default '________'
   */
  splitIdentity: string
  [key: string]: any
}

export type IUserNameObj = {
  /** 这里主要用在编辑页的提交 */
  name: string,
  /** 格式化姓名：包括实名之后格式化的姓名 */
  nameFormat: string
}

/** 处理找活信息的标签-返回的值 */
export type IHeaderJobTags = {
  /** tag标签信息数组 */
  jobTags: any[],
  /** 是否已完善找活信息 */
  completed: boolean
}

type IOperationType = '个人信息' |
'求职意向' |
'个人介绍' |
'面试视频' |
'工作经历' |
'技能证书'

export type IClickPointExp = {
  /** 页面: 我的简历、完善简历 */
  page_name: string,
  /** 资源位类型 */
  operation_type: IOperationType,
}

export type IClickPointClick = {
  /** 页面: 我的简历、完善简历 */
  page_name?: string,
  /** 资源位类型 */
  operation_type: IOperationType,
  /** 点击按钮 */
  click_bottom: '去完善' | '头像' | '' |
  '实名按钮' | '编辑按钮' | '简历开关按钮' |
  '工种选择' | '地区选择' | '期望薪资' | '职业优势'
}
