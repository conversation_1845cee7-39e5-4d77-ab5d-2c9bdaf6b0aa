.wrap {
  padding: 36px 0;

}
.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 32px;
}

.name {
  font-size: 54px;
  color: #FFFFFFFF;
  max-width: 510px;
  margin-bottom: 8px;
  @include ellip()
}

.state {
  font-size: 26px;
  color: #FFFFFF73;
}

.logo {
  width: 128px;
  height: 128px;
  border-radius: 16px;
  border: 1px solid #e9edf3ff;
}

.point {
  // width: 14px;
  padding: 0 8px;
  color: #FFFFFF73;
}

.workLeft {
  display: flex;
  flex-wrap: wrap;
}

.work {
  display: flex;
  justify-content: space-between;
  padding: 0 32px;
  padding-top: 48px;
}

.item {
  display: flex;
  align-items: center;
  margin-right: 52px;
  margin-bottom: 24px;
}

.item:last-child {
  margin-right: 0px;
}

.itemText {
  font-size: 26px;
  color: #FFFFFFFF;
  margin-left: 16px;
}

.benefitList {
  display: flex;
  padding-top: 48px;
  width: 100%;
  overflow: scroll;
  height: 154px;
}

.benefitList::-webkit-scrollbar {
  display: none;
}

.benefitItem {
  display: flex;
  height: 104px;
  padding: 0 20px;
  border: 1px solid #ffffff40;
  border-radius: 16px;
  align-items: center;
  flex: 1;
  flex-shrink: 0;
  margin-right: 20px;

  &:first-child {
    margin-left: 32px;
  }
}

.benefitItemImage {
  width: 48px;
  height: 48px;
  // background-color: green;
}

.benefitItemText {
  font-size: 26px;
  color: #FFFFFF;
  white-space: nowrap;
  padding-left: 16px;

}
