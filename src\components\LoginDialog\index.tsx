import { Button, Image, Text, View } from '@tarojs/components'
import { useEffect, useMemo, useState } from 'react'

import styles from './index.module.scss'
import IconFont from '../IconFont'
import ModalBody from '@/components/Modal/Body'
import LoginProtocol from '../LoginProtocol'
import { afterLogin, goToAgreement } from '@/utils/login'
import { actions, dispatch, useSelector } from '@/core/store'
import useClick from '@/hooks/useClick'
import LoginBtn from '../LoginBtn'
import { getAuthCodeLock } from '@/core/utils/login'

function LoginDialog() {
  const [checked, $checked] = useState(false)

  const [pVisible, $pVisible] = useState(false)

  const currentPage = useMemo(() => $.taro.getCurrentPages().pop() || {}, [])

  const { pageId, success, fail } = useSelector((state) => state.global.showLoginModel)

  const visible = useMemo(() => pageId === currentPage.__wxWebviewId__, [pageId, currentPage])
  const onClose = () => dispatch(actions.global.setState({ showLoginModel: {} }))

  /** 每次弹出时刷新数据 */
  useEffect(() => {
    if (visible) {
      getAuthCodeLock()
      $.showLoading('加载中...')
      $checked(false)
      $pVisible(false)
    }
  }, [visible])

  /** 协议勾选样式 */
  const iconColor = useMemo(() => {
    if (checked) {
      return 'rgb(0, 146, 255)'
    }
    return 'rgba(0,0,0,.25)'
  }, [checked])
  /** 获取手机号登录
   * @param { boolean } certainProtocol 是否已阅读并同意协议
   * @returns {Function} eventHandler
   */
  const fakerLogin = useClick(async (evt: any = {}) => {
    if (pVisible) {
      $pVisible(false)
      $checked(true)
    }
    const { iv, encryptedData } = evt.detail || {}
    if (!iv || !encryptedData) {
      await $.msg('获取手机号失败，将为你跳转到验证码登陆')
      toLoginPage()
    } else {
      try {
        $.showLoading('登陆中...')
        const result = await $.taro.login()
        if (result && result.code) {
          const response = await $.request['POST/account/v1/login/kuaiShouMiniLogin']({ authCode: result.code, encryptedData, iv, appId: $.config.appid })
          const token = $.getObjVal(response, '0.token')
          const message = $.getObjVal(response, '1.message')
          $.hideLoading()
          if (token) {
            afterLogin(token)
            $pVisible(false)
            $.msg('登录成功')
            success && success()
            onClose && onClose()
          } else {
            fail && fail()
            $.msg(message || '登陆失败')
          }
        }
      } catch ([_, error]) {
        $.hideLoading()
        if (error && (error.code == ******** || error.code == ********)) {
          fail && fail()
          await $.msg('无法获取支付宝绑定手机号,为您选择手机号验证码登录')
          $.router.push('/pages/auth-login/index', { back: true }, {}, (logged: boolean) => {
            if (logged) {
              onClose && onClose()
              success && success()
            } else {
              fail && fail()
            }
          })
          return
        }

        const dialogIdentify = $.getObjVal(error, 'data.dialogData.dialogIdentify') || $.getObjVal(error, 'data.data.dialogData.dialogIdentify')
        if (dialogIdentify === 'limitOperate' || (error && error.code == 10110009)) {
          $.confirm({
            title: '温馨提示',
            content: '您的登录/注册环境异常，请稍后再试',
            confirmText: '联系客服',
            cancelText: '知道了',
          }).then(() => {
            $.taro.makePhoneCall({ phoneNumber: '4008381888' })
          })
          fail && fail()
          return
        }
        // fail && fail()
        $.msg('登录失败, 请重试')
      }
    }
  })
  const toLoginPage = useClick(async () => {
    if ($.router.path.includes('pages/auth-login/index')) return
    $.router.push('/pages/auth-login/index', { back: true }, {}, (logged: boolean) => {
      if (logged) {
        onClose && onClose()
        success && success()
      } else {
        fail && fail()
      }
    })
  })

  // 这个地方处理弹窗渲染慢的情况，使用图片的load事件来关闭加载动画
  const onLoadImg = () => {
    $.hideLoading()
  }

  return (
    <>
      <ModalBody visible={visible} onMaskClick={() => {
        onClose()
        fail && fail()
      }}
      >
        <View className={styles.container}>
          <Image onError={onLoadImg} onLoad={onLoadImg} src="https://cdn.yupaowang.com/yupao_mini/alipay-mini-login-log.png" className={styles.logo}></Image>
          <Text className={styles.loginText}>登录鱼泡直聘</Text>
          {checked ? <LoginBtn
            openType="getPhoneNumber"
            onGetPhoneNumber={fakerLogin}
            className={styles.loginBtn}
          >快手账号登录</LoginBtn>
            : <Button className={styles.loginBtn} onClick={() => $pVisible(true)} onError={console.error}>快手账号登录</Button>}
          <Text className={styles.verifyCodeLogin} onClick={toLoginPage}>用其他手机号码登录</Text>
          <View className={styles.protocol} onClick={() => $checked(!checked)}>
            <View><IconFont className={styles.checkedIcon} color={iconColor} type="yp-alipay-checked-round"></IconFont></View>
            <Text>已阅读并同意
              <Text className={styles.primary} onClick={goToAgreement} data-type="privacy">《隐私政策》</Text>
              <Text className={styles.primary} onClick={goToAgreement} data-type="user">《服务协议》</Text>
            并授权鱼泡直聘
            </Text>
          </View>
        </View>
      </ModalBody>
      <LoginProtocol visible={pVisible} onClose={() => $pVisible(false)} fakerLogin={fakerLogin} isGetPhoneNumber></LoginProtocol>
    </>)
}

export default LoginDialog
