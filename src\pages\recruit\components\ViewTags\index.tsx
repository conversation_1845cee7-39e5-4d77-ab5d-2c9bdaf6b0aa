import { View } from '@tarojs/components'
import { memo } from 'react'
import styles from './index.module.scss'

const ViewTags = memo<{ loading: boolean; resTags: any[], defaultRows?: number, id: TextLiteral, compactClass?: string }>(({ resTags, defaultRows = 3, id = 0, compactClass = '' }) => {
  return (
    <View
      className={`${styles.compact} ${compactClass}`}
      style={{ maxHeight: 'none' }}
      id={`compact-grid-${id}`}
    >
      {resTags.map((item, key) => (
        <View key={key} className={styles.gridItem} id={`${key}`} style={{ opacity: 1 }}>
          {item.name}
        </View>
      ))}
    </View>
  )
})

export default ViewTags
