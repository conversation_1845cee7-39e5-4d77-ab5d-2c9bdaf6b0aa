function getTabBarList() {
  const list = [
    {
      pagePath: 'pages/index/index',
      text: '找工作',
      iconPath: 'assets/images/home.png',
      selectedIconPath: 'assets/images/home-active.png',
      color: '#666666',
      selectedColor: '#0092ff',
    },
    {
      pagePath: 'pages/resume/index',
      text: '简历',
      iconPath: 'assets/images/resume.png',
      selectedIconPath: 'assets/images/resume-active.png',
      color: '#666666',
      selectedColor: '#0092ff',
    },
    {
      pagePath: 'pages/mine/index',
      text: '我的',
      iconPath: 'assets/images/mine.png',
      selectedIconPath: 'assets/images/mine-active.png',
      color: '#666666',
      selectedColor: '#0092ff',
    },
  ]
  if (process.env.TARO_ENV === 'kwai') {
    return list
  }
  return list.map((item) => {
    return {
      pagePath: item.iconPath,
      text: item.text,
    }
  })
}

export default defineAppConfig({
  permission: {
    'scope.userLocation': {
      desc: '你的位置信息将用于推荐附近的工作',
    },
  },
  // 快手小程序权限配置
  requiredPrivateInfos: process.env.TARO_ENV === 'kwai' ? [
    'getLocation',
    'openLocation',
  ] : undefined,
  entryPagePath: 'pages/index/index',
  pages: [
    'pages/index/index',
    'pages/recruit/detail',
    'pages/auth-login/index',
    'pages/mine/index',
    'pages/resume/index',
  ],
  subPackages: [
    {
      root: 'subpackage/classify', // 职位选择器
      pages: ['index'],
    },
    {
      root: 'subpackage/map',
      pages: ['address/index'],
    },
    {
      // 企业详情
      root: 'subpackage/company',
      pages: ['index', 'video-preview/index', 'album/index'],
    },
    {
      root: 'subpackage/web-view',
      pages: ['index'],
    },
    {
      root: 'subpackage/certification',
      pages: ['index'],
    },
    {
      root: 'subpackage/resume-complete',
      pages: ['one/index', 'two/index', 'perfect-job-v4/index'],
    },
    {
      // 搜索页
      root: 'subpackage/search',
      pages: ['search-page/index', 'search-result/index'],
    },
    {
      // 我的在线简历页的二级页面
      root: 'subpackage/my-resume',
      pages: ['edit-introduce/index', 'err-check/index'],
    },
    {
      // 开发开关
      root: 'subpackage/dev',
      pages: ['index/index'],
    },
    {
      // 个人中心二级页面
      root: 'subpackage/member',
      pages: [
        'info/index',
        'info-editname/index',
        'info-editinput/index',
      ],
    },
    {
      //
      root: 'subpackage/resume-publish',
      pages: ['index'],
    },
  ],
  tabBar: {
    list: getTabBarList(),
  } as any,
  window: {
    navigationBarBackgroundColor: '#FFFFFF', // 导航栏背景颜色
    allowsBounceVertical: 'NO', // 禁止下拉回弹
  },
})
