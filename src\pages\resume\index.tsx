/*
 * @Date: 2024-11-21 16:57:14
 * @Description: 简历
 */

import { useDidHide, useDidShow, useLoad, usePullDownRefresh } from '@tarojs/taro'
import { useState, useEffect, useRef } from 'react'
import Page from '@/components/Page'
import ResumePublish from './components/ResumePublish'
import ResumeDetail from './components/ResumeDetail'
import LoadingBox from '@/components/LoadingBox'
import { actions, dispatch, store, useSelector } from '@/core/store/index'
import { IDefValProps } from './data'
import NoLoginData from './components/NoLoginData'
import { getDefaultData } from './utils'
import { getGlobalDefaultConfig } from '@/utils/helper/resume'

export default () => {
  const [defVal, setDefVal] = useState<IDefValProps>({ hopeAreas: [], occupations: [], publishRuleSwitch: false })
  const [exist, setExist] = useState(false)
  const [isRequest, setIsRequest] = useState(false)
  const [refreshNum, setRefreshNum] = useState(0)
  const isHide = useRef(false)
  const token = useSelector((state) => state.storage.token)
  const userInfo = useSelector((state) => state.storage.userInfo)

  useLoad(() => {
    // 获取全局通用配置
    getGlobalDefaultConfig()
  })

  useEffect(() => {
    if (!token) {
      dispatch(actions.storage.setItem({ key: 'myResumeDetails', value: {} }))
      setIsRequest(false)
      setExist(false)
    }
  }, [token])

  useEffect(() => {
    if (userInfo.userId) {
      setRefreshNum(1)
      init()
    }
  }, [userInfo.userId])

  useDidShow(() => {
    if (!isHide.current) return
    isHide.current = false
    init().then(() => {
      setRefreshNum(refreshNum + 1)
    })
  })

  useDidHide(() => {
    isHide.current = true
  })

  usePullDownRefresh(() => {
    setRefreshNum(refreshNum + 1)
    setTimeout(() => {
      $.taro.stopPullDownRefresh()
    }, 1200)
  })

  const init = async () => {
    if (!userInfo.userId) {
      dispatch(actions.storage.setItem({ key: 'myResumeDetails', value: {} }))
      setExist(false)
      setIsRequest(false)
      return
    }
    const resumeUuid = $.getObjVal(store.getState().storage.myResumeDetails, 'resumeUuid')

    if (resumeUuid) {
      setExist(true)
      setIsRequest(true)
      return
    }
    if (isRequest) return
    const data = await getDefaultData()
    const { exist, hopeAreas = [], occupations = [], publishRuleSwitch } = data || { exist: false }
    setIsRequest(true)
    setDefVal({ occupations, publishRuleSwitch, hopeAreas: (hopeAreas || []).map((area) => `${area.id}`).filter(Boolean) })
    setExist(exist)
  }
  return (
    <Page backgroundColor='#fff'>
      {
        userInfo.userId ? !isRequest && <LoadingBox /> : <NoLoginData />
      }
      {
        userInfo.userId && isRequest ? (
          exist ? <ResumeDetail refreshNum={refreshNum} /> : <ResumePublish value={defVal} />
        ) : null
      }
      <V className='tab-bar-height' />
    </Page>
  )
}
