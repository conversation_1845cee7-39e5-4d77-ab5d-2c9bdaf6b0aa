/** 融资规模 */
export const FINANCE_SCALE_MAP = [
  { label: '未融资', value: '1' },
  { label: '天使轮', value: '2' },
  { label: 'A轮', value: '3' },
  { label: 'B轮', value: '4' },
  { label: 'C轮', value: '5' },
  { label: 'D轮及以上', value: '6' },
  { label: '已上市', value: '7' },
  { label: '不需要融资', value: '8' },
] as const

/** 人员规模 */
export const STAFF_SIZE_MAP = [
  { label: '0-20人', value: '1' },
  { label: '20-99人', value: '2' },
  { label: '100-499人', value: '3' },
  { label: '500-999人', value: '4' },
  { label: '1000-9999人', value: '5' },
  { label: '10000人以上', value: '6' },
]as const

/** 休息时间 */
export const REST_TIME_MAP = [
  { label: '双休', value: '1' },
  { label: '排班轮休', value: '2' },
]as const

/** 加班情况 */
export const OVERTIME_SITUATION_MAP = [
  { label: '不加班', value: '1' },
  { label: '偶尔加班', value: '2' },
  { label: '弹性工作', value: '3' },
]as const

/** 晋升制度 */
export const PROMOTION_SYSTEM_MAP = [
  { label: '考核晋升', value: '1' },
  { label: '定期晋升', value: '2' },
  { label: '完善的晋升机制', value: '3' },
]as const

/** 人才激励 */
export const TALENT_MOTIVATION_MAP = [
  { label: '定期普调', value: '1' },
  { label: '定期绩效调薪', value: '2' },
  { label: '晋级涨薪', value: '3' },
  { label: '项目奖金', value: '4' },
  { label: '团队奖金', value: '5' },
  { label: '个人奖金', value: '6' },
  { label: '绩效提成', value: '7' },
  { label: '股票期权', value: '8' },
  { label: '人才补贴', value: '9' },
]as const

/** 能力培养 */
export const SKILL_DEVELOPMENT_MAP = [
  { label: '老员工带新', value: '1' },
  { label: '导师一对一', value: '2' },
  { label: '岗前带薪培训', value: '3' },
  { label: '内部定期培训', value: '4' },
  { label: '专业技能培训', value: '5' },
  { label: '内部课程资源', value: '6' },
  { label: '大牛带队', value: '7' },
  { label: '人脉积累', value: '8' },
  { label: '国内外进修', value: '9' },
  { label: '校招培养', value: '10' },
]as const

/** 保险 */
export const INSURANCE_MAP = [
  { label: '定期体检', value: '1', icon: 'yp-dingqitijian', description: '为员工提供带薪年休假' },
  { label: '五险一金', value: '2', icon: 'yp-wuxianyijin-05', description: '缴纳养老保险、医疗保险、失业保险、工伤保险、生育保险和住房公积金' },
  { label: '补充医疗保险', value: '3', icon: 'yp-buchongyiliaoxian', description: '补充医疗保险、商业医疗保险、社会互助和社区医疗保险等多种形式' },
  // { label: '意外险', value: '4', icon: 'yp-yiwaixian', description: null },
]

/** 期权薪资 */
export const STOCK_OPTION_SALARY_MAP = [
  { label: '全勤奖', value: '1', icon: 'yp-quanqinjiang-08', description: null },
  { label: '年终奖', value: '2', icon: 'yp-nianzhongjiang', description: '年末给予员工年终奖励，对一年来的工作业绩给予肯定' },
  { label: '绩效奖金', value: '3', icon: 'yp-jixiaojiangjin', description: null },
  { label: '保底工资', value: '4', icon: 'yp-baodigongzi', description: null },
  { label: '底薪加提成', value: '5', icon: 'yp-dixinjiaticheng', description: null },
  { label: '股票期权', value: '6', icon: 'yp-gupiaoqiquan', description: '对优秀员工提供股票期权' },
  { label: '企业年金', value: '7', icon: 'yp-qiyenianjin', description: null },
  { label: '加班补助', value: '8', icon: 'yp-jiabanbuzhu', description: null },
  { label: '夜班补助', value: '9', icon: 'yp-yebanbuzhu', description: null },
  { label: '节假日加班费', value: '10', icon: 'yp-jiejiarijiabanfei', description: null },
  { label: '法定节假日三薪', value: '11', icon: 'yp-fadingjiejiarisanxin', description: null },
  { label: '工龄奖', value: '12', icon: 'yp-gonglingjiang', description: null },
]

/** 度假休假 */
export const LEAVE_VACATION_MAP = [
  { label: '带薪年假', value: '1', icon: 'yp-daixinnianjia', description: '为员工提供带薪年休假' },
  { label: '员工旅游', value: '2', icon: 'yp-yuangonglvyou', description: null },
]

/** 生活补贴 */
export const LIFE_SUBSIDY_MAP = [
  { label: '交通补贴', value: '1', icon: 'yp-jiaotongbutie', description: '' },
  { label: '生日福利', value: '2', icon: 'yp-shengrifuli', description: '' },
  { label: '节日福利', value: '3', icon: 'yp-jierifuli', description: '法定或者特定节日时提供礼物' },
  { label: '零食下午茶', value: '4', icon: 'yp-lingshixiawucha', description: '' },
  { label: '餐补', value: '5', icon: 'yp-canbu-03', description: '' },
  { label: '包吃', value: '6', icon: 'yp-baochi', description: '' },
  { label: '团建聚餐', value: '7', icon: 'yp-tuanjianjucan', description: '' },
  { label: '包住', value: '8', icon: 'yp-baozhu', description: '' },
  { label: '宿舍有空调', value: '9', icon: 'yp-susheyoukongtiao', description: '' },
  { label: '住房补贴', value: '10', icon: 'yp-zhufangbutie', description: '' },
  { label: '有无线网', value: '11', icon: 'yp-youwuxianwang', description: '' },
  { label: '免费工装', value: '12', icon: 'yp-mianfeigongzhuang', description: '' },
  { label: '免费班车', value: '13', icon: 'yp-mianfeibanche', description: '' },
  { label: '通讯补贴', value: '14', icon: 'yp-tongxunbutie', description: '' },
  { label: '高温补贴', value: '15', icon: 'yp-gaowenbutie', description: '' },
]

/**
 * 企业选项 枚举
 * 用于数据合并
 */
export const enum OPTION_MAP {
  /** 推荐福利 */
  RECOMMEND_BONUS = -1,
  /** 融资规模 */
  FINANCE_SCALE = 1,
  /** 人员规模 */
  STAFF_SIZE = 2,
  /** 公司标准时间 */
  WORK_TIME = 3,
  /** 休息时间 */
  REST_TIME = 4,
  /** 加班情况 */
  OVERTIME_SITUATION = 5,
  /** 保险 */
  INSURANCE = 6,
  /** 薪资期权 */
  STOCK_OPTION_SALARY = 7,
  /** 晋升制度 */
  PROMOTION_SYSTEM = 8,
  /** 人才激励 */
  TALENT_MOTIVATION = 9,
  /** 能力培养 */
  SKILL_DEVELOPMENT = 10,
  /** 度假休假 */
  LEAVE_VACATION = 11,
  /** 生活补贴 */
  LIFE_SUBSIDY = 12
}

/** 企业福利 */
export const COMPANY_BONUS_MAP = [
  {
    id: OPTION_MAP.RECOMMEND_BONUS,
    name: '推荐福利',
    children: [
      { type: OPTION_MAP.INSURANCE, label: '五险一金', value: '2', checked: false, icon: 'yp-wuxianyijin-05', description: '缴纳养老保险、医疗保险、失业保险、工伤保险、生育保险和住房公积金' },
      { type: OPTION_MAP.STOCK_OPTION_SALARY, label: '全勤奖', value: '1', checked: false, icon: 'yp-quanqinjiang-08', description: null },
      { type: OPTION_MAP.LIFE_SUBSIDY, label: '餐补', value: '5', checked: false, icon: 'yp-canbu-03', description: '' },
    ],
  },
  {
    id: OPTION_MAP.INSURANCE,
    name: '保险',
    children: INSURANCE_MAP.map(item => ({
      ...item,
      type: OPTION_MAP.INSURANCE,
      checked: false,
    })),
  },
  {
    id: OPTION_MAP.STOCK_OPTION_SALARY,
    name: '薪资期权',
    children: STOCK_OPTION_SALARY_MAP.map(item => ({
      ...item,
      type: OPTION_MAP.STOCK_OPTION_SALARY,
      checked: false,
    })),
  },
  {
    id: OPTION_MAP.LEAVE_VACATION,
    name: '度假休假',
    children: LEAVE_VACATION_MAP.map(item => ({
      ...item,
      type: OPTION_MAP.LEAVE_VACATION,
      checked: false,
    })),
  },
  {
    id: OPTION_MAP.LIFE_SUBSIDY,
    name: '生活补贴',
    children: LIFE_SUBSIDY_MAP.map(item => ({
      ...item,
      type: OPTION_MAP.LIFE_SUBSIDY,
      checked: false,
    })),
  },
]

/** 企业变更申请 枚举 */
export const enum CHANGE_APPLY_MAP {
  /** 企业头像 */
  COMPANY_AVATAR = 1,
  /** 企业简介 */
  COMPANY_DESCRIPTION = 2,
  /** 企业品牌名称 */
  COMPANY_BRAND_NAME = 3,
  /** 发展历程 */
  COMPANY_DEVELOPMENT_HISTORY = 4,
  /** 获得荣誉 */
  COMPANY_ACHIEVEMENT = 5,
  /** 企业文化 */
  COMPANY_CULTURE = 6,
}

/**
 * 企业变更申请 与中文的映射
*/
export const CHANGE_APPLY_MAP_OBJ = {
  COMPANY_DESCRIPTION: '公司简介',
  COMPANY_DEVELOPMENT_HISTORY: '发展历程',
  COMPANY_ACHIEVEMENT: '获得荣誉',
  COMPANY_CULTURE: '企业文化',
} as const

/** 审核状态 枚举 */
export const enum AUDIT_STATUS {
  /** 审核失败 */
  FAILED = -1,
  /** 审核中 */
  PROCESSING = 1,
  /** 审核通过 */
  SUCCESS = 2
}

export const AUDIT_STATUS_MAP = {
  /** 审核失败 */
  [AUDIT_STATUS.FAILED]: '审核失败',
  /** 审核中 */
  [AUDIT_STATUS.PROCESSING]: '审核中',
  /** 审核通过 */
  [AUDIT_STATUS.SUCCESS]: '审核通过',
}

export const BONUS_TYPE_MAP = {
  [OPTION_MAP.INSURANCE]: 'selectInsurance',
  [OPTION_MAP.STOCK_OPTION_SALARY]: 'selectStockOptionSalary',
  [OPTION_MAP.LEAVE_VACATION]: 'selectLeaveVacation',
  [OPTION_MAP.LIFE_SUBSIDY]: 'selectLifeSubsidy',
}
