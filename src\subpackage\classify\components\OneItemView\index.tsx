import { Image, Text, View } from '@tarojs/components'
import style from './index.module.scss'

type IOneItemViewProps = {
  /**  一级职位ID */
  occId: string
  /** 一级职位对象 */
  item: any
  id?: string
  onClick?: (id: string) => void
  /** 选中的行业ID */
  indSlted?: string
  [key: string]: any
}

const OneItemView = (props: IOneItemViewProps) => {
  const { id = '', occId = '', item = {}, indSlted = {}, onClick } = props
  return (
    <View id={id} className={style.occItem} >
      <Text className={style.occTxt}>{item.name}</Text>
      {
        `${item.extType}` === '3' ? (
          <View className={style.xunView} onClick={() => onClick && onClick(`${indSlted}_${occId}`)}>
            <Text className={style.xunTxt}>查看</Text>
            <Image className={style.xunImg} src="https://staticscdn.zgzpsjz.com/miniprogram/images/xjj/iyp_mini_sx_nor.png" />
          </View>
        ) : null
      }
    </View>
  )
}

export default OneItemView
