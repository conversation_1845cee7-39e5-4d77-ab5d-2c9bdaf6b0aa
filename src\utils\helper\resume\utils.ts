import { store } from '@/core/store'
import { MyResumeDetails, resumeDetailsDef } from '@/store/storage/defData'

type MyDetail = Models['POST/resume/v3/detail/app/myDetailFirstScreen']

function getOccIds(subs: MyDetail['Res']['data']['basicResp']['subs']): number[] {
  const occIds: number[] = []
  if ($.isArrayVal(subs)) {
    subs.forEach(item => {
      const occId = $.getObjVal(item, 'occupationInfo.occId')
      if (occId) {
        occIds.push(occId)
      }
    })
  }
  return occIds
}

/** 查询我的简历信息
 * @param resumeUuid 简历名片的UUID
 */
export function getResumeInfoMe(resumeUuid: string): Promise<MyResumeDetails> {
  return new Promise((resolve) => {
    $.request['POST/resume/v3/detail/app/myDetailFirstScreen']({ resumeUuid }, { hideMsg: true }).then(([_, res]) => {
      if (res.error) {
        resolve({ ...resumeDetailsDef })
        return
      }
      const subs = $.getObjVal(res, 'data.basicResp.subs') || []
      const resumeId = $.getObjVal(res, 'data.basicResp.resumeId')
      const subCount = subs.length || 0
      const occIds = getOccIds(subs)
      const subFullCount = subs.filter((item) => item.positionType === 1).length
      const subPartCount = subCount - subFullCount
      // eslint-disable-next-line no-param-reassign
      res.data.basicResp.subs = subs // 处理subs字段
      resolve({ ...res.data, resumeUuid, resumeId, occIds, subCount, subFullCount, subPartCount })
    }).catch(() => {
      let { myResumeDetails } = store.getState().storage
      myResumeDetails = $.deepClone(myResumeDetails)
      resolve({ ...myResumeDetails })
    })
  })
}

/** 通用模板展示接口
 * @param occIds 职位ID 数组
 * @param isFilter 将控件信息过滤为status=1的
 */
export function getTempShow(occIds: number[], isFilter = true): Promise<Models['POST/resume/v3/template/show']['Res']['data']> {
  return new Promise(async (resolve, reject) => {
    if (!$.isArrayVal(occIds)) {
      reject({ error: true })
      return
    }
    const [, res] = await $.request['POST/resume/v3/template/show']({
      occIds,
    }, { hideMsg: true })
    const templates = $.getObjVal(res, 'data.templates') || []
    if (isFilter && $.isArrayVal(templates)) {
      templates.forEach((item) => {
        const controlInfoList = $.getObjVal(item, 'templateInfo.controlInfoList') || []
        if ($.isArrayVal(controlInfoList)) {
          item.templateInfo.controlInfoList = controlInfoList.filter((control) => control.status == 1)
        }
      })
    }
    resolve(res.data)
  })
}
