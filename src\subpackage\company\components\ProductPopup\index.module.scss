

.header {
  height: 112px;
  display: flex;
  align-items: center;
  padding-left: 32px;
}
.wrap {
  height: 65vh;
}
.avatar {
  display: block;
  margin: 0 auto;
  width: 136px;
  height: 136px;
  border-radius: 17px;
  border: 1px solid #e9edf3ff;
  margin-bottom: 32px;
}

.swiperTitle {
  font-size: 34px;
  color: rgba(0, 0, 0, 0.85);
  margin-bottom: 32px;
  text-align: center;
  padding: 0 32px;
}

.swiper {
  height: calc(65vh - 112px);

}
.swiperItem {
  // width: calc(100% - 64px);
  padding: 32px;
  box-sizing: border-box;
}
.itemHeight {
  height: calc(65vh - 112px - 110px);
}

.more {
  position: absolute;
  right: 0px;
  bottom: 2px;
  font-size: 26px;
  color: #0092FFFF;
  padding-left: 100px;
  background: linear-gradient(to right, rgba(255, 255, 255, 0.3) 10%, rgba(255, 255, 255, 0.6) 20%, rgba(255, 255, 255, 0.9) 30%, rgba(255, 255, 255, 1));
}

.moreOpen {
  position: absolute;
  right: 0px;
  bottom: 2px;
  font-size: 26px;
  color: #0092FFFF;
}


.inner {
  position: relative;
  width: 686px;
  // max-height: 60%;
  // overflow: scroll;
  margin: 0 auto;
  font-size: 26px;
  color: #000000A6;
  line-height: 42px;
}

.overflow {
  @include textrow(2);
}

.scrollView {
  max-height: calc(65vh - 500px);
  width: 100%;
}

.docs {
  display: flex;
  justify-content: center;
  padding-top: 32px;
}
.point {
  width: 26px;
  height: 6px;
  border-radius: 4px;
  background-color: #E9EDF3FF;
  margin-right: 16px;
}

.active {
  background-color: #0092FFFF;
}