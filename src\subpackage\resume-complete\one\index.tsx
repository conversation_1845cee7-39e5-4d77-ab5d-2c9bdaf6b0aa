import { useEffect, useState } from 'react'
import dayjs from 'dayjs'
import Page from '@/components/Page'
import style from './index.module.scss'
import HeadTitle from './components/HeadTitle'
import NameView from './components/NameView'
import Gender from './components/Gender'
import BirthDay from './components/BirthDay'
import ResumeFooter from '@/pages/resume/components/ResumePublish/components/ResumeFooter'
import { getBtmHeight } from '../utils'
import storage from '@/store/storage/storage'
import { handlePublishResult, savePublishData } from '@/core/utils/publish'
import { getResumeDetails } from '@/utils/helper/resume/index'
import CustomNavbar from '@/components/CustomNavbar'
import { publishResumeResultReport } from '@/pages/resume/utils'
import { store } from '@/core/store/index'

export default function Index() {
  const { publishRuleSwitch, publishFlow, userInfo = {}, template, params, isBack } = $.router.data

  const [btmHeight, setBtmHeight] = useState(0)
  const [codeObj] = useState({ F_Name: 'userName', F_Gender: 'gender', F_Age: 'birthday' })
  const [data, setData] = useState<any>({
    baseInfo: { birthday: '', gender: 1, userName: '' },
    oriBaseInfo: {},
    subBtnTxt: '发布简历',
    mustObj: {
      // userName: { status: true, must: true },
      // gender: { status: true, must: true },
      // birthday: { status: true, must: true },
    },
  })

  useEffect(() => {
    setTimeout(() => {
      getBtmHeight(setBtmHeight)
    }, 100)
    getBaseInfoData()
  }, [])

  // eslint-disable-next-line sonarjs/cognitive-complexity
  const getBaseInfoData = async () => {
    if ($.isEmptyObject(userInfo) || $.isEmptyObject(template)) {
      $.router.push('/pages/index/index')
      return
    }
    if (['1', '2'].includes(`${userInfo.realNameStatus}`)) {
      $.router.replace(
        '/subpackage/resume/resume_publish/complete/two/index',
        {},
        { publishFlow, template, params: { ...params }, publishRuleSwitch, isBack },
      )
      return
    }
    const { templateInfo } = template || {}
    const { controlInfoList } = templateInfo || {}
    const mustObj: any = {}
    if ($.isArrayVal(controlInfoList)) {
      controlInfoList.forEach(ct => {
        const { status, controlCode, scenes } = ct || {}
        if (codeObj[controlCode]) {
          const scene = ($.isArrayVal(scenes) ? scenes : []).find((s) => s.displayScene == 'RESUME_PUBLISH_DISPLAY')
          const { ifMust } = scene || {}
          mustObj[codeObj[controlCode]] = {
            must: ifMust,
            status: status == 1,
          }
        }
      })
    }
    const { subStep } = publishFlow || {}
    const { nameAuditStatus } = userInfo || {}
    const baseInfo: any = { ...(userInfo || {}) }
    const oriBaseInfo: any = { ...(userInfo || {}) }
    const pubishData = storage.getItemSync('pubishData')
    const { gender, userName, birthday } = pubishData || {}
    if (gender) {
      baseInfo.gender = gender
    }
    if (userName && nameAuditStatus != 1) {
      baseInfo.userName = userName
    }
    if (birthday) {
      baseInfo.birthday = birthday
    }
    if (['先生', '女士'].includes(baseInfo.userName) && nameAuditStatus != 1) {
      baseInfo.userName = ''
      oriBaseInfo.userName = ''
    }

    let subBtnTxt = '发布简历'
    if (subStep == 1) {
      subBtnTxt = '保存'
    } else if (subStep == 3) {
      subBtnTxt = '下一步'
    }
    setData(prev => ({ ...prev, oriBaseInfo, subBtnTxt, mustObj, baseInfo }))
  }

  /** onChanges事件 */
  const onChanges = (e, type) => {
    let { value } = e.detail
    if (type == 'userName') {
      value = value.trim()
    }
    if (type == 'birthday' && (value.length == 6 || value.length == 7)) {
      value = `${value}-01`
    }
    savePublishData(type, value)
    if (type == 'userName' && value != e.detail.value) {
      setData(prev => ({ ...prev, baseInfo: { ...prev.baseInfo, [type]: e.detail.value } }))
      setTimeout(() => {
        setData(prev => ({ ...prev, baseInfo: { ...prev.baseInfo, [type]: value } }))
      }, 50)
    } else {
      setData(prev => ({ ...prev, baseInfo: { ...prev.baseInfo, [type]: value } }))
    }
  }
  const onInputClick = () => {
    const { nameAuditStatus } = data.baseInfo || {}
    if (nameAuditStatus == 1) {
      $.msg('审核中，无法修改')
    }
  }

  const onClear = () => {
    setData((prev) => ({ ...prev, baseInfo: { ...prev.baseInfo, userName: '' } }))
  }

  const onConfirm = async () => {
    const checkParam = checkConfirmValue()
    if (!checkParam) return
    const { subStep } = publishFlow || {}
    const nParams: any = { ...params, userReq: checkParam }
    const { occAreaReq } = nParams
    if (subStep == 3 || $.isEmptyObject(checkParam)) {
      $.router.replace('/subpackage/resume-complete/two/index', {}, { publishFlow, template, params: nParams, publishRuleSwitch, isBack })
      return
    }
    $.showLoading('请求中...')
    let rqPath = ''
    let nRqParams = {}
    let isPublish = false
    if (subStep == 1) {
      rqPath = 'POST/resume/v3/publish/updateUserInfo'
      nRqParams = { ...checkParam }
      isPublish = false
    } else {
      rqPath = 'POST/resume/v3/publish/publish'
      isPublish = true
      nRqParams = { ...nParams }
    }
    $.request[rqPath](nRqParams, { hideMsg: true }).then(async () => {
      storage.removeSync('pubishData')
      if (subStep != 1) {
        await getResumeDetails()
        $.hideLoading()
        isPublish && publishResumeResultReport('1', occAreaReq)
        await $.msg('发布成功')
      } else {
        $.hideLoading()
      }
      judgeJump()
    }).catch((res) => {
      $.hideLoading()
      handlePublishResult(res[1] || {}, '发布')
      isPublish && publishResumeResultReport('0', occAreaReq)
    })
  }

  const checkConfirmValue = () => {
    const eparams: any = {}
    const { baseInfo, oriBaseInfo } = data

    const { templateInfo } = template || {}
    const { controlInfoList } = templateInfo || {}
    const currentYear = new Date().getFullYear()
    const resumeUuid = $.getObjVal(store.getState().storage.myResumeDetails, 'resumeUuid')

    if (baseInfo.userName && (oriBaseInfo.userName != baseInfo.userName || !resumeUuid)) {
      if (!$.allChinese(baseInfo.userName)) {
        $.msg('姓名请输入2-5字纯中文')
        return null
      }
      if (baseInfo.userName == '先生' || baseInfo.userName == '女士') {
        $.msg(`新姓名不能是${baseInfo.userName}`)
        return null
      }
      eparams.name = baseInfo.userName
    }
    if ($.isArrayVal(controlInfoList)) {
      const misFields = controlInfoList.filter(ct => {
        const { status, controlCode, scenes } = ct || {}
        const scene = ($.isArrayVal(scenes) ? scenes : []).find((s) => s.displayScene == 'RESUME_PUBLISH_DISPLAY')
        const { ifMust } = scene || {}
        return ifMust && status == 1 && codeObj[controlCode] && !baseInfo[codeObj[controlCode]]
      })

      if (misFields.length > 0) {
        const tCodeName = { F_Name: '姓名', F_Gender: '性别', F_Age: '出生年月' }
        const fstMisField = misFields[0]
        const { controlName, controlCode: fsCode } = fstMisField || {}
        $.msg(`请填写${tCodeName[fsCode] || controlName}`)
        return null
      }
    }
    if (baseInfo.gender && (baseInfo.gender != oriBaseInfo.gender || !resumeUuid)) {
      eparams.gender = baseInfo.gender
    }
    if (baseInfo.birthday && (baseInfo.birthday != oriBaseInfo.birthday || !resumeUuid)) {
      const year = dayjs(baseInfo.birthday).format('YYYY')
      const age = currentYear - Number(year)
      // 年龄输入范围
      if (age < 16 || age > 100) {
        $.msg('年龄范围为16至100岁')
        return null
      }
      eparams.birthDay = baseInfo.birthday
    }
    return eparams
  }

  const judgeJump = async () => {
    const { perfects } = publishFlow || {}
    if ($.isArrayVal(perfects, 2) && perfects[1] == 1) {
      $.router.replace('/subpackage/resume-complete/two/index', {}, { publishFlow, template, params, publishRuleSwitch, isBack })
    } else {
      if (isBack) {
        $.router.back()
        return
      }
      const data: any = { origin: 'resume', listType: 2 }
      const { occAreaReq } = params || {}
      const { hopeArea } = occAreaReq || {}
      if ($.isArrayVal(hopeArea)) {
        data.areaId = `${hopeArea[0]}`
      }
      $.router.push('/pages/index/index', {}, data)
    }
  }
  return (
    <Page backgroundColor='#fff'>
      <V className={style.page}>
        <CustomNavbar isBack title=' ' />
        <V className={style.content}>
          <HeadTitle />
          <NameView value={data.baseInfo.userName} mustObj={data.mustObj.userName} disabled={userInfo.nameAuditStatus == 1} onChanges={onChanges} onInputClick={onInputClick} onClear={onClear} />
          <Gender value={data.baseInfo.gender} mustObj={data.mustObj.gender} onChanges={onChanges} />
          <BirthDay value={data.baseInfo.birthday} mustObj={data.mustObj.birthday} onChanges={onChanges} />
          <V style={{ height: `${btmHeight}px`, width: '100%' }}></V>
        </V>
        <ResumeFooter id="fotter" publishRuleSwitch={publishRuleSwitch} btnTxt={data.subBtnTxt} onConfirm={onConfirm} isPdBtm />
      </V >
    </Page>
  )
}
