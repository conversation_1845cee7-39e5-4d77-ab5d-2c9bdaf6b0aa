/*
 * @Date: 2024-11-28 17:02:29
 * @Description: 登录浮标
 */

import { View as V, Text as T, Button } from '@tarojs/components'
import cn from 'classnames'
import Click from '@/components/Click'
import s from './index.module.scss'
import { useSelector } from '@/core/store'
import { useLogin } from '@/core/utils/login'
import LoginBtn from '@/components/LoginBtn'

export default () => {
  const token = useSelector((state) => state.storage.token)

  const [logionProps] = useLogin({})

  if (token) {
    return null
  }

  return (
    <>
      <V className={cn(s.height)} />
      <V className={cn(s.body)}>
        <T className={s.text}>登录后可获得更多精准职位匹配</T>
        <Click className={s.btn}>立即登录<LoginBtn {...logionProps} style={{ position: 'absolute', top: 0, bottom: 0, left: 0, right: 0, opacity: 0 }} /> </Click>
      </V>
    </>
  )
}
