.item {
  padding: 32rpx 0;
}

.label {
  display: flex;
  align-items: center;
  color: rgba(0, 0, 0, 0.85);
  font-weight: bold;
  font-size: 38rpx;
  line-height: 54rpx;
}

.must {
  font-size: 28rpx;
  color: rgba(232, 54, 46, 1);
  line-height: 40rpx;
}

.genderView {
  margin-top: 24rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.gender {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 28rpx 16rpx;
  border-radius: 16rpx;
  color: rgba(0, 0, 0, 0.85);
  font-size: 34rpx;
  line-height: 48rpx;
  background: rgba(245, 247, 252, 1);
  width: 331rpx;
}

.genderSlted {
  border: 2rpx solid rgba(0, 146, 255, 1);
  background: rgba(224, 243, 255, 1);
  color: rgba(0, 146, 255, 1);
}
