import cn from 'classnames'
import type { StandardProps } from '@tarojs/components'
import s from './index.module.scss'
import IconFont from '@/components/IconFont'

type Props = StandardProps & {
  /** 状态 */
  checkStatus: number
  /** 错误的状态信息描述 */
  checkText?: string
}

/** 通用单元格组件 */
export default function Index(props: Props) {
  const onClick = () => {
    const { checkStatus, checkText } = props
    if (checkStatus == 3) {
      $.router.push(
        '/subpackage/my-resume/err-check/index',
        {},
        { content: checkText || '' },
      )
    }
  }
  return (
    <>
      { props.checkStatus == 1
        && <V className={cn(s['status-pass'], props.className)}>审核中</V>
      }

      {props.checkStatus == 3
        && <V onClick={() => onClick()}
          className={cn(s['status-err'], props.className)}
        >
          <V className={s['err-text']}>审核失败</V>
          <V className={s['err-icon']}>
            <IconFont type="yp-icon_hyzx_bzyfk" size={32} />
          </V>
        </V>
      }
    </>
  )
}
