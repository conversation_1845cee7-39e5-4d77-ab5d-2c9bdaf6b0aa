.container {
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    flex: 1;
    position: fixed;
    left: 0;
    top: 0;
    bottom: 0;
    right: 0;
}

.content {
    display: flex;
    flex-direction: column;
    align-items: center;
}

.emptyIc {
    width: 200px;
    height: 200px;
}

.text {
    font-size: 30px;
    font-weight: 400;
    line-height: 42px;
    margin-top: 24px;
    color: $text85;
}

.subText {
    margin-top: 24px;
    font-weight: 400;
    font-size: 26px;
    line-height: 36px;
    color: $text45;
}

.count {
    color: rgba(0, 146, 255, 1);
}

.btn {
    margin-top: 24px;
    width: 200px;
    height: 80px;
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    color: #fff;
    background:  rgba(0, 146, 255, 1);
    border-radius: 16px;
}

.hidden {
    display: none !important;
}
