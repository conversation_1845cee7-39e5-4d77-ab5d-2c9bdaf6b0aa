import { Picker } from '@tarojs/components'
import { useEffect, useState } from 'react'
import { useSelector } from '@/core/store'

type Props = {
  /** 默认选中的民族code */
  value?: number
  /** 刷新事件 */
  refresh?(): void
  /** 子元素，用于触发 picker */
  children: React.ReactNode
}

export default function Index(props: Props) {
  const { children } = props

  const nationList = useSelector((state) => state.global.nationList)
  const [index, setIndex] = useState(0)

  useEffect(() => {
    let findIndex = nationList.findIndex((item) => item.code === props.value)
    findIndex = findIndex < 0 ? 0 : findIndex // 找不到就默认选中第一项
    setIndex(findIndex)
  }, [props.value, nationList])

  const pickerChange = async ({ detail: { value } }) => {
    setIndex(value)

    const data = nationList[value]
    if (data.code == props.value) {
      return
    }

    try {
      $.showLoading('设置中...')
      const [, res] = await $.request['POST/account/v1/userBase/updateNation']({
        nationCode: data.code,
        nationName: data.name,
      })
      $.hideLoading()

      if (res.code != 0) {
        $.msg(res.message || '设置失败')
        return
      }

      props.refresh?.()
    } catch {
      $.hideLoading()
      $.msg('网络错误，请重试')
    }
  }

  const arr = nationList.map(item => item.name)

  return (
    <Picker
      mode='selector'
      onChange={pickerChange}
      range={arr}
      value={index}
    >
      {children}
    </Picker>
  )
}
