.container {
    display: flex;
    flex-direction: column;
}

.cardTitle {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
}

.title {
    font-size: 34px;
    font-weight: bold;
    color: $text85;
    line-height: 48px;
}

.extra {
    font-size: 28px;
    font-weight: 400;
    color: $text45;
    line-height: 40px;
    display: flex;
    flex-direction: row;
    align-items: center;
}

.compact {
    display: flex;
    flex-direction: row;
    // gap: 16px;
    flex-wrap: wrap;
    overflow-y: hidden;
    position: relative;
    margin-top: 32px;
}

.gridItem {
    padding: 0 20px;
    height: 56px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 26px;
    font-weight: 400;
    color:$text65;
    // line-height: 36px;
    background:  rgba(245, 247, 252, 1);
    border-radius: 8px;
    margin: 0 16px 16px 0;
}

.content {
    margin-top: 32px;
    white-space: pre-line;
    overflow-wrap: break-word;
    font-size: 32px;
    font-weight: 400;
    color: $text65;
    line-height: 64px;
    position: relative;
    // max-height: calc( 64px * 15 );
    overflow: hidden;
    position: relative;
    opacity: 0;
}

.showContent {
    opacity: 1 !important;
    max-height: calc( 64px * 15 );
}


.viewAll {
    width: 200px;
    position: absolute;
    bottom: 0;
    right: 0;
    z-index: 5;
    background: linear-gradient(to right,  rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, 1) 25%, rgba(255, 255, 255, 1) 100%);
    display: flex;
    flex-direction: row;
    justify-content: flex-end;
    color: #0092ff;
    opacity: 0;
}

.complain {
    margin-right: 4px;
}

.arrowDown {
    margin-left: 12px;
}


.hidden {
    display: none !important;
}

.hiddenText {
    opacity: 0 !important;
    max-height: none !important;
}