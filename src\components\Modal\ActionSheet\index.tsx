/*
 * @Date: 2024-11-21 09:46:57
 * @Description: 弹窗Alert
 */

import { View } from '@tarojs/components'
import cn from 'classnames'
import Click from '@/components/Click'
import s from './index.module.scss'
import useModal from '../hooks'
import Popup from '@/components/Popup'

export default () => {
  const { options, visible, onResolve, onReject } = useModal('actionSheet')
  const { itemColor, title, cancelText, itemList = [], syncClose, zIndex } = options

  const isSyncClose = (item) => {
    return item.syncClose != null ? item.syncClose : syncClose
  }
  const onClick = async (item: any, index: number) => {
    const data = {
      $type: 'confirm',
      $index: index,
      ...item,
    }
    if (options.success) {
      if (isSyncClose(item)) {
        const isBool = await options.success(data)
        if (isBool) {
          onResolve(data)
        }
        return
      }
      options.success(data)
    }
    onResolve(data)
  }
  return (
    <Popup
      catchMove
      disableScroll
      visible={visible}
      zIndex={zIndex}
      contentStyle="background: transparent;"
      onClose={() => onReject({ $type: 'mask' })}
    >
      <View className={s.sheetBox}>
        <View className={s.sheetList}>
          {!!title && <View className={s.sheetTitle}>{title}</View>}
          {itemList.map((item, index) => (
            <Click
              key={index}
              className={cn(s.sheetItem, item.active && s.active)}
              onClick={() => onClick(item, index)}
              style={`color: ${item.itemColor ? item.itemColor : itemColor || 'rgba(0, 0, 0, 0.85)'};${item.style}`}
            >{item.name}</Click>
          ))}
          {!!cancelText && (
            <View onClick={() => onReject({ $type: 'cancel' })} className={cn(s.sheetItem, s.itemClose)}>{cancelText}</View>
          )}
        </View>
      </View>
    </Popup>
  )
}
