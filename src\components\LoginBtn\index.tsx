import { Button, ButtonProps, ITouchEvent } from '@tarojs/components'
import { useCallback, useRef, useState } from 'react'

type LoginBtnProps = ButtonProps

/**
 * 处理快手小程序性能太差导致多次点击重复拉起登陆弹窗
 */
export default (props: LoginBtnProps) => {
  const { children, onClick, openType, onGetPhoneNumber, ...rest } = props

  const [loading, $loading] = useState(false)
  const timeRef = useRef<any>()

  const onBtnClick = useCallback((event: ITouchEvent) => {
    if (timeRef.current) {
      clearTimeout(timeRef.current)
    }
    timeRef.current = setTimeout(() => {
      $loading(false)
    }, 1000)
    $loading(true)
    onClick && onClick(event)
  }, [loading, $loading, onClick])
  if (loading) {
    return <Button {...rest} onClick={onBtnClick}>{children}</Button>
  }
  return <Button {...rest} onClick={onBtnClick} openType={openType} onGetPhoneNumber={onGetPhoneNumber}>{children}</Button>
}
