/*
 * @Date: 2024-11-23 10:18:38
 * @Description: 招工卡片
 */

import { memo, useMemo } from 'react'
import cn from 'classnames'
import { Button, RichText, View as V, Image } from '@tarojs/components'
import s from './index.module.scss'
import { useSelector } from '@/core/store'
import useCardReport from '@/hooks/useCardExposure'
import { haversineDistance } from '@/utils/location/index'
import { useLogin } from '@/core/utils/login'

/** 这几个需要展示为蓝色 */
const blueTag = [1, 10, 11, 12]

export default memo(({ info, onClickCard, index, freeRegistration, extraData }: any) => {
  const userLocation = useSelector((state) => state.storage.userLocation)
  const distance = userLocation.success
    && info.location
    && info.location.latitude
    && info.location.longitude
    ? haversineDistance(
      userLocation.latitude,
      userLocation.longitude,
      info.location.latitude,
      info.location.longitude,
    )
    : null
  const onCardClick = () => {
    onClickCard.current(info, String(index + 1))
  }

  const classId = `card-${index}-${info.jobId}`
  const applied = info.isIm
  const logged = useSelector((state) => !!state.storage.token)
  const { userId } = useSelector((state) => state.storage.userInfo)

  useCardReport(classId, () => [$.getElementTop('#listbody'), 0], async (exposureDuration) => {
    $.report.event('homeWorklistExposure', {
      info_id: String(info.jobId),
      keywords_source: '',
      exposure_duration: Number(exposureDuration / 1000).toFixed(2),
      check_degree: String(info.checkDegreeStatus),
      detailed_address: info.address,
      post_distance: distance || '',
      job_location: [info.location?.longitude, info.location?.latitude].join(','),
      position_status: String(info.isEnd.code),
      divisionline_area: '0',
      free_information: '免费',
      part_time_options: '',
      extInfo: '',
      feedback_exposure: '0',
      occupations_type: info.occMode == 1 ? '订单' : '招聘',
      search_result: extraData?.search_result || '',
      ...info.buriedData,
    })
  })

  const [loginProps] = useLogin({
    success: async () => {
      freeRegistration()
    },
    disableSuccessMsg: true,
  })

  /** 是我的职位信息 */
  const isMyJob = useMemo(() => {
    return Number(info.userId) === Number(userId)
  }, [info, userId])

  return (
    <V className={`${s.card} ${classId}`} onClick={() => onCardClick()}>
      <RichText className={s.title} nodes={info.title} />
      <V className={s.tags}>
        {info.showTags.map((item) => {
          return (
            <V
              className={cn(s.tag, blueTag.includes(item.type) && s.tagBlue)}
              key={item.name}
            >
              {item.name}
            </V>
          )
        })}
      </V>
      <V className={s.footer}>
        <V className={s.address}>
          {info.address} {distance}
        </V>
        <V className={s.footerInfo}>
          <V className={s.date}>{info.showDate}</V>
          {
            !isMyJob ? <V>
              {!applied ? (
                !logged ? (
                  <Button {...loginProps} className={cn(s.sendBtn, s.btn)} onClick={(e) => e.stopPropagation()}>
                    <Image src='https://cdn.yupaowang.com/yupao_mini/publish.svg' className={s.sendIcon} />
                    <V>免费报名</V>
                  </Button>
                ) : (
                  <V className={s.sendBtn} onClick={(e) => { e.stopPropagation(); freeRegistration(e) }}>
                    <Image src='https://cdn.yupaowang.com/yupao_mini/publish.svg' className={s.sendIcon} />
                    <V>免费报名</V>
                  </V>
                )
              ) : (
                <V className={s.sendedBtn}>
                  <V>已报名</V>
                </V>
              )}
            </V> : <></>
          }
        </V>
      </V>
    </V>
  )
})
