.content {
  padding: 0;
  display: flex;
  align-items: center;
}

.cell-card {
  padding: 0 !important;
}

.c-text {
  flex: 1;
}

.placeholder {
  padding: 40px 0;
  color: rgba(0, 0, 0, 0.25);
}

.list {
  padding-top: 40px;
  &.top-line {
    @include top-line-b();
  }
}

.item {
  padding: 32px 0;
  /** @include top-line-b(); */

  &.pd-top-none {
    padding-top: 0;
    border-top: none;
  }
}

.title {
  line-height: 42px;
  color: rgba(0, 0, 0, 0.85);
  font-weight: bold;
  padding-bottom: 32px;
}

.head {
  display: flex;
  align-items: center;
  .label {
    @include ellip();
    max-width: 360px;
  }
  .value {
    padding-left: 16px;
  }
  .icon {
    flex: 1;
    display: inline-flex;
    justify-content: flex-end;
  }
}

.content-text {
  color: rgba(0, 0, 0, 0.65);
  line-height: 40px;
  padding-top: 8px;
  font-size: 26px;
  @include ellip(2);
}
