.tabbar {
  display: flex;
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
  background-color: #fff;
  z-index: 10;
  box-sizing: content-box;
  height: 112px;
  box-shadow: 0px -4px 8px #32343c0d;
}

.item {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
}

.img {
  width: 48px;
  height: 48px;
}

.text {
  font-weight: 500;
  font-size: 20px;
  margin-top: 8px;
}

.height {
  box-sizing: content-box;
  height: 112px;
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
}