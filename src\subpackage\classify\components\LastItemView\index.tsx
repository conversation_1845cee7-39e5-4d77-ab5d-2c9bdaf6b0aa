import { Text, View } from '@tarojs/components'
import style from './index.module.scss'

type ILastItemViewProps = {
  /** 职位ID,格式:行业_职位ID */
  occId: string
  /** 职位名称 */
  item: any
  // 选中的值
  slted?: string
  // 点击方法
  onClick?: (id: string) => void
  [key: string]: any
}

const LastItemView = (props: ILastItemViewProps) => {
  const { occId, item = {}, slted, onClick } = props

  return (
    <View className={`${style.lastItem} ${slted ? style.lastSlted : ''}`} onClick={() => onClick && onClick(occId)}>
      <Text>{item.name}</Text>
    </View>

  )
}

export default LastItemView
