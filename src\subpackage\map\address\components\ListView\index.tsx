import type { StandardProps } from '@tarojs/components'
import { ScrollView } from '@tarojs/components'
import cn from 'classnames'
import { memo, useEffect, useState } from 'react'
import { getAreaId } from '../../utils'
import { TTreeFullVal } from '../../utils/index.d'
import style from './index.module.scss'
import IconFont from '@/components/IconFont'

type ListViewProps = StandardProps & {
  data: TTreeFullVal
  levelUI: number | string
  /** 当前层级 */
  level: 1 | 2 | 3
  /** 是否是多选组件 */
  isMultiple: boolean,
  /** 已选中的地址信息 */
  selectAddrs: TTreeFullVal
  /** 是否有显示底部按钮 */
  isFooter?: boolean,
  /** 禁用的id */
  disabledIds?: number[],
  /** ListView的高度 */
  height?: number,
  click: (item: TTreeFullVal[0]) => void
}

type MapIdNum = { [key: string]: TTreeFullVal[0] & { count: number} }

/** 计算id出现的次数 */
function handlerMapCount(selectAddrs: TTreeFullVal): MapIdNum {
  const mapId = {} as MapIdNum

  function updateMap(id: undefined | string | number, item: TTreeFullVal[0]) {
    if (id && id != 1) {
      if (mapId[id]) {
        mapId[id].count += 1
      } else {
        mapId[id] = { ...item, count: 1 }
      }
    }
  }

  selectAddrs.forEach((item) => {
    const { gid, pid, id } = item
    updateMap(gid, item)
    updateMap(pid, item)
    updateMap(id, item)
  })

  return mapId
}

const ShowSelect = memo((props: {
  /** 是否是选择的地址 */
  isSelect: boolean,
  /** 当前选择的地址id和数量 */
  mapIdNum: MapIdNum,
  /** 当前地址信息 */
  area: TTreeFullVal[0],
  /** 是否是多选组件 */
  isMultiple: boolean,
  /** 页面UI层数 */
  levelUI: number | string
  /** 当前层级 */
  level: 1 | 2 | 3
}) => {
  const { mapIdNum, area, isSelect, isMultiple } = props
  const [showType, setShowType] = useState<'count' | 'single' | 'check' | ''>('')

  useEffect(() => {
    if (isSelect) {
      setShowType('check')
      if (mapIdNum[area.id] && !area.isFull && area.level == 1) {
        setShowType(isMultiple ? 'count' : 'single')
      }
      return
    }
    if (!mapIdNum[area.id] || area.isFull || mapIdNum[area.id].count < 1) {
      setShowType('')
      return
    }
    setShowType(isMultiple ? 'count' : 'single')
  }, [mapIdNum, area])

  return (<>
    {showType == 'count' && mapIdNum[area.id]
      && <V className={style['item-count']}>{mapIdNum[area.id].count}</V>}
    {showType == 'single'
      && <V className={style['item-single']}></V>}
    {showType == 'check'
      && <IconFont className={style.icon} color="#0092ff" type='yp-duih' />
    }
  </>)
})

const ListView = memo((props: ListViewProps) => {
  const { data, levelUI, level, isMultiple, selectAddrs, height } = props
  const [intoView, setIntoView] = useState('')
  const [selectIds, setSelectIds] = useState<number[]>([])
  /** 以地址id为key, 值为地址信息 */
  const [mapIdNum, setMapIdNum] = useState<MapIdNum>({})
  const onClick = (area: TTreeFullVal[0]) => {
    const disabledIds = props.disabledIds || []
    if ($.isArrayVal(disabledIds) && disabledIds.some(id => id == area.id)) {
      $.msg('此地区暂未提供服务')
    } else {
      props.click(area)
    }
  }
  useEffect(() => {
    if (!$.isArrayVal(selectAddrs)) {
      setSelectIds([])
      setMapIdNum({})
      return
    }

    const newMapIdNum = handlerMapCount(selectAddrs)
    const newSelectIds = selectAddrs.map(item => +item.id)
    setMapIdNum(newMapIdNum)
    setSelectIds(newSelectIds)

    let delay = 0
    let provinceId: number | string = ''
    let cityId: number | string = ''
    let districtId: number | string = ''

    if (selectAddrs && selectAddrs.length > 0) {
      const areaIds = getAreaId(selectAddrs[0])
      provinceId = areaIds.provinceId
      cityId = areaIds.cityId
      districtId = areaIds.districtId

      if (provinceId && Number(provinceId) > 30) {
        delay = 1000
      }
    }

    setTimeout(() => {
      if (!intoView && provinceId) {
        switch (Number(level)) {
        case 1:
          setIntoView(`yp${level}_${provinceId}`)
          break
        case 2:
          setIntoView(`yp${level}_${cityId}`)
          break
        case 3:
          setIntoView(`yp${level}_${districtId}`)
          break
        }
      }
    }, delay)
  }, [selectAddrs])

  return (
    <ScrollView
      scrollY
      scrollIntoView={intoView}
      className={cn(style.scroll, style[`list-${level}`])}
      style={{ height: height ? `${height}px` : '100%' }}
      show-scrollbar={false}
    >
      {
        data && data.map(item => {
          return (
            <V
              id={`yp${level}_${item.id}`}
              key={item.id}
              className={cn(style.item, item.checked && style.checked)}
              onClick={() => onClick(item)}
            >
              <V className={style['item-name']}>{ item.isFull ? '全' : '' }{ item.name }</V>
              {selectAddrs && selectAddrs.length > 0 && item.name != '全国'
                && <V className={style['item-right']}>
                  <ShowSelect
                    mapIdNum={mapIdNum}
                    isSelect={selectIds.includes(+item.id)}
                    area={item}
                    level={level}
                    isMultiple={isMultiple}
                    levelUI={levelUI}
                  />
                </V>
              }
            </V>
          )
        })
      }
      {/* bottom */}
      <V className={props.isFooter ? style.bottom : style['bottom-safe']}></V>
    </ScrollView>
  )
})

export default ListView
