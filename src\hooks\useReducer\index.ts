import { useReducer as $useReducer, useMemo } from 'react'


/** 
 * 创建reducer
 * @template InitState 初始状态类型
 */
function createReducer <InitState>() {
  return (preState: InitState, actionState: Partial<InitState> | ((state: Partial<InitState>) => Partial<InitState>)) => {
    return {
      ...preState,
      ...(typeof actionState === 'function' ? actionState(preState) : actionState)
    }
  }
}

/**
 * useReducer 函数，处理页面很多 useState 的 情况。
 * @param initializerArg 初始化的值
 * @returns 返回 状态值 和 dispatch 函数，dispatch函数用于更新状态
 */
function useReducer<InitState>(initializerArg: InitState) {
  const reducer = useMemo(() => {
    return createReducer<InitState>()
  }, [])
  return $useReducer(reducer, initializerArg)
}

export default useReducer