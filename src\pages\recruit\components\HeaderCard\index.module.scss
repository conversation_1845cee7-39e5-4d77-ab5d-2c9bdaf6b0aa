
.container {
    display: flex;
    flex-direction: column;
}

.title {
    @include textrow(2);
    font-size: 46px;
    font-weight: bold;
    line-height: 64px;
    color: $text85;
    
}

.salary {
    font-size: 38px;
    font-weight: bold;
    line-height: 54px;
    color: #0092ff;
    margin-top: 16px;
}

.summary {
    display: flex;
    flex-direction: row;
    margin-top: 16px;
    flex-wrap: wrap;
}

.summaryIc {
    width: 28px;
    height: 28px;
    margin-right: 8px;
    display: flex;
    flex-direction: row;
    align-items: center;
}

.summaryText {
    margin-right: 32px;
}

.hidden {
    display: none !important;
}

.location,.settlement,.exp,.edu {
    display: flex;
    flex-direction: row;
    align-items: center;
    margin-bottom: 16px;
}

.card {
    padding-bottom: 0;
}