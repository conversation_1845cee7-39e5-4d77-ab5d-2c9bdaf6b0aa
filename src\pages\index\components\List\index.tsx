/*
 * @Date: 2024-11-23 10:06:55
 * @Description: 列表
 */

import { View as V, Text as T, Image as Img } from '@tarojs/components'
import { MutableRefObject, useState } from 'react'
import Skeleton from '../Skeleton'
import JobCard from '@/components/JobCard'
import Loading from '@/components/Loading'
import s from './index.module.scss'
import GenerateResumePublish from '@/pages/recruit/components/GenerateResumePublish'
import { freeRegistrationFor<PERSON>uaishou, generateHopeOcc } from './utils'

// 定义组件的props类型
interface ListProps {
  onClickCard: (() => void) | MutableRefObject<(info: any, locationId: string) => void>;
  location: any;
  homeNearbyLocation: any;
  loadingStatus: string;
  list: any[];
  skeleton: boolean;
  listType: number;
  noDataText?: string;
  topHeight?: string | null;
  refreshList?: (newList: any[]) => void;
  extraData: Record<string, any>;
  oneClickResumePopupExpose: { popupPage: '1' | '2' | '3' | '4' | '5' } // one_click_resume_popup_expose 1-抖音小程序留资1一配置招工信息ID;2-抖音小程序留资2一配置工种 ID:3-招工详情;4-推荐列表一键投递;5-搜索列表一键投递;
}

export default ({ onClickCard, location, homeNearbyLocation, loadingStatus, list, skeleton, listType, extraData, noDataText = '暂无相关职位', topHeight = null, refreshList = () => { }, oneClickResumePopupExpose }: ListProps) => {
  const [publishShow, $publishShow] = useState<boolean>(false)
  const [showResumeDialogItem, $showResumeDialogItem] = useState<any>(null)
  const [showResumeDialogLocationId, $showResumeDialogLocationId] = useState<string | null>(null)
  if (skeleton) {
    return <Skeleton />
  }

  const nodata = list.length === 0 && loadingStatus === 'done' ? (
    <V className={s.noData} style={{ height: topHeight ? `calc(100vh - ${topHeight})` : 'initial' }}>
      <Img className={s.noDataImg} src='https://cdn.yupaowang.com/yupao_mini/alipay/home_no_data.png' />
      <T className={s.noDataText}>{noDataText}</T>
    </V>
  ) : null

  // 首页免费报名投递逻辑
  const freeRegistration = (item: any, extData: Record<string, any>) => {
    freeRegistrationForKuaishou({
      item,
      extData: {
        buriedData: {
          ...(extraData?.buriedDataInfo || {}),
          ...item.buriedData,
          location_id: extData?.locationId,
          recommend_reason: item?.recommendedReason || '',
        },
        sortTime: item.sortTime,
        occIds: extraData?.occIds,
        jobId: item.jobId,
      },
      showResumeDialog: () => {
        $showResumeDialogItem(item)
        $publishShow(true)
        $showResumeDialogLocationId(extData?.locationId || null)
      },
      applySuccessCallback: () => {
        // 直接更新列表中的投递状态，不刷新职位详情
        const jobIdMap = new Map(list.map((it, index) => [it.jobId, index]))
        const targetIndex = jobIdMap.get(item.jobId)
        if (typeof targetIndex === 'number') {
          refreshList(list.map((it, i) => (i === targetIndex ? {
            ...it,
            isIm: true,
          } : it)))
        }
      },
    })
  }

  return (
    <V>
      <V>
        {list.map((item, index) => {
          return <JobCard onClickCard={onClickCard} location={location} info={item} key={`${index}-${item.jobId}`} index={index} freeRegistration={() => freeRegistration(item, { locationId: String(index + 1) })}
            extraData={{
              search_result: extraData?.search_result,
            }}
          />
        })}
      </V>
      {/* 定位失败 */}
      {listType === 3 && homeNearbyLocation.success === false ? (
        <V className={s.noData}>
          <Img className={s.noDataImg} src='https://cdn.yupaowang.com/yupao_mini/alipay/home_no_data.png' />
          <T className={s.noDataText}>暂无符合职位，更换筛选范围试试</T>
        </V>
      ) : nodata}
      {/* 加载状态 */}
      <V className={s.loading}>
        {loadingStatus === 'more' ? (
          <>
            <Loading />
            <T className={s.loadingText}>加载中...</T>
          </>
        ) : null}
        {/* 无内容状态 */}
        {loadingStatus === 'done' && list.length > 0 ? (
          <T className={s.loadingTextDone}>- 没有更多内容了 -</T>
        ) : null}
      </V>
      <GenerateResumePublish
        sourceId={oneClickResumePopupExpose.popupPage}
        visible={publishShow}
        hopeAreas={showResumeDialogItem?.urbanAreas?.cityId ? [showResumeDialogItem.urbanAreas.cityId] : []}
        occupations={generateHopeOcc(showResumeDialogItem?.occV2) || []}
        onClose={() => $publishShow(false)}
        onConfirm={() => freeRegistration(showResumeDialogItem, { locationId: showResumeDialogLocationId })}
      />
    </V>
  )
}
