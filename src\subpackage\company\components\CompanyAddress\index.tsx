import { Text as T, View as V } from '@tarojs/components'
import IconFont from '@/components/IconFont'
import styles from './index.module.scss'

type IProps = {
  /** 地址 */
  address: string
  /** 经纬度 */
  location: string
}
/**
 * 公司地址
*/
export default (props: IProps) => {
  const { address, location } = props

  /**
   * 打开地图
  */
  const openLocation = async () => {
    const [longitude, latitude] = location.split(',')
    const lng = Number(longitude)
    const lat = Number(latitude)

    // 验证经纬度是否有效
    if (Number.isNaN(lng) || Number.isNaN(lat) || lng < -180 || lng > 180 || lat < -90 || lat > 90) {
      console.error('Invalid coordinates:', { longitude: lng, latitude: lat })
      $.msg('位置信息无效')
      return
    }

    // 快手小程序使用原生 ks.openLocation API
    if (process.env.TARO_ENV === 'kwai') {
      // 先检查和请求位置权限
      try {
        await new Promise((resolve, reject) => {
          ks.getSetting({
            success: (res) => {
              if (res.authSetting && res.authSetting['scope.userLocation'] === false) {
                // 用户之前拒绝了权限，需要引导用户去设置页面开启
                ks.showModal({
                  title: '位置权限',
                  content: '需要位置权限才能打开地图，请在设置中开启位置权限',
                  confirmText: '去设置',
                  success: (modalRes) => {
                    if (modalRes.confirm) {
                      ks.openSetting({
                        success: () => resolve(true),
                        fail: () => reject(new Error('打开设置失败')),
                      })
                    } else {
                      reject(new Error('用户取消'))
                    }
                  },
                })
              } else {
                resolve(true)
              }
            },
            fail: () => reject(new Error('获取设置失败')),
          })
        })

        // 权限检查通过，调用 openLocation
        ks.openLocation({
          longitude: lng, // 确保是有效的数字类型
          latitude: lat, // 确保是有效的数字类型
          // 移除 scale 参数，因为快手小程序可能不支持或格式不同
          name: address || '位置', // 确保有默认值
          address: address || '详细地址', // 确保有默认值
          success: res => {
            console.log('openLocation success:', res)
          },
          fail: res => {
            console.error('openLocation fail:', res)
            if (res.errMsg && res.errMsg.includes('no permission')) {
              $.msg('没有位置权限，请在设置中开启')
            } else {
              $.msg('打开地图失败')
            }
          },
        })
      } catch (error) {
        console.error('Permission check failed:', error)
        $.msg('权限检查失败')
      }
    } else {
      // 其他平台使用 taro 的 openLocation API
      $.taro.openLocation({
        longitude: +longitude,
        latitude: +latitude,
        name: address,
        address,
        scale: 19,
        success: res => {
          console.log(res)
        },
        fail: res => {
          console.log(res)
        },
      })
    }
  }

  return (
    <V className={styles.wrap}>
      <T className={styles.title}>公司地址</T>
      <V className={styles.inner}>
        <T className={styles.leftInner}> {address} </T>
        {
          location && (
            <V className={styles.nav} onClick={() => openLocation()}>
              <IconFont type='yp-daohang' size={24} color='#fff' />
              <V className={styles.navText}>导航</V>
            </V>
          )
        }
      </V>
    </V>
  )
}
