import { View } from '@tarojs/components'
import style from './index.module.scss'
import { useSelector } from '@/core/store'
import LastItemView from '../LastItemView'

type ITwoItemViewProps = {
  /** 一级职位对象 */
  oneItem?: any
  /** 一级职位对应的二级职位 */
  list?: Array<any>
  /** 一级职位ID */
  oneocc?: string
  /** 选中的行业 */
  indSlted?: string
  /** 选中的二级职位 */
  occSlted?: any
  /** 点击方法 */
  onClick?: (id: string) => void
  [key: string]: any
}

const TwoItemView = (props: ITwoItemViewProps) => {
  const { oneItem = {}, list = [], occSlted = {}, indSlted = '', oneocc = '', onClick } = props

  // 职位对象(格式:key:行业_职位ID,value:对象)
  const industryOccMap = useSelector((state) => state.classify.industryOccMap)
  if (oneItem.extType !== 3) {
    const { length: len } = list || []
    if (len > 1) {
      return (
        <View className={style.twoOccView}>
          {
            list.map((twoocc) => {
              const ky = `${indSlted}_${twoocc}`
              return <LastItemView slted={occSlted[ky]} item={industryOccMap[ky] || {}} occId={ky} onClick={onClick} key={twoocc} />
            })
          }
        </View>
      )
    }
    if (len === 0) {
      const ky = `${indSlted}_${oneocc}`
      return (
        <View className={style.twoOccViewFirst}>
          <LastItemView slted={occSlted[ky]} item={industryOccMap[ky] || {}} occId={ky} onClick={onClick} />
        </View>
      )
    }
    const [twoId] = list
    const ky = `${indSlted}_${twoId}`
    return (
      <View className={style.twoOccViewFirst} >
        <LastItemView slted={occSlted[ky]} item={industryOccMap[ky] || {}} occId={ky} onClick={onClick} />
      </View>
    )
  }
  return null
}

export default TwoItemView
