
.hidden {
  opacity: 0 !important;
  visibility: hidden !important;
  pointer-events: none !important;
}

.body {
  padding: 0 32px;
  position: relative;
}

.title {
  font-size: 54px;
  font-weight: bold;
  line-height: 76px;
}

.weInfo {
  line-height: 42px;
  padding-bottom: 20px;
}

.desc {
  font-size: 26px;
  line-height: 36px;
  color: rgba(0, 0, 0, 0.25);
  display: flex;
}

.inputBox {
  position: relative;
  display: flex;
  align-items: center;
  margin-bottom: 30px;
  @include bottom-line-b();
}


.inputCont {
  display: flex;
  align-items: center;
  flex: 1;
  .input {
    font-size: 30px;
    width: 100%;
    color: rgba(0, 0, 0, 0.85);
    display: block;
    height: 112px;
  }
}

.inputRight {
  display: flex;
  align-items: center;
  .inputClear{
    opacity: 0.7;
    padding: 6px 24px;
    display: inline-flex;
  }
  .info-num {
    display: flex;
    align-items: center;
  }
  .num {
    color: $primary-color;
  }
  .num-err {
    color: $error-color;
  }
  .num-gray {
    color: rgba(0, 0, 0, 0.45);
  }
}


.placeholder {
  word-break: break-word;
  white-space: pre-wrap;
  font-size: 30px;
  color: rgba(0, 0, 0, 0.45);
}

.footer {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  padding: 24px 32px;
  background-color: #FFF;
  @include safe-area(24px);
  border-top: 1px solid #e9edf3;
}

.disabled {
  color: rgba(0, 0, 0, 0.25) !important;
  pointer-events: none !important;
}

.btn {
  color: #FFF;
  font-size: 34px;
  height: 80px;
  line-height: 80px;
  display: flex;
  font-weight: bold;
  text-align: center;
  align-items: center;
  justify-content: center;
  border-radius: 12px;
  background: #0092ff;

  &.btn-mini{
    height: 72px;
    line-height: 72px;
    display: inline-flex;
    align-items: center;
    padding: 0 30px;
    margin-left: 24px;
  }
}
