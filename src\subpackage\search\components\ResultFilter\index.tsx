import React from 'react'
import { View, Text } from '@tarojs/components'
import classNames from 'classnames'
import s from './index.module.scss'
import { IClassifyDataRp } from '@/core/utils/index.d'
import Click from '@/components/Click'
import { useSelector } from '@/core/store'

interface ResultFilterProps {
  city: {
    areaId: number,
    name: string,
    latitude: string | null,
    longitude: string | null,
  }; // 城市值
  jobs: Array<IClassifyDataRp>; // 职位值
  onCitySelect: (city: ILocation.TAreaData) => void; // 城市选择回调
  onJobSelect: (jobs: Array<IClassifyDataRp>) => void; // 职位选择回调
}

const ResultFilter: React.FC<ResultFilterProps> = ({
  city,
  jobs,
  onCitySelect,
  onJobSelect,
}) => {
  const findJobSearchOccCnt = useSelector(state => state.classify.classifyConfig.findJobSearchOccCnt)
  // 城市选择逻辑
  const onChooseCity = () => {
    const params = city?.areaId ? {
      areas: [city?.areaId],
      showLocation: true,
    } : {}
    // 调用 $.openAddress 方法，使用回调更新城市
    $.openAddress(params, {
      source_id: '7',
      source: '招工搜索结果页',
      button_name: city.name || '',
    }, (areaDataS: ILocation.TAreaData[]) => {
      if (areaDataS && areaDataS.length > 0) {
        onCitySelect(areaDataS[0]) // 调用父组件回调
      }
    })
  }

  // 职位选择逻辑
  const onChooseJobs = () => {
    const homeClassify = jobs.map((job) => ({
      id: job.id,
      industries: job.industries,
    }))

    $.openClassify({ value: homeClassify, title: '选择期望职位（可多选）', isClear: true, maxSelectNum: findJobSearchOccCnt, source_id: 5 }, (data: Array<IClassifyDataRp>) => {
      if (data) {
        onJobSelect(data) // 调用父组件回调
      }
    })
  }

  // 动态文案展示
  const jobText = () => {
    if (jobs.length === 0) return '选择职位'
    if (jobs.length === 1) return jobs[0].name || '工种'
    return `职位·${jobs.length}`
  }

  return (
    <View className={s.resultFilter}>
      <V className={s.filterBox}>
        <Click className={classNames(city?.name ? s.filterItemActive : s.filterItem)} onClick={onChooseCity}>
          <Text numberOfLines={1}>{city?.name || '选择城市'}</Text>
        </Click>
      </V>
      <V className={s.filterBox}>
        <Click className={classNames(jobs?.length ? s.filterItemActive : s.filterItem)} onClick={onChooseJobs}>
          <Text numberOfLines={1}>{jobText()}</Text>
        </Click>
      </V>
    </View>
  )
}

export default ResultFilter
