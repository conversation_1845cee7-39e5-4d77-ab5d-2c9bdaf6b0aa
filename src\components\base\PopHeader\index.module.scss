/** 弹出层的标题栏 */
.popBox {
  display: flex;
  align-items: center;
  height: 100px;
  padding: 0 16px;
  font-size: 30px;
  background-color: #fff;
  &.line {
    border-bottom: 1px solid #eff1f6;
  }
}

.popBtn {
  @include btn();
  display: block;
  width: auto;
  font-size: 30px;
  background: transparent;
  box-shadow: none;
}

.popCancel {
  padding: 16px;
  font-weight: normal;
  color: rgba(0, 0, 0, 0.45);
}

.popTitle {
  flex: 1;
  padding: 16px;
  font-size: 34px;
  font-weight: bold;
  text-align: center;
  color: rgba(0, 0, 0, 0.85);
}

.popConfirm {
  padding: 16px;
  font-weight: bold;
  color: $primary-color;
}
