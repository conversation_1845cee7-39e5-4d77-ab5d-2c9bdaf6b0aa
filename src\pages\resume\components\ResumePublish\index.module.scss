

.page {
  height: 100%;
}

.content {
  padding: 0 32rpx;
}

.item {
  padding: 32rpx 0;
}

.pop_item {
  padding: 29rpx 0;
}

.title {
  color: rgba(0, 0, 0, 0.85);
  font-weight: bold;
  font-size: 50rpx;
  line-height: 70rpx;
}

.pop_title {
  font-size: 38rpx;
}


.addressTips {
  width: fit-content;
  height: 64px;
  display: inline-flex;
  place-content: center;
  place-items: center;
  gap: 12rpx;
  flex-shrink: 0;
  border-radius: 16rpx;
  background: rgba(224, 243, 255, 1);
  padding: 0 24rpx;
  margin-bottom: 32rpx;
}

.gpsImg {
  width: 36rpx;
  height: 36rpx;
}

.gpsText {
  font-size: 26rpx;
  color: rgba(0, 146, 255, 1);
}

.closeImg {
  width: 48rpx;
  height: 48rpx;
  position: absolute;
  right: 32rpx;
  top: 32rpx;
}


.footer {
  padding: 24rpx 32rpx;
  @include safe-area(24px);
}

.footer_ios {
  padding: 24rpx 32rpx 48rpx 32rpx;
}

.pop_btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 96rpx;
  border-radius: 16rpx;
  background: rgba(0, 146, 255, 1);
  color: rgba(255, 255, 255, 1);
}

.pop_btnTxt {
  font-weight: bold;
  font-size: 34rpx;
}
