import { View, Image } from '@tarojs/components'
import { CSSProperties, useMemo } from 'react'
import styles from './index.module.scss'

type IProps = {
  classNames?: string
  style?: CSSProperties
}

export default (props: IProps) => {
  const result = useMemo(() => $.taro.getMenuButtonBoundingClientRect(), [])
  const onBack = () => {
    const pages = getCurrentPages()
    if (pages.length === 1) {
      $.router.reLaunch('/pages/index/index')
      return
    }
    $.taro.navigateBack()
  }
  return (
    <View
      className={`${styles.header} ${props.classNames}`}
      style={props.style}
    >
      <Image src="https://cdn.yupaowang.com/yupao_common/507c7d68.png" mode='aspectFill' className={styles.backIcon} style={{ top: `${result.top + result.height / 2}px`, transform: 'translateY(-50%)' }} onClick={onBack} />
    </View>
  )
}
