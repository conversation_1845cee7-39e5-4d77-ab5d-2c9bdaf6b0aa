/*
 * @Author: ji<PERSON><PERSON>
 * @LastEditors: jianglong
 */
declare interface IExtra {
  /** 是否展示加载动画 */
  showLoading?: boolean
  /** 不需要拦截处理的状态码，多个传入数组，完全不需要拦截处理请传入：true，数组示例如：['to_auth', 'company_status'] */
  hideErrCodes?: Array<any> | true
  /** 这个code码是处理通用弹框的逻辑，需要拦截处理的code状态码，多个传入数组，数组示例如：['10000', '10001'] */
  showErrCodes?: Array<string> | undefined
  /** 通用弹框标识,需要拦截处理的弹框标识, 传入数组，数组示例如：['renzhengchakanhaohuo', 'SXCKXX1'] */
  showErrDialog?: Array<string> | undefined
  /** 是否隐藏默认报错toast, 默认false */
  hideMsg?: boolean
  /** toast提示 是否显示透明蒙层，防止触摸穿透 默认false */
  msgMask?: boolean
  /** 是否去掉默认携带的wechat_token请求参数 */
  isNoToken?: boolean
  /** 是否开启mock数据 */
  mock?: boolean
  /**
   * 请求的类型，默认不传 代表redux请求，会发送 Action，也存入redux store
   * normal 代表普通请求，不发送 Action，也不存入redux store
   * redux 代表redux请求，会发送 Action，也存入redux store
   */
  type?: 'normal' | 'redux'
  /**
   * 请求头 content-type，默认是 'application/json'
   */
  contentType?: 'application/json' | 'multipart/form-data' | 'application/x-www-form-urlencoded' | 'text/plain' | 'text/html' | 'application/javascript'
  /**
   * 请求 url 后面拼接的 query 参数，比如 POST 请求需要拼接 token 参数
   */
  query?: {
    [key: string]: any
  }
  /**
   * 用户自定义的queryString函数，默认使用JSON.stringify处理，例如 { a: 1, b: 2 } 结果是 a=1&b=2
   */
  queryStringFn?: (input: any[] | object) => string
  /** 扩展字段 */
  [key: string]: any
}

/** 外部使用的接口类型 */
declare type Models = DeepPartial<YModels>

declare type YFetch = {
  [K in keyof YResponseTypes]: (
    /** 请求参数 */
    req?: YModels[K]['Req'],
    /** 请求参数 */
    extra?: IExtra,
    /** 请求头 */
    headers?: {[key: string]: string},
  ) => Promise<[DeepPartial<YResponseTypes[K]>['data'], {
    /** 响应拦截器里边匹配到的通用逻辑标识, 没有匹配到返回 undefined */
    dialogType?: 'java_errcode' | 'showErrCodes' | 'showErrDialog'
    /** 后端返回的弹框标识对象，后端没有返回弹框标识或者dialogType有逻辑标识时，此字段返回空字符串 */
    dialogData: '' | {
      /** 弹框标识 */
      dialogIdentify: string
      /** 弹框内容所要替换的变量 */
      template: { [key: string]: any } | null
    }
    /** 弹框对象这个使用dealDialogApi方法返回的结果，后端没有返回弹框标识或者没有匹配到弹框标识返回 undefined */
    popup?: {[key: string]: any}
  } & DeepPartial<YResponseTypes[K]>]>
}
