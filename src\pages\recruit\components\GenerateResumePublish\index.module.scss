.warp {
  padding: 0 32px;
}

.head {
  padding: 29rpx 0;
}

.title {
  color: rgba(0, 0, 0, 0.85);
  font-weight: bold;
  font-size: 38rpx;
}

.item {
  padding: 40rpx 0;
  border-bottom: 1rpx solid rgba(233, 237, 243, 1);
}

.label {
  margin-bottom: 16rpx;
}

.labelTxt {
  color: rgba(0, 0, 0, 0.65);
  font-size: 30rpx;
}

.content {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.value {
  color: rgba(0, 0, 0, 0.85);
  font-size: 34rpx;
}

.placeholder {
  color: rgba(0, 0, 0, 0.25);
  font-size: 34rpx;
}

.icon {
  margin-left: 32rpx;
}

.footer {
  padding: 24rpx 32rpx;
  @include safe-area(24px);
}

.btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 96rpx;
  border-radius: 16rpx;
}

.btnTxt {
  font-weight: bold;
  font-size: 34rpx;
}

.btnOk {
  background: rgba(0, 146, 255, 1);
  color: rgba(255, 255, 255, 1);
}

.btnNo {
  background: rgba(153, 211, 255, 1);
  color: rgba(255, 255, 255, 0.45);
}
