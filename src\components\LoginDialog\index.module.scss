.container {
    width: 654px;
    display: flex;
    flex-direction: column;
    align-items: center;
    box-sizing: border-box;
    background-color: white;
    padding: 64px 32px 48px 32px;
    border-radius: 24px;
    z-index: 10002 !important;
}

.logo {
    width: 144px;
    height: 144px;
    object-fit: contain;
}

.loginText {
    color:  rgba(0, 0, 0, 0.85);
    font-weight: 500;
    font-size: var(--字号25, 50px);
    line-height: 70px;
    margin-top: 40px;
}

.loginBtn {
    font-size: 34px;
    font-weight: bold;
    color: #fff;
    line-height: 48px;
    width: 590px;
    height: 96px;
    background-color: rgb(0, 146, 255);
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
    border-radius: 16px;
    margin-top: 40px;
}
.loginBtnHover {
    opacity: 0.75;
}

.verifyCodeLogin {
    font-size: 32px;
    font-weight: bold;
    color: rgb(0, 146,255);
    line-height: 44px;
    margin-top: 40px;
}

.protocol {
    width: 590px;
    font-size: 26px;
    font-weight: 400;
    color: rgba(0,0,0,.45);
    line-height: 150%;
    display: flex;
    flex-direction: row;
    margin-top: 40px;
}

.checkedIcon {
    width: 32px;
    height: 32px;
    margin-right: 8px;
}

.primary {
    color: rgba(0,146,255,1);
}

.protocolContainer {
    z-index: 10004 !important;
    height: fit-content;
    min-height: 0px;
    padding: 48px 32px;
    border-top-left-radius: 16px !important;
    border-top-right-radius: 16px !important;
    width: 750px;
    box-sizing: border-box;
}
.pHeader {
    width: 100%;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    font-size: 38px;
    font-weight: bold;
    line-height: 54px;
    color: rgba(0,0,0,.85);
}

.pTips {
    line-height: 42px;
    font-size: 30px;
    font-weight: 400;
    color:rgba(0,0,0,.65);
}

.certainBtn {
    @extend .loginBtn;
    width: 686px;
    margin-top:48px;
}