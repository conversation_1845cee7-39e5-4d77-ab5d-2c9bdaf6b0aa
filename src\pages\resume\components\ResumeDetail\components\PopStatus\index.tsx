import cn from 'classnames'
import type { StandardProps } from '@tarojs/components'
import s from './index.module.scss'
import IconFont from '@/components/IconFont'
import Popup from '@/components/Popup'
import { getState, useSelector } from '@/core/store'
import { fetchResumeExist } from '@/utils/helper/resume'

type Props = StandardProps & {
  visible: boolean
  onClose: () => void
  onChange?: (data) => void
}

/** 通用单元格组件 */
export default function Index(props: Props) {
  const { visible } = props
  const resumeExist = useSelector(state => state.resume.resumeExist)
  // const [selected, setSelected] = useState(resumeExist.workStatus || 1) // 默认选中第一个

  const statusList = [
    { id: 1, name: '离职-随时到岗', isTag: true },
    { id: 4, name: '在职-月内到岗', isTag: true },
    { id: 5, name: '在职-考虑机会' },
    { id: 2, name: '在职-暂不考虑' },
  ]

  const onCapsule = async (item: typeof statusList[0]) => {
    const { resumeUuid } = getState().storage.myResumeDetails
    if (resumeExist.workStatus == item.id) {
      return
    }
    $.showLoading('修改中...')
    const [, res] = await $.request['POST/resume/v3/base/changeWorkStatus']({
      uuid: resumeUuid,
      workStatus: item.id,
    }, {})
    if (res && !res.error) {
      fetchResumeExist(true)
      $.hideLoading()
      $.msg('求职状态已修改')
      props.onClose()
      props.onChange && await props.onChange({
        workStatus: item.id,
        workStatusName: item.name,
      })
      return
    }
    $.hideLoading()
    $.msg(res.message || '求职状态修改失败')
  }
  return (
    <Popup
      visible={visible}
      catchMove
      disableScroll
      position="bottom"
      onClose={props.onClose}
    >
      <V className={s['pop-cont']} onClick={(e) => e.stopPropagation()}>
        <V className={s['pop-head']}>
          <V className={s['pop-title']}>
            <V className={s['pop-tit-text']}>求职状态</V>
            <IconFont onClose={props.onClose} type="yp-guanbi" size={44} color="rgba(0, 0, 0, 0.25)" />
          </V>
          {resumeExist.hideStatus != 1
            ? <V className={s['pop-desc']}>求职状态会影响你被推荐的频率，以及老板的决策</V>
            : <V className={s['state-status']}>
              <IconFont type="yp-safety" size={32} />
              <V className={s['status-text']}>简历已对老板隐藏，老板无法查看你的求职状态</V>
            </V>
          }
        </V>

        <V className={s.list}>
          {statusList.map((item) => (
            <V className={s.item}
              key={item.id}
              onClick={() => onCapsule(item)}
            >
              <V className={cn(s['item-text'], item.id == resumeExist.workStatus && s['text-checked'])}>{item.name}</V>
              {item.isTag && <V className={s['item-tag']}>优先推荐</V>}
              <V className={cn(s['item-icon'], item.id != resumeExist.workStatus && s.hidden)}>
                <IconFont type="yp-duih" size={40} color="#0092FF" />
              </V>
            </V>
          ))}
        </V>
      </V>
    </Popup>
  )
}
