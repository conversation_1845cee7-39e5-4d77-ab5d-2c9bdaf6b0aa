page, .container {
    width: 100%;
    height: 100%;
    background-color: #fff;
}

.content {
    padding: 32px 48px;
    display: block;
    box-sizing: border-box;
}

.titleText {
    font-size: 54px;
    font-weight: bold;
    line-height: 76px;
    color: rgba(0,0,0,.85);
    display: block;
}

.subTitleText {
    display: block;
    font-size: 26px;
    font-weight: 400;
    color: rgba(0,0,0,.65);
    line-height: 160%;
    margin-top: 16px;
}

.input {
    border-radius: 16px;
    background:  rgba(245, 247, 252, 1);
    padding: 0 32px;
    height: 96px;
    width: 100%;
    width: 654px;
    display: flex;
    flex-direction: row;
    align-items: center;
    box-sizing: border-box;
}

.input:nth-of-type(1) {
    margin-top: 80px;
}

.input:nth-of-type(2) {
    margin-top: 48px;
}

.inputInner {
    background:  rgba(245, 247, 252, 1);
    padding: 0;
    display: flex;
    flex: 1;
    font-size: 28px;
    font-weight: 400;
    color: $text85;
}
.inputInner1 {
    background:  rgba(245, 247, 252, 1);
    padding: 0;
    width: 590px;
    font-size: 28px;
    font-weight: 400;
    color: $text85;
}

.protocol {
    width: 590px;
    font-size: 26px;
    font-weight: 400;
    color: rgba(0,0,0,.45);
    line-height: 150%;
    display: flex;
    flex-direction: row;
    margin-top: 48px;
}

.checkedIcon {
    width: 32px;
    height: 32px;
    margin-right: 8px;
}

.primary {
    color: rgba(0,146,255,1);
}

.loginBtn {
    font-size: 34px;
    font-weight: bold;
    color: #fff;
    line-height: 48px;
    height: 96px;
    background-color: rgb(0, 146, 255);
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
    border-radius: 16px;
    width: 654px;
    margin-top:40px;
    overflow: hidden;
}

.disabled {
    background-color: rgb(153, 211, 255);
    font-size: 34px;
    font-weight: bold;
    color: rgba(255,255,255,.45);
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
}

.placeholder {
    font-size: 30px;
    font-weight: 400;
    line-height: 32px;
    color: rgba(0,0,0,.45);
    display: flex;
    flex: 1;
}
