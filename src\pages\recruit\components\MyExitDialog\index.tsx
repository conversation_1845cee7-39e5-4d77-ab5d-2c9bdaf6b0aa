import { Button, Image, View } from '@tarojs/components'
import ModalBody from '@/components/Modal/Body'
import styles from './index.module.scss'

export default ({ visible, onClose }) => {
  return <ModalBody visible={visible} onMaskClick={onClose}>
    <View className={styles.container}>
      <Image src="https://cdn.yupaowang.com/yupao_mini/detail_close_ic_1.png" className={styles.closeIc} onClick={onClose}></Image>
      <Image src="https://cdn.yupaowang.com/yupao_mini/detail_success_ic_1.png" className={styles.successIc}></Image>
      <View className={styles.content}>投递成功</View>
      <Button className={styles.btn} onClick={() => $.taro.exitMiniProgram()}>返回支付宝就业</Button>
    </View>
  </ModalBody>
}
