/** 弹出层的标题栏 */
.popBox {
  display: flex;
  align-items: center;
  padding: 35rpx 32rpx;
  font-size: 30rpx;
  background-color: #fff;
}

.line {
  border-bottom: 2rpx solid #eff1f6;
}

.popBtn {
  width: auto;
  font-size: 30rpx;
  line-height: 42rpx;
}

.popCancel {
  color: rgba(0, 0, 0, 0.65);
}

.popTitle {
  flex: 1;
  margin: 0 80rpx;
  font-size: 34rpx;
  font-weight: bold;
  text-align: center;
  color: rgba(0, 0, 0, 0.85);

}

.popConfirm {
  color: rgba(23, 119, 255, 1);
}