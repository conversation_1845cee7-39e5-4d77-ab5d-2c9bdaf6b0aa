/*
 * @Date: 2023-12-26 15:55:39
 * @Description: auto loading
 */

import { useState, useCallback } from 'react'

export default function useLoading<T extends(...args: any[]) => Promise<any>>(fn: T): [boolean, T] {
  const [loading, setLoading] = useState(false)

  // 使用useCallback缓存call函数，避免每次重新定义
  const call = useCallback(async (...args: Parameters<T>) => {
    if (loading) return
    setLoading(true)
    try {
      await fn(...args)
    } finally {
      setLoading(false)
    }
  }, [fn, loading]) // 添加`fn`和`loading`为依赖项

  return [loading, call as T]
}
