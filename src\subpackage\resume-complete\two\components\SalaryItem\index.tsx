import { PickerView, PickerViewColumn } from '@tarojs/components'
import { useEffect, useState } from 'react'
import style from './index.module.scss'

type ISalaryItemProps = {
  /** 薪资数据 */
  salary?: any,
  /** 当前薪资范围 */
  value?: any,
  change?: (value: any) => void
  start?: () => void
  end?: () => void
}
const SalaryItem = (props: ISalaryItemProps) => {
  const { salary = [], value = [], change, start, end } = props

  const [rSalary, setRSalary] = useState<Array<any>>(['面议'])
  const [selectIndex, setSelectIndex] = useState<Array<any>>([0, 0])

  useEffect(() => {
    handlerSalary(value, salary)
  }, [value, salary])

  const handlerSalary = async (pvalue, psalary) => {
    const rSalary: Array<any> = []
    const dSalary = psalary || salary
    let startIndex = 0
    let endIndex = 0
    if ($.isArrayVal(pvalue)) {
      startIndex = pvalue[0] ? dSalary.findIndex((item) => item === pvalue[0]) : 0
      startIndex = startIndex === -1 ? 0 : startIndex

      if (startIndex != 0) { // rSalary只要后五项, 右侧数据的处理
        rSalary.push(...dSalary.slice(startIndex + 1, startIndex + 1 + 5))
        endIndex = pvalue[1] ? rSalary.findIndex((item) => item === pvalue[1]) : 0
        endIndex = endIndex === -1 ? 0 : endIndex
      }
    }
    if (startIndex == 0) {
      rSalary.push('面议')
    }
    setRSalary(rSalary)
    // 设置右边的数据
    setSelectIndex([startIndex, endIndex])
    // 选中的索引, 为了解决picker-view的选中问题所以延迟加载
    await $.wait(100)
    setSelectIndex([startIndex, endIndex])
  }

  /** change事件 */
  const onChange = (e) => {
    const { value } = e.detail
    const startValue = salary[value[0]] || 0
    let endValue = rSalary[value[1]] || 0
    // 如果滑动的是左边的模块
    if (value[0] != selectIndex[0]) {
      // 处理右侧数据回到第一个数据
      endValue = value[0] == 0 ? '面议' : salary[value[0] + 1]
      value[1] = 0
    }
    change && change({ detail: { value: [startValue, endValue] } })
  }

  const onStart = () => {
    start && start()
  }

  const onEnd = () => {
    end && end()
  }
  return (
    <PickerView
      value={selectIndex}
      onChange={onChange}
      onPickStart={onStart}
      onPickEnd={onEnd}
      className={style.pickerView}
      indicatorClass={style.indicatorClass}
    >
      <PickerViewColumn>
        {
          salary.map((item, index) => {
            return index != salary.length - 1 ? (
              <V className={style.pickerItem} key={item}><T>{item}</T></V>
            ) : null
          })
        }

      </PickerViewColumn>
      <PickerViewColumn>
        {
          rSalary.map((item) => {
            return <V className={style.pickerItem} key={item} ><T>{item == '面议' ? '' : item}</T></V>
          })
        }
      </PickerViewColumn>
    </PickerView>

  )
}

export default SalaryItem
