.employerInfo {
    width: 100%;
    box-sizing: border-box;
    display: flex;
    flex-direction: row;
    align-items: center;
    position: relative;
}

.avatar {
    width: 96px;
    height: 96px;
    border-radius: 50%;
}

.avatarBox {
    width: 96px;
    height: 96px;
    position: relative;
}

.active {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    position: absolute;
    bottom: 0;
    right: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 4px solid #fff;
    background-color: rgb(6, 181, 120);
}

.textRow {
    margin-left: 24px;
    height: 96px;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.nameRow {
    display: flex;
    flex-direction: row;
    align-items: center;
}

.name {
    font-size: 30px;
    font-weight: bold;
    line-height: 42px;
    color: $text85;
}

.subInfo {
    font-size: 22px;
    font-weight: 400;
    line-height: 30px;
    color: $text45;
    margin-left: 16px;
}

.companyName {
    margin-top: 8px;
    font-size: 26px;
    font-weight: 400;
    color: $text65;
    line-height: 36px;
    @include textrow(1);
}

.hidden {
    display: none !important;
}

.btn {
    position: absolute;
    top: 50%;
    right: 0px;
    transform: translateY(-50%);
    padding: 15px 32px;
    border-radius: 36px;
    background-color: rgb(255, 137, 4);
    color: #fff;
    z-index: 1;
}

.btnB{
    position: absolute;
    top: 50%;
    right: 0px;
    transform: translateY(-50%);
    padding: 15px 32px;
    border-radius: 36px;
    background-color: rgb(233, 237, 243);
    color: rgba(0, 0, 0,0.25);
    z-index: 1;
}