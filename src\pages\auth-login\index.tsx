import { View, Text, Input, Button } from '@tarojs/components'

import { useMemo, useState } from 'react'
// import { useUnload } from '@tarojs/taro'
import styles from './index.module.scss'
import IconFont from '@/components/IconFont'
import VerifyCodeButton from '@/components/VerifyCodeButton'
import Page from '@/components/Page'
import LoginProtocol from '@/components/LoginProtocol'

import { afterLogin, goToAgreement } from '@/utils/login'
import Click from '@/components/Click'

definePageConfig({
  navigationBarBackgroundColor: '#fff',
})

export default () => {
  const [checked, $checked] = useState<boolean>(false)

  const [pVisible, $pVisible] = useState<boolean>(false)

  const [tel, $tel] = useState<string|undefined>(undefined)

  const [verifyToken, $verifyToken] = useState<string>('')

  const [verifyCode, $verifyCode] = useState<string| undefined>(undefined)

  // const triggerEvent = useRef(false)

  const onTelChange = (event) => {
    const prev = tel

    const newTel = event.detail.value
    $tel(newTel || undefined)
    setTimeout(() => {
      if (newTel && (String(newTel).length > 11 || !/^\d+$/.test(String(newTel)))) {
        $tel(prev || undefined)
      }
    })
  }

  const onVerifyCodeChange = (event) => {
    const prev = verifyCode
    const newCode = event.detail.value
    $verifyCode(newCode)
    setTimeout(() => {
      if (newCode && (String(newCode).length > 4 || !/^\d+$/.test(String(newCode)))) {
        $verifyCode(prev || undefined)
      }
    })
  }

  /** 协议勾选样式 */
  const iconColor = useMemo(() => {
    if (checked) {
      return 'rgb(0, 146, 255)'
    }
    return 'rgba(0,0,0,.25)'
  }, [checked])

  // const validate = useCallback(() => {
  //   if (!tel || !/^\d{11}$/.test(String(tel))) {
  //     $.msg('请输入11位手机号')
  //     return false
  //   }
  //   if (!verifyToken) {
  //     $.msg('请先获取验证码')
  //     return false
  //   }
  //   if (!verifyCode || !/^\d{4}$/.test(verifyCode)) {
  //     $.msg('请输入4位数字验证码')
  //     return false
  //   }
  //   return true
  // }, [tel, verifyCode, verifyToken])

  const valid = useMemo(() => {
    if (!tel || !/^\d{11}$/.test(String(tel))) {
      // $.msg('请输入11位手机号')
      return false
    }
    if (!verifyToken) {
      // $.msg('请先获取验证码')
      return false
    }
    return verifyCode && /^\d{4}$/.test(verifyCode)
  }, [tel, verifyCode, verifyToken])

  const fakerLogin = (certainProtocol = false) => () => {
    if (!valid) { return }
    if (!checked && !certainProtocol) {
      $pVisible(true)
      return
    }
    // if (pVisible) {
    //   $pVisible(false)
    //   $checked(true)
    // }
    $.showLoading('登录中...')
    $.request['POST/account/v1/login/codeLogin']({
      tel,
      code: verifyCode,
      verifyToken,
    }, { hideMsg: true }).then(([data, res]) => {
      $.hideLoading()
      if (data && data.token) {
        afterLogin(data.token)
        $.msg('登录成功')
        if ($.router.query.back && $.router.getCurrentPages().length > 1) {
          /** 需要返回时有上一页返回上一页，没有上一页返回首页 */
          if ($.router.event) {
            $.router.event(true)
          }
          $.router.back()

          /** 否则返回首页 */
        } else {
          $.router.replace('/pages/index/index')
        }
        return
      }
      $.msg(res.message)
      if ($.router.event) {
        $.router.event(false)
      }
    }).catch(([_, error]) => {
      $.hideLoading()
      const dialogIdentify = $.getObjVal(error, 'data.dialogData.dialogIdentify') || $.getObjVal(error, 'data.data.dialogData.dialogIdentify')
      if (dialogIdentify == 'limitOperate' || (error && error.code == 10110009)) {
        $.confirm({
          title: '温馨提示',
          content: '您的登录/注册环境异常，请稍后再试',
          confirmText: '联系客服',
          cancelText: '知道了',
        }).then(() => {
          $.taro.makePhoneCall({ phoneNumber: '4008381888' })
        })

        // fail && fail()
        return
      }
      error && error.message && $.msg(error.message)
    })
  }

  return (
    <Page backgroundColor='#FFF'><View className={styles.container}>
      <View className={styles.content}>
        <Text className={styles.titleText}>手机号登录/注册</Text>
        <Text className={styles.subTitleText}>首次验证通过即注册鱼泡直聘专业版账号</Text>
        <View className={styles.input} id="borderItem" key="borderItem">
          <Input placeholder='请输入手机号' id="mobile" key="mobile" className={styles.inputInner1} placeholderClass={styles.placeholder} controlled type="number" maxlength={11} onInput={onTelChange} value={tel || undefined}></Input>
        </View>

        <View className={styles.input}>
          <Input placeholder='请输入验证码' placeholderClass={styles.placeholder} controlled type="number" maxlength={4} className={styles.inputInner} onInput={onVerifyCodeChange} value={verifyCode || undefined} />
          <VerifyCodeButton tel={tel} setVerifyToken={$verifyToken} ></VerifyCodeButton>
        </View>
        <View className={styles.protocol} onClick={() => $checked(!checked)}>
          <View><IconFont className={styles.checkedIcon} color={iconColor} type="yp-alipay-checked-round"></IconFont></View>
          <Text>我已阅读并同意
            <Text className={styles.primary} onClick={goToAgreement} data-type="privacy">《隐私政策》</Text>
            <Text className={styles.primary} onClick={goToAgreement} data-type="user">《服务协议》</Text>
          </Text>
        </View>
        <Click className={[styles.loginBtn, valid ? '' : styles.disabled].join(' ')} onClick={fakerLogin()}>登录</Click>
      </View>
      <LoginProtocol buttonType='verifyCodeLogin' visible={pVisible} onClose={() => $pVisible(false)} fakerLogin={fakerLogin(true)}></LoginProtocol>
    </View>
    </Page>)
}
