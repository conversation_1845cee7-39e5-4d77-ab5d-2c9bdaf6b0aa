.item {
  padding: 32rpx 0;
}

.head {
  display: flex;
  align-items: center;
  font-size: 34rpx;
  line-height: 48rpx;
}

.must {
  color: rgba(232, 54, 46, 1);
}

.title {
  color: rgba(0, 0, 0, 0.85);
  font-weight: bold;
}

.desc {
  color: rgba(0, 0, 0, 0.45);
}

.content {
  margin-top: 24rpx;
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: 24rpx;
}

.gridItem {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 15rpx 16rpx;
  color: rgba(0, 0, 0, 0.85);
  font-size: 30rpx;
  line-height: 42rpx;
  background: rgba(245, 247, 252, 1);
  border-radius: 16rpx;
  text-align: center;
}

.txt {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
  text-overflow: ellipsis;
}

.checked {
  box-shadow: inset 0 0 0 2px rgba(0, 146, 255, 1);
  background: rgba(224, 243, 255, 1);
  color: rgba(0, 146, 255, 1);
}
