import { View, Text } from '@tarojs/components'
import ModalBody from '@/components/Modal/Body'
import styles from './index.module.scss'
import IconFont from '../IconFont'
import { goToAgreement } from '@/utils/login'
import Click from '../Click'
import LoginBtn from '../LoginBtn'

export default ({ visible, onClose, fakerLogin, isGetPhoneNumber = false }: IProps) => {
  return (
    <ModalBody visible={visible} contentClassName={styles.protocolContainer} onMaskClick={onClose} position='bottom'>
      <View className={styles.pHeader}>
        <Text className={styles.pHeaderText}>温馨提示</Text>
        <IconFont type="yp-close-small" size={48} onClick={onClose}></IconFont>
      </View>
      <Text className={styles.pTips}>为了更好保障您的合法权益，请您阅读并同意
        <Text className={styles.primary} onClick={goToAgreement} data-type="privacy">《隐私政策》</Text>
        <Text className={styles.primary} onClick={goToAgreement} data-type="user">《服务协议》</Text>
        内容。
      </Text>
      { isGetPhoneNumber ? <LoginBtn openType='getPhoneNumber' className={styles.certainBtn} onGetPhoneNumber={fakerLogin}>同意并登录</LoginBtn> : <Click className={styles.certainBtn} onClick={fakerLogin}>同意并登录</Click>}
    </ModalBody>
  )
}

interface IProps {
  visible: boolean, onClose?: () => void, fakerLogin: (result: any) => any, isGetPhoneNumber?: boolean
}
