
import http from 'http'
import path from 'path'
import fs from 'fs'
import { createRequire } from "module";

// const require = createRequire(import.meta.url);

/** 更新yapi文档 */
async function updateDoc() {
  /** 读取项目根目录yapi.json文件 */
  const pRequire = createRequire(path.join(process.cwd(), 'script/apidoc', 'index.json'))
  const yapiJson = pRequire('./index.json').list

  /** 远程拉取yapiJson数组内部对应url的json文件，并同步赋值给yapiJSONArr */
  const yapiJsonArr = []
  for (let i = 0; i < yapiJson.length; i++) {
    const data = await getJson(yapiJson[i])
    yapiJsonArr.push(...data)
  }
  /** 通过获取到的yapi生成rapper格式的TS提示文件 */
  generateTS(yapiJsonArr)
}

/** 获取数据 */
function getJson (item) {
  return new Promise((resolve, reject) => {
    http.get(item.url, (res) => {
      let data = ''
      res.on('data', (chunk) => {
        data += chunk
      })
      res.on('end', () => {
        try{
          const newDataTemp = JSON.parse(data)
          const newDataFilter = newDataTemp.filter(row => {
            return item.keys.includes(row.name)
          })
          resolve(newDataFilter)
        } catch (e) {
          console.log('Error:item.url - ', item.url)
          console.log(e)
          reject(e)
        }
      })
    }).on('error', (err) => {
      console.log('item.url', item.url)
      reject(err)
    })
  })
}

/** 根据数据，生成typescript提示文件 */
function generateTS (data) {
  let content = ''
  let YModels = ''
  let YResponseTypes = ''
  data.forEach(item => {
    item.list.forEach((row) => {
      /** 生成接口顶部注释 */
      YModels += createYModels(row)
      YResponseTypes += createYResponseTypes(row)
    })
  })
  content += `/**
 * 此文件由脚本自动生成，请勿手动修改
 * 生成时间：${new Date().toLocaleString()}
 */

declare interface YModels {
  ${YModels}
}

type YResSelector<T> = T

declare interface YResponseTypes {
  ${YResponseTypes}
}
`
  /** 空行替换 */
  content = content.replace(/\n\s*\n/g, '\n')
  /** 把content写入本地文件 */
  fs.writeFileSync(path.join(process.cwd(), 'src/core/request', 'yapi.request.d.ts'), content)
  console.log('无报错提示说明更新成功')
}

function createYModels (row) {
  /** Req生成 */
  let Req = {}
  try {
    if (row.req_body_other) {
      Req = JSON.parse(row.req_body_other).properties
    }
  } catch (e) {
    console.log('header', row)
    console.log(e)
  }
  let ReqStr = createResData(Req, 0, 'req')
  /** Res生成 */
  let Res = {}
  try {
    if (row.res_body) {
      Res = JSON.parse(row.res_body).properties
    }
  } catch (e) {
    console.log('body', e, row.res_body)
  }
  if (Res && !Res.data) {
    Res = {
      data: {
        type: 'object',
        description: '返回数据',
        properties: Res,
      },
      code: { type: 'number' },
      message: { type: 'string' },
      askId: { type: 'string' },
      error: { type: 'boolean' },
    }
  }
  let ResStr = createResData(Res)
  return `/**
  * 接口名: ${row.title}
  * YApi 地址: http://yapi.3pvr.com/project/${row.project_id}/interface/api/${row._id}
  */
  '${row.method}${row.path}': {
    Req: {
    ${ReqStr}
    }
    Res: {
    ${ResStr}
    }
  }
  `
}

function createYResponseTypes(row) {
  return `
  '${row.method}${row.path}': YResSelector<YModels['${row.method}${row.path}']['Res']>
  `
}

/** 如果json的key有特殊符号则带上单引号 */
function hederKey(key) {
  if (key.includes('-') || key.includes(' ')) {
    return `'${key}'`
  }
  return key
}

/** 循环生成递归数据
 * @param type res | req 默认res
 */
function createResData (Res, deep = 0, type = 'res') {
  let str = ''
  let emptyStr = '' // 空格 用于缩进
  /** res第一层的对象设置为必选 */
  const emptyTs = (type === 'res' && deep === 0) ? '' : '?'
  for (let i = 0; i < deep; i++) {
    emptyStr += '   '
  }
  for (let key in Res) {
    const jsonKeyStr = emptyStr + hederKey(`${key}`)
    if (Res[key].type == 'object') {
      str += `
      ${emptyStr}${ Res[key].description ? `/**
      ${emptyStr}* ${Res[key].description}
      ${emptyStr}*/` : '' }
      ${jsonKeyStr}${emptyTs}: {
      ${emptyStr}${createResData(Res[key].properties, deep + 1, type)}
      ${emptyStr}}
      `
    } else if (Res[key].type == 'array') {
      const tempEmptyStrKey = (Res[key].items.properties && Object.keys(Res[key].items.properties).length > 0)
      ? `{
        ${emptyStr}${createResData(Res[key].items.properties, deep + 1, type)}
        ${emptyStr}}[]`
      : `${Res[key].items.type === 'integer' ? 'number' : Res[key].items.type.replace('object', '{ [key: string]: any }')}[]`
      str += `
      ${emptyStr}${ Res[key].description ? `/**
      ${emptyStr}* ${Res[key].description}
      ${emptyStr}*/` : '' }
      ${jsonKeyStr}${emptyTs}: ${tempEmptyStrKey}
      `
    } else {
      str += `
      ${emptyStr}${Res[key].description ? `/**
      ${emptyStr}* ${Res[key].description}
      ${emptyStr}*/` : '' }
      ${jsonKeyStr}${emptyTs}: ${Res[key].type === 'integer' ? 'number' : Res[key].type}
      `
    }
  }
  return str
}

updateDoc()
