.posBox {
  margin-top: 8px;
  height: 80px;
  background: #fff;
  display: flex;
  align-items: center;
}

.pos {
  padding: 0 24px;
  height: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: auto;
}

.posAdd {
  padding: 0 16px;
  height: 56px;
  border-radius: 56px;
  border: 2px solid #0092ff;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #0092ff;
  font-weight: 500;
  font-size: 26px;
}

.posText {
  margin-left: 4px;
}

.posScroll {
  padding: 0 8px;
  width: 554px;
  ::-webkit-scrollbar {
    display: none;
  }
}

::-webkit-scrollbar {
  display: none;
}

.posScrollFull {
  width: 654px;
}

.posList {
  display: flex;
  align-items: center;
}

.posItem {
  padding: 0 16px;
  font-weight: 500;
  font-size: 34px;
  color: #00000073;
  white-space: nowrap;
  height: 80px;
  line-height: 80px;
}

.active {
  color: rgba(0, 0, 0, 0.85);
  font-size: 46px;
  position: relative;

  &::after {
    display: block;
    position: absolute;
    bottom: 3px;
    left: 50%;
    transform: translateX(-50%);
    background: url(https://cdn.yupaowang.com/yp_mini/images/gyf/select-subscript1.png) no-repeat center center;
    width: 26px;
    height: 8px;
    content: '';
    background-size: cover;
  }
}
