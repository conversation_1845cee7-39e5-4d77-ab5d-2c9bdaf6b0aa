/*
 * @Date: 2024-11-21 09:54:23
 * @Description: 弹窗调用
 */

import type { ActionSheet } from './core.d'

/** 创建一个发布订阅 */
function createPublishSubscribe() {
  const listens: Function[] = []
  return {
    /** 订阅 */
    subscribe(fn) {
      listens.push(fn)
      return () => {
        listens.splice(listens.indexOf(fn), 1)
      }
    },
    /** 发布 */
    publish(arg) {
      return new Promise((resolve, reject) => {
        listens.forEach(fn => fn({ ...arg, resolve, reject }))
      }) as Promise<any>
    },
  }
}

const publishSubscribe = createPublishSubscribe()

const alertConfig = {
  title: '温馨提示',
  content: '提示',
  confirmText: '确定',
}

const alert = (options: Partial<typeof alertConfig>) => {
  return publishSubscribe.publish({
    type: 'alert',
    options: {
      ...alertConfig,
      ...options,
    },
  })
}

const forceLogin = (options:any = {}) => {
  
  return publishSubscribe.publish({
    type: 'forceLogin',
    options: {
      ...options,
    },
  })
}

const confirmConfig = {
  title: '温馨提示',
  content: '提示',
  confirmText: '确定',
  cancelText: '取消',
}

const confirm = (options: Partial<typeof confirmConfig>) => {
  return publishSubscribe.publish({
    type: 'confirm',
    options: {
      ...confirmConfig,
      ...options,
    },
  })
}

const actionSheetConf: ActionSheet = {
  syncClose: false,
  maskClose: true,
  dialogIdentify: '',
  title: '',
  itemList: [],
  cancelText: '取消',
}

const actionSheet = (options: Partial<ActionSheet>) => {
  return publishSubscribe.publish({
    type: 'actionSheet',
    options: {
      ...actionSheetConf,
      ...options,
    },
  })
}

export {
  publishSubscribe,
  alert,
  confirm,
  actionSheet,
  forceLogin,
}
