import style from './index.module.scss'

type IJobTypeViewProps = {
  mustObj?: any
  query?: any
  positionType?: number
  onSwitchChange?: (e: any) => void
}
const JobTypeView = (props: IJobTypeViewProps) => {
  const { mustObj = {}, query = {}, positionType = 1, onSwitchChange } = props
  const { numFull = 1, numPart = 1 } = query
  const { status = true, must = true } = mustObj.F_WorkNature || {}
  const { status: smStatus = true } = mustObj.F_SalaryMonth || {}
  const { status: sdStatus = true } = mustObj.F_SalaryDay || {}

  const onClick = (val) => {
    onSwitchChange && onSwitchChange({ detail: { value: val } })
  }

  return status ? (
    <V className={style.item}>
      <V className={style.label}>
        {must && <T className={style.must}>*</T>}<T>求职类型</T>
      </V>
      <V className={style.wantedSwitch}>
        {
          smStatus && numFull > 0 && <V className={`${style.sileft} ${sdStatus && numPart > 0 ? '' : style.sileftRb} ${style.switchItem} ${positionType == 1 ? style.switchSelect : ''}`} onClick={() => onClick(1)}>全职</V>
        }
        {
          sdStatus && numPart > 0 && <V className={`${style.siright} ${smStatus && numFull > 0 ? '' : style.sirightLb} ${style.switchItem} ${positionType == 2 ? style.switchSelect : ''}`} onClick={() => onClick(2)}>零工/兼职</V>
        }
      </V>
    </V>
  ) : null
}

export default JobTypeView
