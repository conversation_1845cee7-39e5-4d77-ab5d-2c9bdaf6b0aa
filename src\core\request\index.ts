/*
 * @Date: 2023-12-23 09:21:59
 * @Description: 请求
 */

import { SHA256 } from 'crypto-js'
import Taro from '@tarojs/taro'
import { actions, dispatch, getState } from '@/core/store'
import * as config from '../config'
import router from '../router'
import { msg } from '../utils'
import { hexMD5 } from './md5'

type Config = {
  url: string
  method: string
  data?: Record<string, string>
  config?: any
  header?: Record<string, any>
}

/** 获取系统信息
 * mid: 设备平台: model, pc, ipad
 */
export const getSystemInfoSync = (() => {
  let data: any = null
  return () => {
    if (data === null) {
      data = Taro.getSystemInfoSync()
      if (data && data.platform) {
        data.platform = `${data.platform}`.toLocaleLowerCase()
        data.mid = `${data.platform}`.toLocaleLowerCase()
        if (data.platform === 'ios' || data.platform === 'android') {
          data.mid = 'mobile'
        } else if (data.platform === 'mac' || data.platform === 'windows') {
          data.mid = 'pc'
        }
      }
    }
    return data
  }
})()

/** json对象key排序 */
// eslint-disable-next-line consistent-return
function sortObj(obj) {
  if (obj === null) {
    return obj
  }
  if (obj === undefined || obj === 'undefined') {
    return ''
  }
  if (Array.isArray(obj)) {
    return [...obj].reduce((acc, item) => {
      if (typeof item === 'object') {
        acc.push(sortObj(item))
      } else {
        acc.push(item)
      }
      return acc
    }, [])
  }
  if (typeof obj === 'object') {
    return Object.keys(obj)
      .sort()
      .reduce((acc, key) => {
        if (typeof obj[key] === 'object') {
          // eslint-disable-next-line no-param-reassign
          acc[key] = sortObj(obj[key])
        } else {
          // eslint-disable-next-line no-param-reassign
          acc[key] = obj[key]
        }
        return acc
      }, {})
  }
}

/** json对象转url的params */
// eslint-disable-next-line consistent-return
function stringifyObj(t) {
  if (t == null) {
    return t
  }
  // 数组
  if (Array.isArray(t)) {
    const list = [...t].reduce((acc, item) => {
      if (typeof item === 'object') {
        acc.push(sortObj(item))
      } else {
        // eslint-disable-next-line no-param-reassign
        acc += item
      }
      return acc
    }, [])
    return JSON.stringify(list)
  }
  // 是对象 非数据
  if (typeof t === 'object') {
    return Object.keys(t)
      .sort()
      .reduce((acc, key) => {
        if (typeof t[key] === 'object') {
          return `${acc}${key}=${JSON.stringify(t[key])}&`
        }
        return `${acc}${key}=${t[key]}&`
      }, '')
  }
}

/** 服务器的签名秘钥 */
const SECRET_SIGN = '*js1(Uc_m12j%hsn#1o%cn1'

/** 参数加密 */
async function getSign(params, timestamp, nonce) {
  const newParams = sortObj({ ...params, timestamp, nonce })
  const signString = stringifyObj(newParams) + SECRET_SIGN
  return SHA256(signString).toString()
}

/** 获取服务器时间 */
const getServerTime = (() => {
  const promise = ajax({
    method: 'POST',
    url: 'eventreport/v1/base/serverTime',
  }).then(([{ serverTime }]) => {
    return serverTime - Date.now()
  })
  return () => promise
})()

// 定义名为 getRandom 的函数，接收一个参数 min，表示生成的随机字符串的长度
const getRandom = (min) => {
  // 定义包含数字和小写字母的字符串，并将其转换为数组
  const arr = '0123456789abcdefghijklmnopqrstuvwxyz'.split('')
  // 使用 Array.from() 方法生成一个长度为 min 的数组，并对数组中的每个元素执行回调函数
  // 回调函数随机从 arr 数组中选择一个元素，作为新数组中对应位置的值
  // 然后将数组中的所有元素拼接成一个字符串
  // eslint-disable-next-line
  return Array.from({ length: min }, () => arr[Math.floor(Math.random() * arr.length)]).join('')
}

const getNonceHeader = async (token: string) => {
  /** 全局公共数据 */
  const diffTime = await getServerTime()
  const nonce = `${getRandom(10)}.${new Date().getTime() + diffTime}`
  const secreKey = config.nonceKey + token + nonce + config.requestVersion
  return {
    'x-yp-nonce': nonce,
    'x-yp-sign': hexMD5(secreKey),
  }
}

export async function getHeaders(requestData = {}) {
  const trackSeed = ''
  const refid = ''
  // eslint-disable-next-line sonarjs/pseudo-random
  const nonce = Math.round(Math.random() * 999999)
  const timestamp = `${Math.floor(new Date().getTime())}`
  const sign = await getSign(requestData, timestamp, nonce)

  const systemInfo = getSystemInfoSync()
  const model = ''
  /* 参数请求头 */
  return {
    'content-type': 'application/json',
    occversion: 2,
    /** 分享小程序的trackSeed */
    trackSeed,
    /** 用户分享的refid存入本地 */
    refid,
    /** 请求参数传输类型 */
    requestType: 'form',
    /** 来源端 1-鱼泡网 */
    business: 'YPZP',
    /** 版本号 */
    version: config.requestVersion,
    /** 版本号 */
    versionmini: 1,
    /** 版本号 */
    wechat_token: config.appid,
    /** 终端 */
    system_type: 'ks', // alipay
    /** 令牌时间 */
    token_time: '',
    /** 用户uuid */
    uuid: '',
    /** 跳系统升级页面用 */
    uid: '',
    /** 版本号 */
    mid: '',
    /** 请求时间 */
    time: timestamp,
    // ! 勿删以下header参数
    /** 签名时间戳 */
    timestamp,
    /** 应用版本，应用版本。 */
    appVersion: config.requestVersion,
    /** 操作系统 ios, android, windows, macos, linux */
    system: systemInfo.system,
    /** 系统版本 */
    systemVersion: systemInfo.version,
    /** 随机数，[1, 999999]之间的整数 */
    nonce,
    /** wx_mini: 微信小程序 bd_mini: 字节小程序 baidu_mini: 百度小程序 qq_mini: QQ小程序 */
    platform: 'ks_mini',
    /** 用户登录态token */
    token: getState().storage.token,
    /** 用户数据签名 */
    sign,
    /** 签名版本 */
    signversion: 1,
    hybrid: 'NO',
    /** 操作系统 */
    os: systemInfo.system,
    runtime: config.runtime,
    channel: systemInfo.platform,
    osVersion: systemInfo.version,
    packagename: config.appid,
    userrole: 2,
    runtimeversion: systemInfo.version,
    packageversion: config.requestVersion,
    /** 业务区分 */
    reqsource: 'YPZP',
    /** 新增参数 */
    brand: systemInfo.brand,
    model: model.indexOf('iPad') > -1 ? 'UNKONW' : model,
  }
}

function ajax(options: Config): Promise<[any, any]> {
  const { url, data = {}, method, header = {}, config: requestConfig } = options as any
  return new Promise(async (resolve, reject) => {
    Taro.request({
      url: `${config.javaDomain}/${url}`,
      method,
      data,
      header: {
        ...await getHeaders(data),
        ...header,
      },
      async success({ data: result }) {
        if (result.code === 0) {
          resolve([result.data, result])
          return
        }
        if (!requestConfig || !requestConfig.hideMsg) {
          result.message && msg(result.message)
        }

        if (result.code === 401) {
          dispatch(actions.user.clearCache())
          // 如果当前页面不是登录页跳转掉登录页
          if (router.path !== 'pages/auth-login/index') {
            router.push('/pages/auth-login/index')
          }
        }
        reject([result.data, result])
      },
      fail(err) {
        reject([err, ' err'])
      },
    })
  })
}

export default new Proxy(
  {},
  {
    get(_, p) {
      return async (data, config = {}) => {
        const [method, ...urlArr] = (p as string).split('/')
        const { token } = getState().storage
        const ypNonce = await getNonceHeader(token)
        return ajax({
          url: urlArr.join('/'),
          method: method.toUpperCase(),
          data,
          config,
          header: ypNonce,
        })
      }
    },
  },
) as YFetch
