/*
 * @Date: 2024-11-25 10:22:13
 * @Description: usePageEffect
 */

import { useEffect, useRef } from 'react'

export default function usePageEffect(fn: (path) => void, deps: any[] = []) {
  const path = useRef('')
  const data = useRef('')
  useEffect(() => {
    if (!path.current) {
      path.current = $.router.path
    }
    if (!data.current) {
      data.current = JSON.stringify(deps)
    }
    if (!path.current || ($.router.path === path.current && data.current !== JSON.stringify(deps))) {
      data.current = JSON.stringify(deps)
      fn(path.current)
    }
  }, [...deps, $.router.path])
}
