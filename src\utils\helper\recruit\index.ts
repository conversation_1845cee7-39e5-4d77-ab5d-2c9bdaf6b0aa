/*
 * @Date: 2024-12-02 14:07:01
 * @Description: 招工相关
 */

/**
 * 岗位详情参数
 */
type JobDetailParams = {
  jobId: string,
  occIds: number[],
  sortTime: string,
  recommend: boolean
}
import { store } from '@/core/store'
import { haversineDistance } from '@/utils/location'

/** 获取招工详情数据 */
export const queryJobDetail = $.createPromiseCache((params: JobDetailParams) => {
  const newParams = {
    jobId: params.jobId,
    occIds: params.occIds || [],
    sortTime: $.isTimestampOrDate(decodeURIComponent(params.sortTime)),
    recommend: params.recommend,
  } as any

  return $.request['POST/job/v2/job/info']($.delNullOp(newParams), { hideMsg: true })
})

/** 融资规模 */
export const FINANCE_SCALE_MAP = [
  { label: '未融资', value: '1' },
  { label: '天使轮', value: '2' },
  { label: 'A轮', value: '3' },
  { label: 'B轮', value: '4' },
  { label: 'C轮', value: '5' },
  { label: 'D轮及以上', value: '6' },
  { label: '已上市', value: '7' },
  { label: '不需要融资', value: '8' },
]

/** 人员规模 */
export const STAFF_SIZE_MAP = [
  { label: '0-20人', value: '1' },
  { label: '20-99人', value: '2' },
  { label: '100-499人', value: '3' },
  { label: '500-999人', value: '4' },
  { label: '1000-9999人', value: '5' },
  { label: '10000人以上', value: '6' },
]

export const getLabelByValue = (labelValueMap, value) => {
  return (labelValueMap.find(item => item.value == value) || {}).label || ''
}

/**
 * @name 埋点上报
 * @param {string} eventName 事件名称
 * @param {Data} data 招工详情数据
 * @param {object} eventData 自定义事件数据
 */
export const uploadStatisticsData = async (eventName, info, eventData: any = {}) => {
  console.log('eventData:', eventData)
  console.log('info:', info)
  if (!info) {
    return
  }

  const { buriedData = {} } = $.router.data

  const statistics = {
    info_id: String(info.jobId),
    /** 详细地址 */
    detailed_address: info.address,
    /** 距离 */
    post_distance: info.location ? getProjectAddressDistanceTxt(info.location) : '',
    /** 经纬度 */
    job_location: info.location?.longitude ? `${info.location.longitude},${info.location.latitude}` : '',
    // 实名状态
    check_degree: info.checkDegreeStatus,
    fix_price_id: info.priceInfo?.priceId || null,
    keywords_source: '',
    search_result: '',
    ...buriedData,
    ...info.buried_point_data,
    ...eventData,
    // 以下是快手无此功能
    is_famous_company: '-99999',
    real_name_view: '-99999',
    free_information: null,
    consumption_product_score: null,
    dialing_interval_duration: null,
    recommend_reason: null,
  }
  console.log('statistics:', statistics)
  $.report.event(eventName, statistics)
}

/**
 * @name 获取用户与项目地址之间的距离文本
 * @param {StringConstructor} location 项目经纬度
 * @return 获取格式化后的距离文本
 */
export function getProjectAddressDistanceTxt(projectLocation?: any): string {
  const { latitude: myLat, longitude: myLon } = store.getState().storage.userLocation || {}

  const { latitude, longitude } = projectLocation
  return haversineDistance(latitude, longitude, myLat, myLon)
}
