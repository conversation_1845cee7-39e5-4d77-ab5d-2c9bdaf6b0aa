/*
 * @Date: 2024-11-22 15:08:00
 * @Description: 骨架屏
 */

import { View as V } from '@tarojs/components'
import s from './index.module.scss'

export default () => {
  return (
    <V className={s.body}>
      {Array(5).fill(0).map((_, i) => {
        return (
          <V key={i} className={s.card}>
            <V className={s.a} />
            <V className={s.b}>
              <V className={s.c} />
              <V className={s.c} />
              <V className={s.c} />
              <V className={s.c} />
              <V className={s.c} />
            </V>
            <V className={s.d}>
              <V className={s.e} />
              <V className={s.f} />
            </V>
          </V>
        )
      })}
    </V>
  )
}
