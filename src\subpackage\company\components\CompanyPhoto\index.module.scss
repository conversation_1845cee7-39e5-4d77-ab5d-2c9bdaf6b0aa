
.wrap {
  padding: 36px 0;
}
.title {
  font-size: 34px;
  color: #FFFFFFFF;
}

.titleBox {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 32px;
}

.more {
  color: #FFFFFFA6;
  font-size: 28px;
}

.tagList {
  display: flex;
  padding: 0 32px;
  padding-top: 32px;
  margin-bottom: 32px;
}

.tag {
  margin-right: 24px;
}

.scrollView {
  height: 364px;
  width: 100%;
}

.scrollView::-webkit-scrollbar {
  display: none;
}

.sourceBox {
  width: 600px;
  height: 364px;
  border-radius: 16px;
  margin-right: 20px;
  position: relative;

  &.first {
    padding-left: 32px;
    box-sizing: content-box;
  }

}

.source {
  width: 600px;
  height: 364px;
  border-radius: 16px;
}

.scrollView {
  display: flex;
  // white-space: nowrap;
}

.videoPlay {
  position: absolute;
  width: 80px;
  height: 80px;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
}

.showAllBox {
  // width: 128px;
  padding: 0 38px;
  height: 364px;
  background: #ffffff14;
  border-radius: 16px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin-right: 32px;
}

.showAllText {
  font-size: 26px;
  color: #fff;
  white-space: nowrap;
}

.allIcon {
  width: 32px;
  height: 32px;
  margin-top: 20px;
}