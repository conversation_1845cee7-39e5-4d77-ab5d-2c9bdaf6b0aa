.compact {
    display: flex;
    flex-direction: row;
    // gap: 16px;
    flex-wrap: wrap;
    // max-height: 200px;
    overflow-y: hidden;
    position: relative;
    margin-top: 32px;
}

.gridItem {
    padding: 0 20px;
    height: 56px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 26px;
    font-weight: 400;
    color:$text65;
    // line-height: 36px;
    background:  rgba(245, 247, 252, 1);
    border-radius: 8px;
    margin: 0 16px 16px 0;
    // transition: opacity 50ms 30ms ease-out;
}

.arrowDown {
    margin-left: 12px;
}
