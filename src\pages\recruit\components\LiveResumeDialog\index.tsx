import { View, Image, Button, Text } from '@tarojs/components'

import ModalBody from '@/components/Modal/Body'
import styles from './index.module.scss'

/** 引导填写在线简历弹窗 */
export default ({ visible, onClose }) => {
  const jumpToResume = () => {
    onClose && onClose()
    $.router.push('/subpackage/resume-publish/index', {}, { isBack: true })
  }

  return <ModalBody visible={visible}>
    <View className={styles.container}>
      <View className={styles.content}>
        <Image className={styles.resumeIc} mode="aspectFill" src="https://cdn.yupaowang.com/yupao_mini/live_resume_dialog_c.png"></Image>
        <View className={styles.title}>填写在线简历</View>
        <View className={styles.desc}>让招聘HR更了解你，系统会推荐更精准的职位。能提升 <Text className={styles.primary}>2倍</Text> 曝光度，提升 <Text className={styles.primary}>3倍</Text> 的沟通效率</View>
        <View className={styles.footer}>
          <Button className={styles.footerBtn} onClick={onClose}>下次填写</Button>
          <Button className={`${styles.footerBtn} ${styles.primaryBtn}`} onClick={jumpToResume}>立即填写</Button>
        </View>
      </View>
      <Image className={styles.closeIc} src="https://cdn.yupaowang.com/yupao_mini/live_resume_dialog_close.png" onClick={onClose}></Image>
    </View>
  </ModalBody>
}
