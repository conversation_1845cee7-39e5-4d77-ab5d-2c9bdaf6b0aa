import { View } from '@tarojs/components'
import { memo } from 'react'
import styles from './index.module.scss'
import ViewTags from '../ViewTags'

interface ClassifyCardProps {
    info: JobDetail
}

const ClassifyCard = memo((props: ClassifyCardProps) => {
  const { info = {} } = props

  const occShowTags = $.getObjVal(info, 'occShowTags', [])

  return (
    <View className={styles.container}>
      {occShowTags.map(item => (<ItemCard key={item.occId} item={item} />))}
    </View>
  )
})

const ItemCard = ({ item = {} }: { item: Record<string, any> }) => {
  const { showTags = [] } = item
  const salaryTag = showTags.find(item => item.type == 1) || { name: '' }
  const resTags = showTags.filter(item => item.type !== 1) || []

  return (<View className={styles.contentContainer}>
    <View className={styles.headerRow}>
      <View className={styles.classifyName}>{item.occName}</View>
      <View className={styles.salary}>{salaryTag.name}</View>
    </View>
    <ViewTags resTags={resTags} compactClass={styles.compact} id={item.occId} loading={false} defaultRows={2} />
  </View>)
}

export default ClassifyCard
