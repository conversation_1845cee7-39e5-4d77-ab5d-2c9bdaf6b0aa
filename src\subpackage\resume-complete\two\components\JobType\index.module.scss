.item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 48rpx 0;
  border-bottom: 2rpx solid rgba(233, 237, 243, 1);
}

.label {
  display: flex;
  align-items: center;
  color: rgba(0, 0, 0, 0.85);
  font-size: 28rpx;
  line-height: 160.000002%;
}

.must {
  font-size: 28rpx;
  color: rgba(232, 54, 46, 1);
  line-height: 40rpx;
}

.wantedSwitch {
  display: flex;
  align-items: center;
  justify-content: center;
  // box-shadow: inset 0 0 0 1rpx rgba(0, 146, 255, 1);
  // border: 1px solid rgba(0, 146, 255, 1);
  // border-radius: 8rpx;
  font-size: 26rpx;
  color: rgba(0, 146, 255, 1);
  height: 60rpx;
}

.switchItem {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  transition: all 0.3s ease;
  width: 150rpx;
}

.switchSelect {
  background: rgba(0, 146, 255, 1);
  color: rgba(255, 255, 255, 1);
}


.sileft{
  border-top-left-radius: 8rpx;
  border-bottom-left-radius: 8rpx;
  border-left: 2rpx solid rgba(0, 146, 255, 1);
  border-top: 2rpx solid rgba(0, 146, 255, 1);
  border-bottom: 2rpx solid rgba(0, 146, 255, 1);
}

.sileftRb{
  border-top-right-radius: 8rpx;
  border-bottom-right-radius: 8rpx;
  border-right: 2rpx solid rgba(0, 146, 255, 1);
}


.siright{
  border-top-right-radius: 8rpx;
  border-bottom-right-radius: 8rpx;
  border-radius:  0 8rpx 8rpx 0 ;
  border-right: 2rpx solid rgba(0, 146, 255, 1);
  border-top: 2rpx solid rgba(0, 146, 255, 1);
  border-bottom: 2rpx solid rgba(0, 146, 255, 1);
}


.sirightLb{
  border-left: 2rpx solid rgba(0, 146, 255, 1);
  border-top-left-radius: 8rpx;
  border-bottom-left-radius: 8rpx;
}