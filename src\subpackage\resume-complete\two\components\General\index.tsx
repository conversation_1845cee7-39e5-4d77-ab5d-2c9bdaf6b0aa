import style from './index.module.scss'
import IconFont from '@/components/IconFont'

type IExpPosViewProps = {
  /** 必填类型 */
  type?: string
  mustObj?: any
  /** 显示的内容 */
  value?: string
  /** 是否允许修改 */
  isClsChange?: boolean
  label?: string
  placeholder?: string
  isNoWarp?: boolean
  onClick?: () => void
}
const ExpPosView = (props: IExpPosViewProps) => {
  const { isNoWarp = false, type = '', mustObj = {}, value = '', isClsChange = true, onClick, label, placeholder } = props
  const { must = false, status = true } = mustObj[type] || {}
  return status ? (
    <V className={style.item}>
      <V className={style.label}>
        {must && isClsChange && <T className={style.must}>*</T>}<T>{label}</T>
      </V>
      <V className={style.content} onClick={onClick}>
        <T className={`${style.names} ${value && isNoWarp ? style.txtNowrap : ''} ${value ? '' : style.plac}`}>{value || placeholder}</T>
        {
          isClsChange && <IconFont className={style.icon} type='yp-mianbaoxue' size={32} color='rgba(0, 0, 0, 0.45)' />
        }
      </V>
    </V>
  ) : null
}

export default ExpPosView
