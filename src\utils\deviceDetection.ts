/*
 * @Date: 2025-01-18
 * @Description: 设备检测工具函数
 */

/**
 * 检测是否是荣耀设备
 * @param sysInfo 系统信息对象
 * @returns boolean
 */
export const isHonorDevice = (sysInfo?: any): boolean => {
  if (!sysInfo || !sysInfo.systemInfo) return false
  
  const brand = sysInfo.systemInfo?.brand?.toLowerCase() || ''
  const model = sysInfo.systemInfo?.model?.toLowerCase() || ''
  
  // 检测荣耀设备的多种标识
  return brand.includes('honor') || 
         model.includes('honor') || 
         brand.includes('荣耀') || 
         model.includes('荣耀') ||
         // 一些荣耀设备可能显示为华为品牌但型号包含荣耀标识
         (brand.includes('huawei') && model.includes('honor'))
}

/**
 * 检测是否是荣耀设备的快手小程序
 * @param sysInfo 系统信息对象
 * @returns boolean
 */
export const isHonorKwaiDevice = (sysInfo?: any): boolean => {
  return process.env.TARO_ENV === 'kwai' && isHonorDevice(sysInfo)
}

/**
 * 检测是否是华为设备
 * @param sysInfo 系统信息对象
 * @returns boolean
 */
export const isHuaweiDevice = (sysInfo?: any): boolean => {
  if (!sysInfo || !sysInfo.systemInfo) return false
  
  const brand = sysInfo.systemInfo?.brand?.toLowerCase() || ''
  const model = sysInfo.systemInfo?.model?.toLowerCase() || ''
  
  return brand.includes('huawei') || 
         model.includes('huawei') || 
         brand.includes('华为') || 
         model.includes('华为')
}

/**
 * 检测是否需要特殊的弹窗层级处理
 * 主要针对快手小程序中的荣耀和华为设备
 * @param sysInfo 系统信息对象
 * @returns boolean
 */
export const needsSpecialPopupZIndex = (sysInfo?: any): boolean => {
  if (process.env.TARO_ENV !== 'kwai') return false
  
  return isHonorDevice(sysInfo) || isHuaweiDevice(sysInfo)
}

/**
 * 获取适合当前设备的弹窗z-index值
 * @param sysInfo 系统信息对象
 * @param defaultZIndex 默认的z-index值
 * @returns number
 */
export const getPopupZIndex = (sysInfo?: any, defaultZIndex: number = 10001): number => {
  if (needsSpecialPopupZIndex(sysInfo)) {
    // 对于需要特殊处理的设备，使用更高的z-index
    return Math.max(defaultZIndex, 99999)
  }
  return defaultZIndex
}

/**
 * 获取适合当前设备的遮罩层z-index值
 * @param sysInfo 系统信息对象
 * @param defaultZIndex 默认的z-index值
 * @returns number
 */
export const getMaskZIndex = (sysInfo?: any, defaultZIndex: number = 10000): number => {
  if (needsSpecialPopupZIndex(sysInfo)) {
    // 对于需要特殊处理的设备，使用更高的z-index，但要比弹窗低1
    return Math.max(defaultZIndex, 99998)
  }
  return defaultZIndex
}
