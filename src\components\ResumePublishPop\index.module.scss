.warp {
  padding: 0 32px;
}

.head {
  padding: 29rpx 0;
}

.title {
  color: rgba(0, 0, 0, 0.85);
  font-weight: bold;
  font-size: 38rpx;
}

.item {
  padding: 32rpx 0;
}

.label {
  margin-bottom: 24rpx;
}

.labelTxt {
  color: rgba(0, 0, 0, 0.85);
  font-size: 38rpx;
  font-weight: bold;
}

.content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 686rpx !important;
  min-height: 48rpx;
  padding: 32rpx 24rpx;
  border: solid 2rpx rgba(233, 237, 243, 1);
  border-radius: 16rpx;
}

.value {
  color: rgba(0, 0, 0, 0.85);
  font-size: 34rpx;
}

.placeholder {
  color: rgba(0, 0, 0, 0.25);
  font-size: 34rpx;
}

.icon {
  margin-left: 32rpx;
}

.footer {
  padding: 24rpx 32rpx;
  @include safe-area(24px);
}

.btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 96rpx;
  border-radius: 16rpx;
}

.btnTxt {
  font-weight: bold;
  font-size: 34rpx;
}

.btnOk {
  background: rgba(0, 146, 255, 1);
  color: rgba(255, 255, 255, 1);
}

.btnNo {
  background: rgba(153, 211, 255, 1);
  color: rgba(255, 255, 255, 0.45);
}

.closeImg {
  width: 48rpx;
  height: 48rpx;
  position: absolute;
  right: 32rpx;
  top: 32rpx;
}

.addressTips {
  width: fit-content;
  height: 64px;
  display: inline-flex;
  place-content: center;
  place-items: center;
  gap: 12rpx;
  flex-shrink: 0;
  border-radius: 16rpx;
  background: rgba(224, 243, 255, 1);
  padding: 0 24rpx;
  margin-bottom: 32rpx;
}

.gpsImg {
  width: 36rpx;
  height: 36rpx;
}

.gpsText {
  font-size: 26rpx;
  color: rgba(0, 146, 255, 1);
}

