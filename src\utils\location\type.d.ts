type ICurrent = ILocation.TAreaData & {
  /** 祖级id */
  gid?: number | string
}
type IDistrict = ILocation.TAreaData & {
  /** 祖级id */
  gid: number | string
}

/** 省市区 */
export interface ITreeArea {
  /** 省份信息 */
  province: ILocation.TAreaData | ''
  /** 城市信息 */
  city: ILocation.TAreaData | ''
  /** 地区信息 gid 为祖级id */
  district: IDistrict | ''
  /** 当前地址信息 */
  current: ICurrent | ''
}

export interface ITreeAreaAll {
  /** 省份信息 */
  province: ILocation.TAreaData
  /** 城市信息 */
  city: ILocation.TAreaData
  /** 地区信息 gid 为祖级id */
  district: IDistrict
  /** 当前地址信息 */
  current: ICurrent
}
