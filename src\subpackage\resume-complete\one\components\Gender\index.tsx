import style from './index.module.scss'

type IGenderViewProps = {
  value: any
  mustObj?: any
  onChanges?: (e: any, type: string) => void
  [key: string]: any
}

const GenderView = (props: IGenderViewProps) => {
  const { value, onChanges, mustObj = {} } = props
  const { status, must } = mustObj || {}

  const onClick = (gender?: number) => {
    onChanges && onChanges({ detail: { value: gender } }, 'gender')
  }
  return status ? (
    <V className={style.item}>
      <V className={style.label}>
        {must && <T className={style.must}>*</T>}<T>性别</T>
      </V>
      <V className={style.genderView}>
        <V onClick={() => onClick(1)} className={`${style.gender} ${value == 1 ? style.genderSlted : ''}`}>男</V>
        <V onClick={() => onClick(2)} className={`${style.gender} ${value == 2 ? style.genderSlted : ''}`}>女</V>
      </V>
    </V>
  ) : null
}

export default GenderView
