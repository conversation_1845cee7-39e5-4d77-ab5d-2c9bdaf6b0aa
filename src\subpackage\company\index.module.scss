.wrap {
  width: 750px;
  min-height: 100vh;
  background: #fff;
  background-position: center;
  background-size: cover;

  &::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
    backdrop-filter: blur(600px);
  }
}

.scrollView::-webkit-scrollbar {
  display: none;
}

.op {
  //透明背景
  background: transparent!important;
}


