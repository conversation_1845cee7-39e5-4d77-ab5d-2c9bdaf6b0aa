/*
 * @Date: 2023-12-21 14:55:53
 * @Description: store
 */

import { connect as oldConnect, useDispatch, useSelector as oldUseSelector } from 'react-redux'
import { configureStore } from '@reduxjs/toolkit'
import { actions as oldActions, reducer } from '@/store/reducer'

export { actions } from '@/store/reducer'

export const store = configureStore({
  devTools: false,
  reducer,
  middleware: (getDefaultMiddleware) => getDefaultMiddleware({
    serializableCheck: false,
  }),
})

export const { dispatch, getState } = store
export type RootState = ReturnType<typeof store.getState>
export type AppDispatch = typeof store.dispatch
export const useAppDispatch = () => useDispatch<AppDispatch>()

type Connect = (
  mapStateToProps: (state: RootState) => Record<string, any>,
  mapDispatchToProps?: (
    dispatch: AppDispatch,
    actions: typeof oldActions,
  ) => Record<string, any>,
) => Function

/** connect 内置了类型提示 */
export const connect: Connect = oldConnect

type EqualityFn<T> = (a: T, b: T) => boolean
type UseSelector = <T>(
  select: (s: RootState) => T,
  equalityFn?: EqualityFn<T>,
) => T

/** useSelector 内置了类型提示 */
export const useSelector: UseSelector = oldUseSelector

// 使用代码 messageQueue 入参返回值为true await就会执行完毕
// const [tel] = await messageQueue(state => state.user.userInfo.tel)
/** 消息队列，用于page页面 onLoad 等场景，store没有值的时候，但onLoad又需要值的场景 */
export const messageQueue = (() => {
  // 队列
  const queue: any = []
  // 3分钟
  const timeout = 1000 * 60 * 3
  // 添加store监听，这个监听永远不会解除
  store.subscribe(checkQueue)
  /**
   * @description: 检查队列中是否有可释放的promise
   */
  function checkQueue(time = timeout) {
    const state = store.getState()
    const currentTime = new Date().getTime()
    for (let i = 0; i < queue.length; i += 1) {
      const item = queue[i]
      // 超过三分钟的就直接移除
      if (currentTime - item.time > time) {
        // 释放promise 调用 reject
        item.isReject && item.reject([undefined, state])
        clearTimeout(item.timer)
        queue.splice(queue.indexOf(item), 1)
        // eslint-disable-next-line sonarjs/updated-loop-counter
        i -= 1
      } else {
        // 匹配到值就执行对应的函数，并且移除队列
        const value = item.getValue(state)
        if (value) {
          item.resolve([value, state])
          clearTimeout(item.timer)
          queue.splice(queue.indexOf(item), 1)
          // eslint-disable-next-line sonarjs/updated-loop-counter
          i -= 1
        }
      }
    }
  }

  /**
   * @param getValue 如：(state => state.recruitDetail.requestData)
   * @param isReject 3分钟没有获取到内容是否需要抛出错误 （默认false）
   * @param time 超时时间 默认3分钟
   */
  return function <T> (getValue: (state: RootState) => T, isReject = false, time = timeout): Promise<[T, RootState]> {
    return new Promise<[T, RootState]>((resolve, reject) => {
      const state = store.getState()
      // 如果已经有值就直接执行
      const value = getValue(state)
      if (value) {
        resolve([value, state])
      } else {
        // 防止等待时间过去后，subscribe未被触发，手动执行一次
        const timer = setTimeout(() => {
          checkQueue(time)
        }, time + 50)
        // 往队列里面添加函数
        queue.push({ getValue, resolve, reject, time: new Date().getTime(), isReject, timer })
      }
    })
  }
})()
