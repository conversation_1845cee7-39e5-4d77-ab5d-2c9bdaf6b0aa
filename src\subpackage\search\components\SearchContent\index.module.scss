.SearchContent {
  padding: 32px 0;
  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;

    .title {
      font-size: 34px;
      font-weight: bold;
      color: rgba(0, 0, 0, 0.85);
    }

    .clearHistory {
      font-size: 28px;
      color: #00000073;
      cursor: pointer;
    }
    .icon {
      margin-left: 6px;
    }
  }

  .list {
    display: flex;
    flex-wrap: wrap;
    max-height: calc(2 * (42px + 30px + 14px)); /* 计算两行的高度 */
    overflow: hidden;
    .tag {
      padding: 15px 24px;
      margin-bottom: 16px;
      margin-right: 16px;
      background-color: #F5F7FCFF;
      border-radius: 16px;
      font-size: 30px;
      line-height: 42px;
      color: rgba(0, 0, 0, 0.85);
      cursor: pointer;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
    .tag:active {
      color: #0092ff;
    }

    .emptyMessage {
      font-size: 28px;
      color: #999;
    }
  }
}