import { Text as T, View as V, Image as Img } from '@tarojs/components'
import IconFont from '@/components/IconFont'
import styles from './index.module.scss'
import { IMAGE_CDN } from '@/core/config'
import { FormatEnterpriseOptionListType } from '../../formatEnterpriseMainView'
import BenefitPopup from '../BenefitPopup'

type IProps = {
  data: Record<string, any>
  enterpriseOptionList: Partial<FormatEnterpriseOptionListType>
}

/**
 * 公司福利信息
*/
export default (props: IProps) => {
  const { data, enterpriseOptionList: item } = props

  // 头像路径
  const avatar = data.enterpriseAvatar || `${IMAGE_CDN}zb/yp-mini_company_default_avatar.png`

  const benefitList = [
    ...(item.insurance?.content || []),
    ...(item.stockOptionSalary?.content || []),
    ...(item.leaveVacation?.content || []),
    ...(item.lifeSubsidy?.content || []),
  ]
  // console.log(benefitList)
  const showWork = item.workTime?.content || item.restTime?.content || item.overtimeSituationTime?.content
  return (
    <V className={styles.wrap}>
      <V className={styles.header}>
        <V>
          <V className={styles.name}>{data.brandName || data.name}</V>
          <T className={styles.state}>{item.financeScale?.content}</T>
          {
            item.financeScale?.content && item.staffSize?.content && <T className={styles.point}>.</T>
          }
          <T className={styles.state}>{item.staffSize?.content}</T>
        </V>
        <Img className={styles.logo} src={avatar} mode="aspectFill"></Img>
      </V>
      <BenefitPopup data={item} benefitList={benefitList}>
        <V>
          {
            showWork && (
              <V className={styles.work}>
                <V className={styles.workLeft}>
                  {
                    item.workTime?.content && (
                      <V className={styles.item}>
                        <IconFont type='yp-clock' size={32} color='#fff' />
                        <T className={styles.itemText}>{ item.workTime?.content }</T>
                      </V>
                    )
                  }
                  {
                    item.restTime?.content && (
                      <V className={styles.item}>
                        <IconFont type='yp-calendar' size={32} color='#fff' />
                        <T className={styles.itemText}>{item.restTime?.content}</T>
                      </V>
                    )
                  }
                  {
                    item.overtimeSituationTime?.content && (
                      <V className={styles.item}>
                        <IconFont type='yp-gong-wen-bao' size={32} color='#fff' />
                        <T className={styles.itemText}>{ item.overtimeSituationTime?.content }</T>
                      </V>
                    )
                  }

                </V>
                <IconFont type='yp-mianbaoxue' size={32} color='#fff' />
              </V>
            )
          }
          {
            !!benefitList.length && (
              <V className={styles.benefitList}>
                {
                  benefitList.map((item, index) => (
                    <>
                      <V className={styles.benefitItem} key={item.value}>
                        <IconFont type={item.icon} size={48} color='#fff' />
                        <T className={styles.benefitItemText}>{item.label}</T>
                      </V>
                      {/* 解决ios兼容性 */}
                      {
                        index === benefitList.length - 1 && (
                          <V style={{ width: '32rpx', height: '1px', opacity: 0 }}>1</V>
                        )
                      }
                    </>
                  ))
                }
              </V>
            )
          }
        </V>
      </BenefitPopup>
    </V>
  )
}
