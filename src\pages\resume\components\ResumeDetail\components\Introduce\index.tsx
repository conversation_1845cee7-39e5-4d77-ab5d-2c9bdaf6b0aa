import CellCard from '../CellCard'
import ExStatus from '../ExStatus'
import s from './index.module.scss'

type PropsType = {
  dataSource: any
  onClick: (e?) => void
  onRefresh: (e?) => void
}

/** 个人信息卡片 */
export default function Index(props: PropsType) {
  const dataSource = props.dataSource || {}
  const onJump = () => {
    props.onClick && props.onClick()
    $.router.push(
      '/subpackage/my-resume/edit-introduce/index',
      {},
      { content: dataSource.introduce || '' },
      () => {
        props.onRefresh && props.onRefresh()
      },
    )
  }
  return (
    <CellCard
      title="个人优势"
      titleIcon="yp-icon_edit_grzl"
      onIcon={onJump}
      childrenDesc={
        dataSource.checkStatus != null && <V className={s['desc-box']}>
          <ExStatus
            checkStatus={dataSource.checkStatus}
            checkText={dataSource.checkErrMsg}
          />
        </V>
      }
    >
      <V onClick={onJump}>
        { !dataSource.introduce
          ? <V className={s.placeholder}>丰富的个人优势介绍，更能赢得老板青睐</V>
          : <V className={s['user-strength']}>{dataSource.introduce}</V>
        }
      </V>
    </CellCard>
  )
}
