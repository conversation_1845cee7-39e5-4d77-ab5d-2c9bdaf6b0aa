import { ScrollView, Image as Img, Text as T, View as V } from '@tarojs/components'
import styles from './index.module.scss'
import Popup from '@/components/Popup'
import useReducer from '@/hooks/useReducer'
import IconFont from '@/components/IconFont'
import ProductPopup from '../ProductPopup'

/**
 * 产品介绍
*/
export default (props: { data: any[]}) => {
  const [{ showIndex }, dispatch] = useReducer({
    showIndex: -1,
  })
  const clickItem = (i: number) => {
    dispatch({ showIndex: i })
  }

  return (
    <V className={styles.warp}>
      <T className={styles.title}>产品介绍</T>
      <ScrollView scrollX className={styles.tagList}>
        <V style={{ display: 'flex' }}>
          {
            props.data.map((item, index) => (
              <V className={styles.item} key={item.id} onClick={() => clickItem(index)}>
                <Img className={styles.itemLeft} src={item.logo} mode="aspectFill" />
                <V className={styles.itemRight}>
                  <V className={styles.itemTitle}>{item.name}</V>
                  <T className={styles.itemTips}>{item.description}</T>
                </V>
              </V>
            ))
          }
        </V>
      </ScrollView>
      <ProductPopup showIndex={showIndex} data={props.data} onClose={() => dispatch({ showIndex: -1 })} />
    </V>
  )
}
