import { Picker } from '@tarojs/components'
import { memo } from 'react'
import s from './index.module.scss'
import './baseCss.scss'
import { getTimeVal } from '../../utils'

type PropsType = {
  start: string
  end: string
  value: string
  onChange: (date: string) => void
}

export default memo((props: PropsType) => {
  const onChange = (e) => {
    let { value } = e.detail
    value = value.replace(/\//g, '-')
    value = getTimeVal(value, props.start, props.end)
    props.onChange(value)
  }

  return (
    <V className={s.formPicker}>
      <Picker
        value={props.value || props.end}
        mode="date"
        fields="month"
        className={s.pickerCont}
        start={props.start}
        end={props.end}
        onChange={onChange}
      >
        <V className={s.pickerCont}></V>
      </Picker>
    </V>
  )
})
