.body {
  height: 72px;
  background-color: #fff;
  display: flex;
  align-items: center;
}

.box {
  display: flex;
  align-items: center;
}

.item {
  padding: 0 24px;
  font-size: 28px;
  color: #000000a6;
  font-weight: 400;
  transition: all 0.1s;
  position: relative;

  &.active {
    color: rgba(0, 0, 0, 0.85);
    font-weight: 500;
  }

  .buoy {
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    bottom: -88px;
    font-size: 24px;
    background: #0092ff;
    height: 56rpx;
    display: flex;
    align-items: center;
    padding: 0 24px;
    white-space: nowrap;
    border-radius: 8px;
    color: #fff;

    &::before {
      content: '';
      position: absolute;
      left: 50%;
      transform: translateX(-50%);
      top: -26px;
      width: 0;
      height: 0;
      background: transparent;
      border: 14px solid transparent;
      border-bottom-color: #0092ff;
    }

  }
}

.city {
  margin-left: auto;
  margin-right: 24px;
  padding: 0 34px 0 16px;
  background: #f5f6fa;
  height: 56px;
  display: flex;
  align-items: center;
  color: #000000a6;
  font-size: 28px;
  border-radius: 8px;
  position: relative;

  &::after {
    display: block;
    content: '';
    position: absolute;
    right: 16px;
    bottom: 8px;
    width: 0;
    height: 0;
    border: 5px solid #00000073;
    border-top-color: transparent;
    border-left-color: transparent;
  }
}

.cityActive {
  background: #e0f3ff;

  &::after {
    display: block;
    content: '';
    position: absolute;
    right: 16px;
    bottom: 8px;
    width: 0;
    height: 0;
    border: 5px solid #0092ff;
    border-top-color: transparent;
    border-left-color: transparent;
  }
}

.cityTextActive {
  color: #0092ff;
}