import classNames from 'classnames'
import { useEffect } from 'react'
import { View as V } from '@tarojs/components'
import useReducer from '@/hooks/useReducer'
import styles from './index.module.scss'

type IProps = {
  children?: string
  /** 是否选中 */
  choose?: boolean
  /** 切换事件 */
  onChange?: (choose: boolean) => void
  /** 是否能点击, 默认true */
  isClick?: boolean
  className?: string
}

/**
 * tag
*/
export default (props: IProps) => {
  const { onChange, children, choose, isClick = true, className } = props
  const [{ state }, dispatch] = useReducer({ state: false })

  useEffect(() => {
    choose !== state && dispatch({ state: choose })
  }, [choose])

  return (
    <V
      className={classNames(styles.tag, state && styles.tagActive, className)}
      onClick={() => {
        if (!isClick) return
        onChange?.(!state)
        dispatch({ state: !state })
      }}
    >{children}</V>
  )
}
