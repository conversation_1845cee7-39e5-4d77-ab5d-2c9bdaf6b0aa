import { Block, View } from '@tarojs/components'
import classNames from 'classnames'
import styles from './index.module.scss'

export default function Skeleton({ loading = false, style }) {
  if (!loading) return <Block></Block>

  return (
    <View className={styles.container} style={style} onTouchMove={(event) => event.stopPropagation()}>
      <View className={classNames(styles.columns, styles.section1)}>
        <View className={classNames(styles.row, 'skeleton')}></View>
        <View className={classNames(styles.row, 'skeleton')}></View>
        <View className={classNames(styles.halfRow, 'skeleton')}></View>
      </View>
      <View className={styles.userInfo}>
        <View className={classNames(styles.avatar, 'skeleton')}></View>
        <View className={classNames(styles.columns, styles.userInfoText)}>
          <View className={classNames(styles.nameRow, 'skeleton')}></View>
          <View className={classNames(styles.companyRow, 'skeleton')}></View>
        </View>
      </View>
      <View className={classNames(styles.columns, styles.section2)}>
        <View className={classNames(styles.section, 'skeleton')}></View>
        <View className={classNames(styles.titleRow, 'skeleton')}></View>
        <View className={classNames(styles.subRow, 'skeleton')}></View>
        <View className={classNames(styles.subRow, 'skeleton')}></View>
        <View className={classNames(styles.subRow, 'skeleton')}></View>
        <View className={classNames(styles.subRow, 'skeleton')}></View>
      </View>
    </View>
  )
}
