import { Block, Text, View } from '@tarojs/components'
import style from './index.module.scss'
import IconFont from '@/components/IconFont'

type IWodeSltedViewProps = {
  /** 选择的职位 */
  sltedItemArr: Array<any>
  /** 选中的最大数量 */
  maxNum: any
  // 点击方法
  onClick?: (id: string) => void
  // 清楚方法
  onClear?: () => void
  /** 确认方法 */
  onConfirm?: () => void
  [key: string]: any
}

const WodeSltedView = (props: IWodeSltedViewProps) => {
  const { sltedItemArr = [], onClick, onClear, onConfirm, maxNum = 5 } = props

  return (
    <View className={style.wodeSltedView}>
      {
        sltedItemArr.length ? (
          <Block>
            <View className={style.wodeHead}>
              <Text className={style.wodeTitle}>我选择的职位</Text>
              <Text className={style.wodeNum}>已选{sltedItemArr.length}/{maxNum}</Text>
            </View>
            <View className={style.wodeSltedItemView}>
              {
                sltedItemArr.map((item: any, index) => {
                  return (
                    <View className={style.wodeSltedItem} key={index} onClick={() => onClick && onClick(item)}>
                      <Text className={style.wodeSltedTxt}>{item.name}{item.matchKeyword}</Text>
                      <IconFont type='yp-close-small' size={24} color='rgba(0, 146, 255, 1)' />
                    </View>
                  )
                })
              }
            </View>
          </Block>
        ) : null
      }

      <View className={style.wodeBtnView}>
        <View className={`${style.wodeBtn} ${style.wodeClear}`} onClick={onClear}>清除</View>
        <View className={`${style.wodeBtn} ${style.wodeConfirm}`} onClick={onConfirm}>确定选择</View>
      </View>
    </View>

  )
}

export default WodeSltedView
