/*
 * @Date: 2024-11-20 14:04:18
  * @Description: 底部悬浮按钮
 */

import cn from 'classnames'
import { Button, StandardProps } from '@tarojs/components'
import { useEffect, useState } from 'react'
import s from './index.module.scss'
import IconFont from '@/components/IconFont'

type Props = StandardProps & {
  iconType?: string
}

export default (props: Props) => {
  const {
    iconType = '',
  } = props

  return (
    <V className={s.tag} onClick={props.onClick}>
      {props.children}
      {iconType && <IconFont type={iconType}></IconFont>}
    </V>
  )
}
