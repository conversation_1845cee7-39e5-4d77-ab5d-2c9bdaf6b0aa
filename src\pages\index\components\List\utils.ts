/*
 * @Date: 2024-12-19
 * @Description: List组件工具函数
 */

import { store } from '@/core/store'
import { delNullOp } from '@/core/utils'
import { fetchResumeExist } from '@/utils/helper/resume'
import { uploadStatisticsData } from '@/utils/helper/recruit'

/**
 * 投递预检查
 */
export const applyPreCheck = (params: any) => {
  return $.request['POST/job/v3/contact/job/im/preCheck'](delNullOp(params), {
    hideMsg: true,
  }).then(([_, res]) => {
    return applyJob(params)
  })
}

/**
 * 执行投递
 */
export const applyJob = (params) => {
  return $.request['POST/job/v3/contact/job/im/call'](delNullOp(params), {
    hideMsg: true,
  }).then(([data]) => {
    return data
  })
}

/**
 * 首页免费报名投递逻辑
 */
export const freeRegistrationForKuaishou = async (params: {
  item: any
  extData: Record<string, any>
  showResumeDialog: () => void
  applySuccessCallback: () => void
  // eslint-disable-next-line sonarjs/cognitive-complexity
}) => {
  const { item, extData, showResumeDialog, applySuccessCallback } = params
  const { buriedData } = extData

  // 检查登录状态
  if (!store.getState().storage.token) {
    await $.login()
  }

  // 检查是否已投递
  const applied = item.isIm
  if (applied) {
    $.hideLoading()
    return
  }

  // 检查简历是否存在
  const existData = await fetchResumeExist(true)

  if (existData && !existData.exist) {
    $.hideLoading()
    showResumeDialog()
    return
  }

  try {
    $.showLoading('投递中...')
    const occIds = extData?.occIds || []
    const occV2 = occIds.length ? [{ industry: -1, occIds }] : []

    await applyPreCheck({
      jobId: item.jobId,
      scene: 19,
      occV2,
      algorithmid: buriedData.backend_id,
    }).then(() => {
      uploadStatisticsData('clickBoss', item, {
        ...buriedData,
        get_status: 1,
      })
      applySuccessCallback()
      $.hideLoading()
      $.msg('投递成功')
    })
  } catch ([_, error]) {
    uploadStatisticsData('clickBoss', item, {
      ...buriedData,
      get_status: 0,
    })
    $.hideLoading()

    if (error instanceof Error) {
      $.msg('网络异常')
    } else if (error.code !== 0) {
      const dialogData = $.getObjVal(error, 'dialogData')
        || $.getObjVal(error, 'data.dialogData')

      if (dialogData && dialogData.dialogIdentify === 'statusRestrictPublish') {
        $.confirm({
          title: '温馨提示',
          content: '您的账号状态异常，部分功能被限制使用。如有疑问，请联系客服处理。电话：************',
          cancelText: '确定',
          confirmText: '联系客服',
        }).then(() => $.taro.makePhoneCall({ phoneNumber: '4008381888' }))
      } else if (dialogData && dialogData.dialogIdentify == 'chat_limit') {
        $.alert({
          title: '温馨提示',
          content: '您今日聊得太多，休息一下明天再来吧~',
          confirmText: '确定',
        })
      } else if (error && error.code == 30010001) {
        // 职位已下架
        console.log('职位已下架')
      } else {
        $.confirm({
          title: '温馨提示',
          content: '投递失败,如有疑问,请联系客服处理。电话:************',
          confirmText: '联系客服',
          cancelText: '确定',
        }).then(() => $.taro.makePhoneCall({ phoneNumber: '4008381888' }))
      }
    }
  }
}

/**
 * 根据传入的 occV2 数组生成 hopeOcc 数组
 * @param occV2 - 输入的数组
 * @returns 返回生成的 hopeOcc 数组
 */
export const generateHopeOcc = (occV2: any[]): any[] => {
  // 如果 occV2 为空或者是空数组，直接返回空数组
  if (!occV2 || occV2.length === 0) {
    return []
  }
  const hopeOcc: any[] = []
  hopeOcc.push({
    industry: occV2[0]?.industry || -1,
    occIds: occV2[0]?.occIds?.slice(0, 1) || [],
  })

  return hopeOcc
}
