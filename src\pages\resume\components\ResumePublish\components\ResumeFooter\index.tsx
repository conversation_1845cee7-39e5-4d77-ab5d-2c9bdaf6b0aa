import style from './index.module.scss'
import { MOBILE_TERMINAL_URL } from '@/core/config/index'
import Click from '@/components/Click'

type IFooterProps = {
  publishRuleSwitch?: boolean
  btnTxt?: string
  isDel?: boolean
  onConfirm?: () => void
  onDelete?: () => void
  id?: string
  isPdBtm?: boolean
}

// 跳转到鱼泡网找活信息发布规则
const onRule = () => {
  $.router.push(`/subpackage/web-view/index?url=${MOBILE_TERMINAL_URL}index/otherzhaohuoruler/`)
}
export default function Footer(props: IFooterProps) {
  const { publishRuleSwitch = false, isDel = false, isPdBtm = false, btnTxt = '保存', onConfirm, onDelete, id = '' } = props

  return (
    <V id={id} className={`${style.fotter} ${isPdBtm ? style.pdBtm : ''}`}>
      <V className={style.btnOut}>
        {
          isDel && (
            <Click className={style.delBtn} onClick={onDelete}>
              <T>删除</T>
            </Click>
          )
        }
        <Click onClick={onConfirm} className={style.btn}>
          <T>{btnTxt}</T>
        </Click>
      </V>
      {
        publishRuleSwitch && (
          <V className={style.ruleTips}>
            <T>发布简历即表示遵守</T><T className={style.rule} onClick={onRule}>《鱼泡直聘简历发布规则》</T>
          </V>
        )
      }
    </V >
  )
}
