import React from 'react'
import { ScrollView, View, RichText } from '@tarojs/components'
import styles from './index.module.scss'

export interface LabelItem {
  aliasName: string; // 标签名称
  count?: number | string; // 相关信息条数
  label: string;
}

interface SearchLabelProps {
  list: LabelItem[]; // 标签列表
  top: string; // 距离顶部高度
  keywords?: string; // 当前关键字（可选）
  onClick: (aliasName: string) => void; // 点击标签回调
}

const SearchLabel: React.FC<SearchLabelProps> = ({ list, top, onClick }) => {
  const handleClick = (item: LabelItem, index: number) => {
    onClick(item.label)
  }

  return (
    <ScrollView
      className={styles.scrollView}
      scrollY
      style={{ height: `calc(100vh - ${top})`, top }}
    >
      {list.map((item, index) => (
        <View
          key={index}
          className={styles.aliasName}
          onClick={() => handleClick(item, index)}
        >
          {/* 标签内容 */}
          <View className={styles.text}>
            <RichText nodes={item.aliasName} />
          </View>

          {/* 条数信息 */}
          {item.count && item.count !== '0' && (
            <View className={styles.cont}>约{item.count}条好活信息</View>
          )}
        </View>
      ))}
    </ScrollView>
  )
}

export default SearchLabel
