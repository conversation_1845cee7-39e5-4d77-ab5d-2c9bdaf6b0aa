/*
 * @Date: 2024-01-11 15:53:47
 * @Description: 职位选择器数据
 */

import { createSlice } from '@reduxjs/toolkit'
import { processOcc, treeQuery, unique } from './utils'

const { reducer, actions, name } = createSlice({
  name: 'classify',
  initialState: {
    // 职位原始数据
    occupations: [],
    // 行业id树数组
    industryTree: [],
    // 行业ID对应的一级职位ID
    indOccIdOne: {},
    // 一级职位ID对应的二级职位ID
    occOneIdTwo: {} as any,
    // 职位ID对应的对象
    occupationsMap: {},
    // 职位行业+ID对应的对象
    industryOccMap: {} as any,
    // 记录职位接口已经获取了数据
    requestData: false,
    classifyConfig: {
      /** 主站列表工种筛选器可选最大工种数量 */
      projectSearchOccCnt: 3,
      /** 主站发布/修改[招工/找活]可选最大工种数量 */
      projectPublishOccCnt: 3,
      /** 工厂专区列表/发布/修改[招工|找活]可选择的最大工种数量 */
      factoryOccCnt: 3,
      /**  找活搜索 */
      findJobSearchOccCnt: 5,
      /** 招工搜索，找活发布工种数 */
      recruitWorkerSearchFindJobPublishOccCnt: 5,
    },
  },
  reducers: {
    setState(state, { payload }) {
      Object.assign(state, payload)
    },
  },
})

const getClassifyConfig = () => async (dispatch, getState) => {
  const { classify } = getState()
  const res = await $.request['POST/occupation/v1/getConfig']()
  if (res.code === 0) {
    dispatch(actions.setState({
      classifyConfig: {
        ...classify.classifyConfig,
        ...res.data,
      },
    }))
  }
}

const getClassTreeData = (payload: boolean = false) => async (dispatch, getState) => {
  const { classify } = getState()
  const { occupations } = classify || {}

  if (!$.isArrayVal(occupations) || payload) {
    try {
      const [, res] = await $.request['POST/occupation/v3/treeQuery']()
      const { data: tdata } = res || {}
      if (tdata) {
        const { occupations: nOcc } = tdata
        // 组装后的工种
        const newClassWorkData = JSON.parse(JSON.stringify(nOcc))
        const nAllData: any = { occupations: [] }
        nAllData.occupations = newClassWorkData
        await dispatch(handleOccupations(newClassWorkData))
        dispatch(actions.setState({ ...nAllData, requestData: true }))
        return nAllData
      }
    } catch (error) {
      console.log('error:', error)
      dispatch(actions.setState({ requestData: true }))
    }
  }
  return {}
}

// 组装职位数据
export const handleOccupations = (occupations) => async (dispatch) => {
  if (!$.isArrayVal(occupations)) return
  const data: any = {
    industryTree: [], indOccIdOne: {}, occOneIdTwo: {}, occupationsMap: {}, industryOccMap: {},
  }
  occupations.forEach((occ) => {
    data.occ = occ
    processOcc(data)
  })
  delete data.occ
  dispatch(actions.setState(data))
}

const extendActions = {
  getClassTreeData,
  getClassifyConfig,
}
export default {
  name,
  reducer,
  actions: { ...actions, ...extendActions },
}
