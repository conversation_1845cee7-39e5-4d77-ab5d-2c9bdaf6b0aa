/*
 * @Author: jiang<PERSON>
 * @LastEditors: jianglong
 * @Description: 找活求职期望：处理数据子名片
 */

import { MyResumeDetails } from '@/store/storage/defData'

// import { getTempShow } from '@/utils/helper/resume/utils'
type IIndexFormat = {
  /** 全职 */
  jobFull: Array<MyResumeDetails['basicResp']['subs'][number] & { occCtrlsStr: string }>,
  /** 兼职 */
  jobPart: Array<MyResumeDetails['basicResp']['subs'][number] & {occCtrlsStr: string}>,
}

/** 子名片清洗 */
function indexFormat(subs: MyResumeDetails['basicResp']['subs']): IIndexFormat {
  const subJob = { jobFull: [] as any[], jobPart: [] as any[] }
  const nSubs = $.isArrayVal(subs) ? subs : []
  nSubs.forEach((item) => {
    const { occName } = item.occupationInfo
    const occCtrlsStr = item.occCtrls.map((ctrl) => ctrl.controlValueList).flat().join('、')
    const nItem: any = { ...item, occCtrlsStr, occName }
    if (item.positionType == 1) { // 全职
      subJob.jobFull.push(nItem)
    } else { // 兼职
      subJob.jobPart.push(nItem)
    }
  })
  return subJob
}

export default indexFormat
