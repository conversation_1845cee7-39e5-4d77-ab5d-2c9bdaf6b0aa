import { actions, dispatch, store } from '../store/index'
import type { IClassifyDataRq, IClassifyDataRp } from './index.d'

/**
 * @description 跳转职位选择器
 * @param {IClassifyDataRq} params - 职位选择器参数
 * @param {function(Array<IClassifyDataRp>):void} event - 回调事件
 * */
export const openClassify = (params: IClassifyDataRq, event: (data: Array<IClassifyDataRp>) => void) => {
  $.router.push('/subpackage/classify/index', {}, params, event)
}

export const sourceMap = {
  1: '订阅好活',
  2: '找活列表',
  3: '编辑招工',
  4: '发布招工',
  5: '招工搜索结果页',
  6: '编辑找活名片',
  7: '新用户找活意向',
  8: '发布找活',
  9: '招工列表',
  10: '找活搜索结果页',
  11: '全部职位',
} as const

/**
 * @description 根据ids获取工种对象
 * */
export const getClassifyByIds = async (ids: string[]) => {
  let { occupationsMap } = store.getState().classify
  if ($.isEmptyObject(occupationsMap)) {
    const data = await dispatch(actions.classify.getClassTreeData())
    const { occupationsMap: nOccMap } = data || {}
    if ($.isObjVal(nOccMap)) {
      occupationsMap = nOccMap
    }
  }
  let classify: Array<any> = []
  if (!$.isArrayVal(ids)) {
    return classify
  }
  classify = ids.map(id => occupationsMap[id]).filter(item => item && item.id)
  return classify
}

/**
 * @description 根据ids获取工种对象(包含行业,格式[{industrys:[string],id:string,name:string,mode:string}])
 * */
export const getClassifyOfIndByIds = async (ids: string[]): Promise<Array<IClassifyDataRp>> => {
  let { industryOccMap, industryTree } = store.getState().classify
  if ($.isEmptyObject(industryOccMap)) {
    const data = await dispatch(actions.classify.getClassTreeData())
    const { industryOccMap: nOccMap, industryTree: nIndTree } = data || {}
    if ($.isArrayVal(nIndTree)) {
      industryOccMap = nOccMap
      industryTree = nIndTree
    }
  }
  const classify: Array<any> = []
  if (!$.isArrayVal(ids)) {
    return classify
  }
  ids.forEach(occId => {
    industryTree.find(indId => {
      const occ = industryOccMap[`${indId}_${occId}`]
      if (occ) {
        const idx = classify.findIndex(c => c.id == occId)
        if (idx >= 0) {
          classify[idx].industrys.push(indId)
        } else {
          classify.push({ industry: indId, industrys: [indId], id: occId, name: occ.name, mode: occ.mode })
        }
        return indId
      }
      return null
    })
  })
  return classify
}
