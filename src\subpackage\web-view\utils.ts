import { store } from '@/core/store'
import { getSystemInfoSync } from '@/core/request'
import * as coreConfig from '@/core/config'

/**
 * 获取 H5 跳转所需的 Header 信息和 Token
 * @param {Object} params 参数配置
 * @param {boolean} params.isHeader 是否需要 header 信息
 * @param {boolean} params.isSession 是否需要 session (token)
 * @param {boolean} params.isBottom 是否需要底部安全区高度
 * @param {boolean} params.isUid 是否需要用户 ID
 * @returns {Promise<string>} 拼接后的参数字符串
 */
export const getH5OfHeader = async ({
  isHeader,
  isSession = true,
  isBottom,
  isUid = true,
}: {
  isHeader: boolean
  isSession?: boolean
  isBottom?: boolean
  isUid?: boolean
}): Promise<string> => {
  const { token, userInfo } = store.getState().storage as any // 用户登录状态

  const { system, version: systemVersion, safeArea, windowHeight } = getSystemInfoSync()

  const iosSABottom = safeArea?.bottom ? Number(windowHeight - (safeArea?.bottom || 0)) : 0

  const headers = {
    os: system,
    business: 'YPZP',
    runtime: coreConfig.runtime,
    packagename: coreConfig.appid,
    osversion: systemVersion,
    runtimeversion: systemVersion,
    packageversion: coreConfig.requestVersion,
    systemversion: systemVersion,
    appversion: coreConfig.requestVersion,
    minipackage: coreConfig.miniToken,
    system,
    platform: coreConfig.runtime.toLocaleLowerCase(),
  }

  let srcStr = ''

  if (isHeader) {
    srcStr += `headers=${encodeURIComponent(JSON.stringify(headers))}&`
  }
  if (isSession) {
    srcStr += `session=${token || ''}&`
  }
  if (isBottom) {
    srcStr += `bottom=${iosSABottom}&`
  }
  if (isUid) {
    srcStr += `uid=${userInfo?.userId || ''}&`
  }

  // 移除末尾多余的 "&"
  return srcStr ? srcStr.slice(0, -1) : ''
}
