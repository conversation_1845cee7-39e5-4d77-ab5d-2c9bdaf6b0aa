/*
 * @Date: 2024-11-23 11:34:17
 * @Description: 获取经纬度 https://opendocs.alipay.com/mini/api/mkxuqd?pathHash=d0e13e67
 */

import { useEffect } from 'react'
import { actions, dispatch, store, useSelector } from '@/core/store'
import { getAreaByAdcode, getLocation, getLocationByApi } from '@/utils/location'
import useRefValue from '../useRefValue'

/**
 * @name 判断首页是否走城市定位
 * 首次定位时，如果路由参数携带listType = 3时，走附近定位，此时阻止首页定位方法
 *  */
let JudgeLandingListType3 = false

export default function useLocation(callback?: Function) {
  const homeLocation = useSelector((state) => state.storage.homeLocation)
  const fn = useRefValue(callback)

  useEffect(() => {
    /** 首次定位时，如果路由参数携带listType = 3时，走附近定位，此时阻止首页定位方法 */
    if (!JudgeLandingListType3 && $.router.query.listType == 3 && $.router.path == 'pages/index/index') {
      JudgeLandingListType3 = true
      return
    }
    if (homeLocation.success && homeLocation.data.latitude && homeLocation.data.longitude) {
      return
    }
    $.taro.getLocation({
      type: 'gcj02', // 代表获取经纬度
      success: async (res) => {
        const { addr } = await getLocationByApi(res.longitude, res.latitude)
        const { province, city, district, current } = await (await getAreaByAdcode(addr.adcode)) || {}
        const value = {
          success: true,
          data: {
            longitude: res.longitude,
            latitude: res.latitude,
            areaId: current.id,
            name: current.name,
          },
        }
        if (homeLocation.data.areaId !== value.data.areaId) {
          fn.current && fn.current(value)
        }

        const myCurrent = current || {}
        dispatch(actions.storage.setItems({
          homeLocation: value,
          userLocation: {
            success: true,
            province,
            city,
            district,
            ...myCurrent,
            longitude: res.longitude,
            latitude: res.latitude,
          } as any,
        }))
      },
      fail: (e) => {
        if (!store.getState().storage.homeLocation?.data?.areaId) {
          const value = {
            success: false,
            data: {
              areaId: 1,
              name: '全国',
              latitude: null,
              longitude: null,
            },
          }
          dispatch(actions.storage.setItem({ key: 'homeLocation', value }))
        }
      },
    } as any)
  }, [])

  return homeLocation
}

/** 就业小程序跳转到详情，不请求定位 */
export function useSkipLocation(skip) {
  const userLocation = useSelector((state) => state.storage.userLocation)

  useEffect(() => {
    if (skip) return
    /** 首次定位时，如果路由参数携带listType = 3时，走附近定位，此时阻止首页定位方法 */
    if (!JudgeLandingListType3 && $.router.query.listType == 3 && $.router.path == 'pages/index/index') {
      JudgeLandingListType3 = true
      return
    }
    if (userLocation.success && userLocation.latitude && userLocation.longitude) {
      return
    }
    getLocation()
  }, [skip])

  return store.getState().storage.userLocation
}
