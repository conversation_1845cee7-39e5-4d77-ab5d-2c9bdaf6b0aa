/*
 * @Author: jianglong
 * @description: 找活工具库
 */

import { workStatusObj } from './data'
import type {
  IFmtTemplateTextOptions,
  IClickPointExp,
} from './index.d'

import { fetchResumeExist } from '@/utils/helper/resume/index'

export const getWorkStatusName = (status: number) => workStatusObj[status] || workStatusObj['1']

/**
 * 格式化积分文案
 * @param template.text 格式化文案
 * @param template.integral 格式化积分
 * @param template.color 颜色 默认 #0092FF
 * @param options.replaceStr 替换的文案 默认 '{integral}'
 * @param options.splitIdentity 默认 '__yp__mini__'
 */
export function getFmtTemplateText(template: any, options?: Partial<IFmtTemplateTextOptions>) {
  try {
    const canFmtTemplateIntegral = template?.text || template?.integral
    if (!canFmtTemplateIntegral) {
      return []
    }

    const { replaceStr = '{integral}', splitIdentity = '__yp__mini__' } = options || {}
    // 积分不足、还需消耗 {} 积分才能购买刷新包
    if (!replaceStr) {
      return []
    }

    const RE_TEXT = new RegExp(replaceStr, 'g')
    const fmtStr = `${splitIdentity}${replaceStr}${splitIdentity}`
    const newText = String(template.text).replace(RE_TEXT, fmtStr)

    const integral = template.integral || 0
    const color = template?.color || '#0092FF'
    const mapItem = text => (text === replaceStr ? ({ text: integral, color }) : ({ text }))
    return newText.split(splitIdentity).map(mapItem)
  } catch (error) {
    console.error(error)
    return []
  }
}

/** 判断处理找活名片是否存在 */
export const resumeExistsHandler = async () => {
  const resData = await fetchResumeExist()
  if (!resData || !resData.exist) { // 找活名片不存在
    resumeNotExistsPop()
    return false
  }
  return true
}

/** 找活名片不存在的弹框
 * @param isCustom 是否是自定义事件
 */
export function resumeNotExistsPop(isCustom = false) {
  return $.confirm({
    cancelText: '知道了',
    confirmText: '去发布',
    content: '您的简历不存在，发布简历获得老板主动联系！',
  }).then((res) => {
    if (!isCustom) {
      // 跳转到发布名片页面
      $.router.replace(
        '/subpackage/resume-publish/index?origin=complete&optype=back&resume=1',
      )
    }
    return Promise.resolve(res)
  }).catch((err) => {
    if (!isCustom) {
      // 返回上一页
      $.router.back()
    }
    return Promise.reject(err)
  })
}

/** 发布找活使用-保存完善信息接口 */
export function fetchPerSave(params) {
  return $.request['POST/resume/v2/perfect/perfectInfo'](params)
}
