import { Input, Text, View } from '@tarojs/components'
import style from './index.module.scss'

type IResumeTemplatesV4Props = {
  control?: any
  mustKey?: string
  change?: (e: any) => void
}
const ResumeTemplatesV4 = (props: IResumeTemplatesV4Props) => {
  const { control = {}, change, mustKey = '' } = props
  const { controlName, controlAttr, inputValue, controlTypeCode, controlCode } = control || {}
  const { dataObj } = controlAttr || {}
  console.log('controlCode:', controlCode)

  const getType = (code, obj): any => {
    let type = 'text'
    const { precision, max } = obj || {}
    if (code == 'INPUT_NUMBER' || (code == 'INPUT_WITH_UNIT' && max)) {
      type = precision < 1 ? 'number' : 'digit'
    }
    return type
  }

  const getInput = () => {
    const type = getType(controlTypeCode, dataObj)
    let maxlength = 140
    if (type != 'text') {
      maxlength = dataObj.max ? dataObj.max : 4
    }
    return <Input
      value={inputValue}
      placeholder={dataObj.tips}
      type={type}
      maxlength={maxlength}
      placeholderClass={style.plac}
      className={`${style.txt} ${style.inp}`}
      onInput={onInputChange}
    />
  }

  const onInputChange = (e) => {
    const value = e.detail.value ? `${e.detail.value}` : ''
    const type = getType(controlTypeCode, dataObj)
    const digitIndex = value.indexOf('.')
    const nControl = { ...control }
    switch (`${type}`) {
    case 'number': // 输入框
      nControl.inputValue = value ? `${Number(value) || ''}` : value
      break
    case 'digit': // 带小数点的输入框
      if (digitIndex == 0) {
        // 如果第一位是小数点
        nControl.inputValue = '0.'
      } else if (Number.isNaN(Number(value))) {
        // 如果输入的不是数字,去掉最后一位
        nControl.inputValue = value.slice(0, -1)
      } else if (digitIndex == -1 && value) {
        // 如果有值但是没有小数点
        nControl.inputValue = Number(value)
      } else {
        nControl.inputValue = value
      }
      break
    default:
      nControl.inputValue = value
    }
    change && change(nControl)
  }
  return (
    <View className={style.item}>
      {control[mustKey] && <Text className={style.must}>*</Text>}
      <View className={`${style.txt} ${style.label}`}><Text>{controlName}</Text></View>
      {
        getInput()
      }
      {
        controlTypeCode == 'INPUT_WITH_UNIT' && dataObj.unit && (
          <View className={style.txt}><Text>{dataObj.unit}</Text></View>
        )
      }
    </View>
  )
}

export default ResumeTemplatesV4
