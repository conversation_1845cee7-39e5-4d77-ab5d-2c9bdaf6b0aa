/*
 * @Date: 2024-11-21 09:38:15
 * @Description: 基础页面组件
 */

import React from 'react'
import { View as V } from '@tarojs/components'
import Modal from '@/components/Modal'

type Props = {
  children: React.ReactNode
  backgroundColor?: string
}

export default (props: Props) => {
  const { children, backgroundColor = '#F5F6FA' } = props
  return (
    <V style={{ backgroundColor, minHeight: '100vh' }}>
      {children}
      <Modal />
    </V>
  )
}
