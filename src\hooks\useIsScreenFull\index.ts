import Taro, { useResize } from '@tarojs/taro'
import { useEffect, useState } from 'react'
import { actions, dispatch, useSelector } from '@/core/store'

export default () => {
  const globalScreenRatio = useSelector(state => state.global)
  const [isScreenFull, setIsScreenFull] = useState<Boolean>(globalScreenRatio.isScreenFull)
  useResize((res) => {
    setIsScreenFull(res.screenRatio == 1)
    dispatch(actions.global.setState({ isScreenFull: res.screenRatio == 1 }))
  })
  useEffect(() => {
    setIsScreenFull(screenState)
  })
  const screenState = () => {
    const systemInfo = Taro.getSystemInfoSync()

    // 获取窗口高度
    const { windowHeight } = systemInfo
    // 获取屏幕高度
    const { screenHeight } = systemInfo
    // 判断是否全屏
    const is = Math.abs(windowHeight - screenHeight) < 150
    dispatch(actions.global.setState({ isScreenFull: is }))
    return is
  }
  return [isScreenFull]
}
