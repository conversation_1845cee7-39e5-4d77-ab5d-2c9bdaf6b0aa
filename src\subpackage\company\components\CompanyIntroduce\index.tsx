import { Fragment } from 'react'
import { Text as T, View as V } from '@tarojs/components'
import * as cn from 'classnames'
import useReducer from '@/hooks/useReducer'
import styles from './index.module.scss'
import { IntroduceList } from '../../formatEnterpriseMainView'
import Tag from '../Tag'
import CompanyIntroducePopup from '../CompanyIntroducePopup'

type IProps = {
  data: IntroduceList
}

type InitState = {
  chooseTag: IntroduceList[0]['type']
  showPopup: number
  // 新增截断后的文本存储
  truncatedTexts: Partial<Record<IntroduceList[0]['type'], string>>
}

// 新增安全截断函数
function safeTruncate(text: string, maxChars: number): string {
  return Array.from(text).slice(0, maxChars).join('') + (Array.from(text).length > maxChars ? '...' : '')
}

export default (props: IProps) => {
  const { data } = props

  const [{ chooseTag, showPopup, truncatedTexts }, dispatch] = useReducer<InitState>({
    chooseTag: data[0]?.type,
    showPopup: -1,
    truncatedTexts: data.reduce((acc, item) => ({
      ...acc,
      [item.type]: safeTruncate(item.content || '', 58),
    }), {}),
  })

  // 修改后的显示更多处理
  const onShowMore = () => {
    const index = data.findIndex(it => it.type === chooseTag)
    dispatch({
      showPopup: index,
    })
  }
  // tag 的切换。
  const tagChange = async (b: boolean, type: IntroduceList[0]['type']) => {
    dispatch({ chooseTag: type })
  }

  return (
    <V className={styles.wrap}>
      <T className={styles.title}>公司介绍</T>
      <V className={styles.tagList}>
        {
          data.map(item => (
            <Fragment key={item.type} >
              <Tag className={styles.tag} onChange={(b: boolean) => tagChange(b, item.type)} isClick={chooseTag !== item.type} choose={chooseTag === item.type}>{item.title}</Tag>
            </Fragment>
          ))
        }
      </V>
      <V onClick={onShowMore}>
        <T className={cn(styles.content)}>
          {truncatedTexts[chooseTag]}
        </T>
        {
          (Array.from(data.find(item => item.type === chooseTag)?.content || '').length > 32)
          && <T className={styles.more}>查看更多</T>
        }
      </V>
      <CompanyIntroducePopup data={data} index={showPopup} onClose={() => dispatch({ showPopup: -1 })} />
    </V>
  )
}
