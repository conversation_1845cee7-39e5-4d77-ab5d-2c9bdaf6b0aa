import { useState } from 'react'
import { useLoad } from '@tarojs/taro'
import { IDefValProps } from '@/pages/resume/data'
import ResumePublish from '@/pages/resume/components/ResumePublish'
import { getDefaultData } from '@/pages/resume/utils'
import Page from '@/components/Page'

export default function Index() {
  const { isBack = false } = $.router.data
  const [defVal, setDefVal] = useState<IDefValProps>({ hopeAreas: [], occupations: [], publishRuleSwitch: false })
  useLoad(() => {
    init()
  })

  const init = async () => {
    const data = await getDefaultData()
    const { exist, hopeAreas = [], occupations = [], publishRuleSwitch } = data || { exist: false }
    if (exist) {
      await $.msg('已存在简历')
      $.router.replace('/pages/index/index')
      return
    }
    setDefVal({ occupations, publishRuleSwitch, hopeAreas: (hopeAreas || []).map((area) => `${area.id}`).filter(Boolean) })
  }
  return (
    <Page backgroundColor="#fff">
      <ResumePublish isBack={isBack} value={defVal} isBottom={false} isPdBtm />
    </Page>
  )
}
