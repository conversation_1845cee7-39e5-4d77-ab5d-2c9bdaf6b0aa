import { useState } from 'react'
import { ScrollView, Text as T, View as V } from '@tarojs/components'
import classNames from 'classnames'
import * as cn from 'classnames'
import IconFont from '@/components/IconFont'
import styles from './index.module.scss'
import Popup from '@/components/Popup'

/**
 * 工商信息
*/
export default (props: { data: Record<string, any> }) => {
  const { data } = props

  const [show, setShow] = useState(false)
  const clickAll = () => {
    setShow(true)
  }

  const renderCell = (title: string, value?: string) => {
    if (!value) return null
    return (
      <V className={styles.cell}>
        <T className={styles.leftInner}>{title}</T>
        <T className={classNames(styles.leftInner, styles.rightInner)}>{value}</T>
      </V>
    )
  }

  return (
    <V className={styles.wrap}>
      <V className={styles.titleBox}>
        <T className={styles.title}>工商信息</T>
        <V className={styles.more} onClick={clickAll}>
          查看全部
          <IconFont type='yp-mianbaoxue' size={32} color='#FFFFFFA6' />
        </V>
      </V>
      <V className={styles.inner}>
        {renderCell('公司名称', data.name)}
        {renderCell('法定代表人', data.legalPerson)}
        {renderCell('注册资本', data.registeredCapital)}
        {renderCell('成立日期', data.incorporationDateTime)}
      </V>
      <Popup visible={show} onClose={() => setShow(false)} catchMove={false} disableScroll={false}>
        <V className={styles.popupWrap}>
          <V className={styles.header}>
            <IconFont type="yp-close-small" onClick={() => setShow(false)} size={48} />
          </V>
          <V className={styles.companyName}>{data.name}</V>
          <ScrollView className={cn(styles.scrollView, styles.safeArea)} scrollY>
            <V className={styles.line}>
              {
                data.legalPerson && (
                  <V className={styles.item}>
                    <V className={styles.pTitle}>法定代表人</V>
                    <V className={styles.pInner}>{data.legalPerson}</V>
                  </V>
                )
              }
              {
                data.incorporationDateTime && (
                  <V className={styles.item}>
                    <V className={styles.pTitle}>成立日期</V>
                    <V className={styles.pInner}>{data.incorporationDateTime}</V>
                  </V>
                )
              }
            </V>
            {
              data.socialCreditCode && (
                <V className={styles.line}>
                  <V className={styles.item}>
                    <V className={styles.pTitle}>统一社会信用代码</V>
                    <V className={styles.pInner}>{data.socialCreditCode}</V>
                  </V>
                </V>
              )
            }
            {
              data.address && (
                <V className={styles.line}>
                  <V className={styles.item}>
                    <V className={styles.pTitle}>注册地址</V>
                    <V className={styles.pInner}>{data.address}</V>
                  </V>
                </V>
              )
            }
            {
              data.businessScope && (
                <V className={styles.line}>
                  <V className={styles.item}>
                    <V className={styles.pTitle}>经营范围</V>
                    <V className={styles.pInner}>{data.businessScope}</V>
                  </V>
                </V>
              )
            }
          </ScrollView>
        </V>
      </Popup>
    </V>
  )
}
