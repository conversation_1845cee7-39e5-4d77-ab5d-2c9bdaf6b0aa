/*
 * @Date: 2024-01-11 15:53:47
 * @Description: 地址数据
 */

import { createSlice } from '@reduxjs/toolkit'

const { reducer, actions, name } = createSlice({
  name: 'address',
  initialState: {
    // 地址id与地址信息的映射
    areaIdMap: {} as {[key: string]: ILocation.TAreaData},
    // 地址adCode与地区的映射
    areaAdCodeMap: {} as { [key: string ]: ILocation.TAreaData },
  },
  reducers: {
    setState(state, { payload }) {
      Object.assign(state, payload)
    },
  },
})

const extendActions = {
  /**
   * 存储地区数据
   */
  setAreaMap: (areaTree: ILocation.TAreaData[]) => (dispatch) => {
    const areaIdMap = {} as {[key: string]: ILocation.TAreaData}
    const areaAdCodeMap = {} as { [key: string ]: ILocation.TAreaData }
    const newAreaTree = $.deepClone(areaTree) as ILocation.TAreaData[]
    function headerArea(areaData: ILocation.TAreaData) {
      const { children, ...item } = areaData
      areaIdMap[item.id] = item
      areaAdCodeMap[item.ad_code] = item
      if (children && $.isArrayVal(children, 1)) {
        children.forEach((item) => {
          headerArea(item)
        })
      }
    }

    newAreaTree.forEach((item) => {
      headerArea(item)
    })

    dispatch(actions.setState({
      areaIdMap: $.deepClone(areaIdMap),
      areaAdCodeMap: $.deepClone(areaAdCodeMap),
    }))

    return {
      areaIdMap,
      areaAdCodeMap,
    }
  },
}
export default {
  name,
  reducer,
  actions: { ...actions, ...extendActions },
}
