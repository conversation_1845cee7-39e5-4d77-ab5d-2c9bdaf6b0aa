/*
 * @Date: 2024-08-08 19:15:19
 * @Description: 全局工具方法
 */

import Taro from '@tarojs/taro'
import dayjs from 'dayjs'
import type { BuryCallAddressParams, IAddressParams, ITypeOf, OpenAddressCB } from './index.d'
import { getAreaById } from '@/utils/location'
import { DIRECT_CITY_IDS } from '@/utils/location/utils'

/** 等待 */
export function wait(time: number) {
  return new Promise((resolve) => setTimeout(resolve, time))
}

/** 生成随机数 */
export function getRandomNumber(min: number, max: number) {
  // eslint-disable-next-line sonarjs/pseudo-random
  return Math.floor(Math.random() * (max - min + 1)) + min
}

/** 判断两个对象是否相等 */
export function isEqual(obj1, obj2) {
  // 如果两个对象的引用相同，则它们相等
  if (obj1 === obj2) return true

  // 如果两个对象的类型不同，则它们不相等
  if (typeof obj1 !== typeof obj2) return false

  // 如果两个对象的类型是对象或数组，则需要进一步比较
  if (typeof obj1 === 'object' && obj1 !== null && obj2 !== null) {
    // 获取两个对象的键
    const keys1 = Object.keys(obj1)
    const keys2 = Object.keys(obj2)

    // 如果两个对象的键的数量不同，则它们不相等
    if (keys1.length !== keys2.length) return false

    // 检查每个键的值是否相等
    // eslint-disable-next-line no-restricted-syntax
    for (const key of keys1) {
      if (!isEqual(obj1[key], obj2[key])) return false
    }

    // 所有键的值都相等，则两个对象相等
    return true
  }

  // 对于其他类型（字符串、数字、布尔值等），直接比较值是否相等
  return obj1 === obj2
}

/** 判断数组是否有值 */
export function isArrayVal(arr, len = 1) {
  return !!(Array.isArray(arr) && arr.length >= len)
}

/** 判断对象是否有值 */
export function isObjVal(obj, len = 1) {
  if (typeOf(obj) !== 'object') {
    return false
  }
  return Object.keys(obj).length >= len
}

/** 判断空对象 */
export function isEmptyObject(obj) {
  return !obj ? true : Object.getOwnPropertyNames(obj).length == 0
}

/** 提示toast */
export function msg(title, time = 1500, mask = false) {
  return new Promise((resolve) => {
    Taro.showToast({
      title,
      mask,
      icon: 'none',
      duration: time || 1500,
    })
    setTimeout(resolve, time)
  })
}

/** 加载loading */
export function showLoading(title) {
  return new Promise((resolve) => {
    Taro.showLoading({
      title,
      mask: true,
      complete: resolve,
    })
  })
}

/** 隐藏loading */
export function hideLoading() {
  return new Promise((resolve) => {
    Taro.hideLoading({
      complete: resolve,
    })
  })
}

/** 处理接口请求中的 null 和 undefined */
export const delNullOp = (obj) => {
  if (!obj || typeof obj !== 'object') return obj
  return Array.from(Object.entries(obj)).filter(
    ([_, value]) => value !== null && value !== undefined,
  ).reduce((so, [keyBy, value]) => {
    so[keyBy] = delNullOp(value)
    return so
  }, ((Array.isArray(obj) ? [] : {}) as any))
}

/** 深拷贝，不能处理日期和函数 */
export function deepClone<T>(any: T): T | any {
  if (any === null) {
    return null
  }
  if (typeof any !== 'object') {
    return any
  }
  const obj: any = typeOf(any) === 'array' ? [] : {}
  // eslint-disable-next-line
  for (const key in any) {
    if (typeof any[key] === 'object') {
      obj[key] = deepClone(any[key])
    } else {
      obj[key] = any[key]
    }
  }
  return obj
}

const firstLowerCase = (str: string) => {
  return str.replace(/^\S/, (s) => s.toLowerCase())
}

/** 判断值类型 */
export function typeOf(val: any): ITypeOf {
  const typeStr: ITypeOf = typeof val
  if (typeStr === 'object') {
    let call = Object.prototype.toString.call(val) as ITypeOf
    call = call.substring(8, call.length - 1) as ITypeOf
    call = firstLowerCase(call) as ITypeOf
    return call
  }
  return typeStr
}

// 定义 sysInfo 的返回值类型
interface SysInfo {
  headerTop: number;
  headerHeight: number;
  menuPadding: number;
  statusBarHeight: number;
  menuRect: Taro.getMenuButtonBoundingClientRect.Rect;
  systemInfo: Taro.getSystemInfoAsync.Result;
  menuRectLeft: number;
  leftBackRect: null | {
    /**
     * 返回按钮图标的布局位置信息
     */
    backButtonIcon: my.IMyGetLeftButtonsBoundingClientRectBackButtonIcon;
    /**
     * 返回按钮点击响应区域的布局位置信息
     */
    backButtonInteractive: my.IMyGetLeftButtonsBoundingClientRectBackButtonIcon;
    /**
     * 首页按钮图标的布局位置信息
     */
    homeButtonIcon: my.IMyGetLeftButtonsBoundingClientRectBackButtonIcon;
  };
  leftBackRectWidth: null | number;
}

/** 获取通用系统信息 */
export const sysInfo = (() => {
  let value: SysInfo

  return (): SysInfo => {
    if (value) {
      return value
    }

    /** 获取菜单按钮（右上角胶囊按钮）的布局位置信息 */
    const menuRect = Taro.getMenuButtonBoundingClientRect()
    const leftBackRect = process.env.TARO_ENV === 'alipay' ? my.getLeftButtonsBoundingClientRect() : null
    /** 获取系统信息 */
    const systemInfo = Taro.getSystemInfoSync()

    if (systemInfo && systemInfo.platform) {
      systemInfo.platform = systemInfo.platform.toLocaleLowerCase()
    }
    /** 胶囊距离顶部的距离 */
    const menuPadding = menuRect.top > (systemInfo.statusBarHeight || 0) ? menuRect.top - (systemInfo.statusBarHeight || 0) : menuRect.top
    // /** 胶囊的高度和padding */
    const headerHeight = menuRect.height + menuPadding * 2

    value = {
      /** 顶部距离 */
      headerTop: systemInfo.brand === 'devtools' ? 104 : headerHeight + (systemInfo.statusBarHeight || 0),
      headerHeight, // 胶囊高度
      menuPadding,
      statusBarHeight: systemInfo.statusBarHeight || 0,
      /** 胶囊 */
      menuRect: {
        ...menuRect,
        width: menuRect.width >= 120 ? 120 : menuRect.width,
      }, // 胶囊相关信息
      systemInfo: systemInfo as Taro.getSystemInfoAsync.Result, // 系统信息
      // 胶囊距离左边的距离 真机上有收藏按钮，所以需要判断一下
      menuRectLeft: (menuRect as any).optionMenuLeft || menuRect.left || 0,
      leftBackRect,
      // 返回按钮或返回+主页按钮占左边的宽度
      leftBackRectWidth: leftBackRect ? (leftBackRect.homeButtonIcon?.right || leftBackRect.backButtonInteractive?.right || 0) : null,
    }

    return value
  }
})()

// export function openAddress(params: IAddressParams, buriedParams: BuryCallAddressParams, cb: OpenAddressCB): void;
/** 打开城市选择页 */
export function openAddress(params: IAddressParams, buriedParams?: BuryCallAddressParams | OpenAddressCB, cb?: OpenAddressCB) {
  const callback = typeof buriedParams === 'function' ? buriedParams : cb

  if (buriedParams && typeof buriedParams === 'object') {
    $.report.event('city_filter_click', {
      ...buriedParams,
    })
  }

  const newOptions = {
    maxNum: 1,
    level: 3,
    areas: [],
    disabledIds: [],
    hideNation: false,
    showLocation: false,
    title: '请选择求职区域',
    ...params,
  } as IAddressParams
  $.router.push(
    '/subpackage/map/address/index',
    {},
    {
      ...newOptions,
    },
    (data) => {
      Promise.all(data.map(item => getAreaById(item.id))).then((areas) => {
        console.log(areas)
        const buriedCities = areas.reduce((cities, item) => {
          cities.select_province.push(item.province?.name)
          cities.select_city.push(item.city?.name)
          if (item.city.id && item.current.id && item.city.id !== item.current.id) {
            cities.select_area.push(item.current?.name)
            cities.area_id.push(item.current?.id)
          }
          return cities
        }, { select_province: [], select_city: [], select_area: [], area_id: [] })

        $.report.event('addressSelection', {
          source: buriedParams?.source ? buriedParams?.source : '-',
          source_id: buriedParams?.source_id ? buriedParams?.source_id : '-99999',
          select_province: uniqueArray(buriedCities.select_province.filter(item => !!item)).join(',') || '',
          select_city: uniqueArray(buriedCities.select_city.filter(item => !!item)).join(',') || '',
          select_area: buriedCities.select_area.filter(item => !!item).join(',') || '',
          area_id: data.map(item => item.id).join(',') || '',
        })
      }).catch(console.error)
      callback && callback(data)
    },
  )
}

/** js数组去重 */
export function uniqueArray(arr) {
  return arr.filter((item, index) => arr.indexOf(item) === index)
}

/**
 * @name: allChinese for jsxin
 * @params: str: string 需要被验证的字符串
 * @return: boolean
 * @description: 当前字符串是否是2-5个全中文
 */
export const allChinese = (str: string): boolean => {
  // eslint-disable-next-line prefer-regex-literals
  const reg = new RegExp('^[\u4E00-\u9FA5]{2,5}$')
  return reg.test(str)
}

/** 生成uuid */
export function generateUUID() {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, (char) => {
    // eslint-disable-next-line
    const random = (Math.random() * 16) | 0 // 生成随机数
    // eslint-disable-next-line
    const value = char === 'x' ? random : (random & 0x3) | 0x8 // 确保 'y' 的位值正确
    return value.toString(16) // 转换为16进制
  })
}

/** 节流函数 */
export function throttle(func, delay = 150) {
  let lastTime = 0
  return function throttledFunction(...args) {
    const now = new Date().getTime()
    if (now - lastTime >= delay) {
      func.apply(this, args)
      lastTime = now
    }
  }
}

/**
 * 防抖函数
 * @param func 需要进行防抖处理的函数
 * @param delay 防抖延迟时间，单位为毫秒
 * @returns 经过防抖处理后的函数
 */
export function debounce(func, delay = 300) {
  let timer: NodeJS.Timeout | null = null
  return function debouncedFunction(...args) {
    if (timer) {
      clearTimeout(timer)
    }
    timer = setTimeout(() => {
      func.apply(this, args)
      timer = null
    }, delay)
  }
}

/** @description 处理ts那对象的值频繁使用?.的问题
 * @param obj 对象
 * @param path 路径
 * @param defVal 默认值
 */
export function getObjVal(obj: any, path: string, defVal: any = undefined) {
  const paths = `${path}`.split('.')
  let val = obj
  for (let index = 0; index < paths.length; index += 1) {
    const key = paths[index]
    if (val == null) {
      return defVal
    }
    val = val[key]
  }
  return val == null ? defVal : val
}
/** px转rpx */
export function pxToRpx(px) {
  const screenWidth = sysInfo().systemInfo.windowWidth // 获取屏幕宽度
  return px * (750 / screenWidth)
}

export function rpxToPx(rpx) {
  const screenWidth = sysInfo().systemInfo.windowWidth // 获取屏幕宽度
  return rpx * (screenWidth / 750) // rpx 转 px
}

/**
 * @return: boolean
 * @description: 验证内容 传入的val数字是否在指定的区间内，包含最大最小区间
 */
export const isVaildNum = (val: number | string, min: number, max: number): boolean => {
  // 如果不是数字
  if (Number.isNaN(Number(val))) {
    return false
  }
  return val >= min && val <= max
}

/** 判断两个对象的值是否一样 */
export function isEqualObj(obj1: any, obj2: any): boolean {
  const { sortStr: sortStr1 } = sortObject(obj1 || {})
  const { sortStr: sortStr2 } = sortObject(obj2 || {})
  return JSON.stringify(sortStr1) === JSON.stringify(sortStr2)
}

/** json对象排序 */
export function sortObject(obj: any): { sortObj: any, sortStr: string } {
  if (!obj || typeof obj !== 'object') {
    return obj
  }
  const newObj = $.deepClone(obj)
  function handler(obj: any): any {
    if (Array.isArray(obj)) { // 如果是数组，对数组元素进行递归排序
      obj.sort()
      return obj.map((item: any) => handler(item))
    }
    if (typeof obj === 'object' && obj !== null) {
      // 如果是对象，对对象的属性进行递归排序
      const sortedObject: { [key: string]: any } = {}
      Object.keys(obj)
        .sort()
        .forEach((key: string) => {
          sortedObject[key] = handler(obj[key])
        })
      return sortedObject
    }
    if (typeof obj === 'string' && !Number.isNaN(Number(obj))) {
      // 如果是字符串表示的数字，转换为实际数字类型
      return Number(obj)
    }
    // 如果是基本类型或null，直接返回
    return obj
  }
  const sortObj = handler(newObj)
  const sortStr = JSON.stringify(sortObj)
  return { sortObj, sortStr }
}

/** 判断是时间戳还是日期值 */
export function isTimestampOrDate(value) {
  // 正则匹配 是否value是0-9的数字
  const numberReg = /^\d*$/
  if (numberReg.test(value) && value.length >= 10) {
    return dayjs(value * (value.length === 10 ? 1000 : 1)).format('YYYY-MM-DD HH:mm:ss')
  }
  // 正则匹配 是否value是时间YYYY-MM-DD HH:mm:ss格式
  const timeReg = /^\d{4}-\d{2}-\d{2}\s\d{2}:\d{2}:\d{2}$/
  if (timeReg.test(value)) {
    return value
  }
  return undefined
}

/** 创建promise缓存 */
export function createPromiseCache(fn) {
  let promise
  return (first) => {
    return (...args) => {
      // 如果是第一次
      if (first) {
        promise = fn(...args)
        return promise
      }
      // 如果不是第一次，并且promise之前请求过
      if (promise && !first) {
        const result = promise
        promise = null
        return result
      }
      // 后续情况每次都调用
      return fn(...args)
    }
  }
}

/** 判断是否是iOS设备 */
export function isIos() {
  const { systemInfo } = sysInfo()
  const platform = `${systemInfo.platform}`.toLowerCase()
  return platform === 'ios' || platform === 'iphone'
}

/** 获取元素高度 */
export const getElementHeight = (() => {
  const cache = new Map()
  return (selector) => {
    if (cache.has(selector)) {
      return cache.get(selector)
    }
    const p = new Promise((resolve) => {
      $.taro.createSelectorQuery().select(selector).boundingClientRect().exec((res) => {
        if (res && res[0]) {
          resolve(res[0].height)
        } else {
          resolve(0)
        }
      })
    })
    cache.set(selector, p)
    return p
  }
})()

export const getElementTop = (() => {
  const cache = new Map()
  return (selector) => {
    if (cache.has(selector)) {
      return cache.get(selector)
    }
    const p = new Promise((resolve) => {
      $.taro.createSelectorQuery().select(selector).boundingClientRect().exec((res) => {
        resolve(res[0].top)
      })
    })
    cache.set(selector, p)
    return p
  }
})()
