import { useLoad } from '@tarojs/taro'
import { useEffect, useState } from 'react'
import dayjs from 'dayjs'
import s from './index.module.scss'
import CustomNavbar from '@/components/CustomNavbar'
import Cell from './components/Cell'
import { actions, dispatch, useSelector } from '@/core/store'
import useStates from '@/hooks/useStates'
import { getDataMonth, getWeChatDiy } from './utils'
import NationPicker from './components/NationPicker'
import DatePicker from './components/DatePicker'
import IconFont from '@/components/IconFont'

export default function Index() {
  const {
    userCommonInfo,
    userApplicantInfo,
    userBaseObj,
  } = useSelector((state) => state.user.userData)
  const [visible, setVisible] = useState('')

  const [isEdit, setIsEdit] = useStates({
    sex: true,
    nation: true,
    birthday: true,
  })

  const [dataTime] = useStates({
    startBirthPicker: dayjs().subtract(100, 'year').format('YYYY-MM'), // 出生开始时间范围
    endBirthPicker: dayjs().subtract(16, 'year').format('YYYY-MM'), // 出生结束时间范围
    startDatePicker: dayjs().subtract(100, 'year').format('YYYY-MM'), // 获取开始时间范围
    endWorkPicker: dayjs().format('YYYY-MM'), // 工作结束时间范围
  })

  useLoad(() => {
    dispatch(actions.global.fetchNation())
    $.showLoading('加载中...')
    refresh().then(() => {
      $.hideLoading()
    })
  })

  useEffect(() => {
    setStateFn()
  }, [userCommonInfo, userApplicantInfo, userBaseObj])

  /** 更新数据状态 */
  const setStateFn = () => {
    const { realNameStatus, faceStatus, nation } = userCommonInfo.userRealNameResp || {}
    const idReal = realNameStatus != 2
    setIsEdit({
      ...isEdit,
      sex: idReal,
      birthday: idReal,
      nation: !(realNameStatus == 2 && faceStatus == 2 && nation),
    })
  }

  /** 刷新 */
  const refresh = async () => {
    setVisible('')
    await dispatch(actions.user.fetchUserData())
  }

  /** 修改性别 */
  const onSex = async () => {
    const { realNameStatus } = userCommonInfo.userRealNameResp || {}
    if (realNameStatus == 2) {
      $.msg('已实名，不可修改')
      return
    }
    const data = await $.actionSheet({
      itemList: [
        {
          id: 1,
          name: '男',
          active: userCommonInfo.sex == 1,
        },
        {
          id: 2,
          name: '女',
          active: userCommonInfo.sex == 2,
        },
      ],
    })
    if (data.id == userCommonInfo.sex) {
      return
    }
    $.showLoading('设置中...')
    const [, res] = await $.request['POST/account/v1/userBase/updateSex']({
      sex: data.id,
    }).catch((err) => err)
    $.hideLoading()
    if (res.code == 0) {
      refresh()
    } else {
      $.msg(res.message || '修改失败')
    }
  }

  /** 修改工作时间 */
  const onWork = async (value) => {
    $.showLoading('设置中...')
    const [, res] = await $.request['POST/account/v1/userBase/updateWorkDate']({
      // 提交的格式是 YYYY-MM-DD
      workDate: value,
    }).catch((err) => err)
    $.hideLoading()
    if (!res || res.code != 0) {
      $.msg(res.message || '修改失败')
      return
    }
    refresh()
  }

  /** 修改出生年月 */
  const onBirth = async (value) => {
    $.showLoading('设置中...')
    const [, res] = await $.request['POST/account/v1/userBase/updateBirthday']({
      birthday: value,
    }).catch((err) => err)
    $.hideLoading()
    if (!res || res.code != 0) {
      $.msg(res.message || '修改失败')
      return
    }
    refresh()
  }

  /** 修改姓名 */
  const onSetName = async () => {
    if ((userCommonInfo.userRealNameResp.realNameStatus == 1 || userCommonInfo.userNameResp.nameAuditStatus == 1)) {
      $.msg('审核中，不可修改')
      return
    }
    $.router.push('/subpackage/member/info-editname/index')
  }

  /** 页面跳转 */
  const onJump = (type) => {
    let content = ''
    let contentDiy = ''
    switch (type) {
    case 'infoEmail':
      content = userApplicantInfo.email || ''
      break
    case 'infoWechat':
      contentDiy = getWeChatDiy(userCommonInfo.wechatNumber)
      break
    case 'username':
      return
    }
    $.router.push(
      '/subpackage/member/info-editinput/index',
      {},
      {
        type,
        content,
        contentDiy,
      },
    )
  }

  return (<P>
    <CustomNavbar isHome={false} title='个人信息' />
    <V className={s.body}>
      {/* 头像 */}
      <V className={s.avatar}>
        <V className={s.title}>头像</V>
        <V className={s.avatarCont}>
          <Img className={s.avatarImg} src={userCommonInfo.userHeadPortraitResp.headPortrait} />
          {userCommonInfo.userHeadPortraitResp.headPortraitAuditStatus == 1
            && <V className={s.avatarStatus}>审核中</V>
          }
        </V>
      </V>

      <Cell
        title='姓名'
        value={userCommonInfo.userNameResp.username || ''}
        placeholder='请输入姓名'
        icon={false}
        onClick={onSetName}
        right={<>
          {userCommonInfo.userRealNameResp.realNameStatus == 2
            && <V className={s.realName}>已实名</V>
          }
          {(userCommonInfo.userRealNameResp.realNameStatus == 1 || userCommonInfo.userNameResp.nameAuditStatus == 1)
            ? <V className={s.statusCenter}>审核中</V>
            : <IconFont type='yp-mbxl' size={32} color="rgba(0, 0, 0, 0.25)" />
          }
        </>}
      />
      <Cell
        title='性别'
        value={userCommonInfo.sex == 2 ? '女' : '男'}
        placeholder='请选择性别'
        icon={isEdit.sex && 'yp-mbxl'}
        onClick={() => onSex()}
      />
      {isEdit.nation ? (
        <NationPicker
          value={userCommonInfo.nationId}
          refresh={refresh}
        >
          <Cell
            title='民族'
            value={userCommonInfo.nation}
            placeholder='请选择民族'
            icon='yp-mbxl'
          />
        </NationPicker>
      ) : (
        <Cell
          title='民族'
          value={userCommonInfo.nation}
          placeholder='请选择民族'
          icon={false}
          onClick={() => $.msg('已实名，不可修改')}
        />
      )}
      <Cell
        title='参加工作时间'
        value={getDataMonth(userApplicantInfo.workDate)}
        placeholder='请选择工作时间'
      >
        <DatePicker
          value={getDataMonth(userApplicantInfo.workDate)}
          start={dataTime.startDatePicker}
          end={dataTime.endWorkPicker}
          onChange={onWork}
        />
      </Cell>
      <Cell
        title='手机号'
        value={userCommonInfo.userTelResp.maskTel}
        placeholder='请输入手机号'
        icon={false}
      />
      <Cell
        title='微信号'
        value={getWeChatDiy(userCommonInfo.wechatNumber)}
        onClick={() => onJump('infoWechat')}
        placeholder='请输入微信号'
      />
      <Cell
        title='出生年月'
        value={getDataMonth(userApplicantInfo.birthday)}
        placeholder='请选择出生日期'
        icon={isEdit.birthday && 'yp-mbxl'}
        onClick={() => (isEdit.birthday ? setVisible('birthday') : $.msg('已实名，不可修改'))}
      >
        {isEdit.birthday
          && <DatePicker
            value={getDataMonth(userApplicantInfo.birthday)}
            start={dataTime.startBirthPicker}
            end={dataTime.endBirthPicker}
            onChange={onBirth}
          />
        }
      </Cell>
      <Cell
        title='邮箱'
        value={userApplicantInfo.email}
        onClick={() => onJump('infoEmail')}
        placeholder='请输入邮箱（选填）'
      />
      <Cell
        title="注册时间"
        value={userBaseObj.userDateObj.registerDate}
        icon={false}
      />
    </V>
  </P>)
}
