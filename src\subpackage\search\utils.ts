import { store, actions, dispatch } from '@/core/store'

/** 保存历史搜索记录 */
export const saveSearchHistory = async (
  key: 'recruit' | 'resume',
  keyword: string,
) => {
  const value = $.deepClone(store.getState().storage.searchHistory)
  value[key].unshift(keyword)

  // 去重：移除后续重复项
  const index = value[key].lastIndexOf(keyword)
  if (index > 0) {
    value[key].splice(index, 1)
  }

  // 限制最多保存 20 条历史记录
  if (value[key].length > 20) {
    value[key].pop()
  }

  // 更新 store
  dispatch(actions.storage.setItem({ key: 'searchHistory', value }))
}

/** 清空历史搜索记录 */
export const clearSearchHistory = async (key: 'recruit' | 'resume') => {
  const value = $.deepClone(store.getState().storage.searchHistory)
  value[key] = []
  dispatch(actions.storage.setItem({ key: 'searchHistory', value }))
}
