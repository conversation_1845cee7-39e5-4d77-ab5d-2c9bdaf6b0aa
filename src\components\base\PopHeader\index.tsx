/*
 * @Date: 2024-11-20 14:04:18
  * @Description: 弹框头部
 */

import cn from 'classnames'
import { Button, StandardProps as sp } from '@tarojs/components'
import { useEffect, useState } from 'react'
import s from './index.module.scss'

type Props = {
  /** 标题 */
  title: string
  /** 是否显示下划线 */
  bottomLine?: boolean
  /** 取消按钮文字 */
  cancelText?: string
  /** 确认按钮文字 */
  confirmText?: string
  /** 取消按钮的style样式 */
  cancelStyle?: sp['style']
  /** 确定按钮的style样式 */
  confirmStyle?: sp['style']
  /** 标题style样式 */
  titleStyle?: sp['style']
  /** 取消事件 */
  onCancel?: sp['onClick']
  /** 确定事件 */
  onConfirm?: sp['onClick']
}
export default (props: Props) => {
  const {
    bottomLine = false,
    cancelStyle = '',
    confirmStyle = '',
    confirmText = '确定',
    cancelText = '取消',
  } = props

  return (
    <V className={cn(s.popBox, bottomLine && s.line)}>
      <Button className={cn(s.popBtn, s.popCancel)} onClick={props.onCancel} style={cancelStyle}>
        { cancelText }
      </Button>
      <V className={s.popTitle} style={props.titleStyle}>{ props.title }</V>
      <Button className={cn(s.popBtn, s.popConfirm)} onClick={props.onConfirm} style={confirmStyle}>
        { confirmText }
      </Button>
    </V>
  )
}
