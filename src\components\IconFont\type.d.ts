type IIconFontSize = 'large' | 'middle' | 'small' | number
type ColorType = 'black' | 'red' | 'green' | 'yellow' | 'blue' | 'magenta' | 'cyan' | 'white' | 'grey' | string

export default interface IIconFont {
  /** @name icon类样式名称 */
  type: string
  /** @name 图标大小 设置的font-size */
  size?: IIconFontSize
  /** @name 图标颜色 */
  style?: React.CSSProperties
  id?: string
  rotate?: number
  color?: ColorType
  className?: string
  onClick?: (param?: any) => void
  [key: string]: any
}
