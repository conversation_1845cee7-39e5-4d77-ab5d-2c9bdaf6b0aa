.container {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background-color: #fff;
    z-index: 200;
    top: 176px;
}

.skeleton {
    width: 100%;
}

.section1 {
    padding: 40px 24px 24px 32px;
    // gap: 24px;
}

.columns {
    display: flex;
    flex-direction: column;
}
.row {
    width: 100%;
    box-sizing: border-box;
    height: 44px;
    border-radius: 8px;
    margin-bottom: 24px;
}

.halfRow {
    width: 350px;
    height: 32px;
    border-radius: 8px;
}

.userInfo {
    display: flex;
    flex-direction: row;
    padding: 40px 0 24px 0;
    margin-left: 32px;
    width: 694px;
    // gap: 32px;
    border-top: 1px solid rgb(233,237,243);
    border-bottom: 1px solid rgb(233,237,243);
}

.avatar {
    width: 96px;
    height: 96px;
    border-radius: 50%;
}

.userInfoText {
    // gap: 20px;
    margin-left: 32px;
}

.nameRow {
    width: 114px;
    height: 44px;
    border-radius: 8px;
}

.companyRow {
    width: 350px;
    height: 32px;
    border-radius: 8px;
    margin-top: 20px;
}

.section2 {
    padding: 24px 24px 40px 32px;
    // gap: 24px;
}

.section {
    width: 128px;
    height: 44px;
    border-radius: 8px;
}

.titleRow {
    width: 350px;
    height: 32px;
    border-radius: 8px;
    margin-top: 24px;
}

.subRow {
    width: 100%;
    height: 32px;
    box-sizing: border-box;
    border-radius: 8px;
    margin-top: 24px;
}

