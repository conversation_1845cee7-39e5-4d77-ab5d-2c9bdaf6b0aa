export const NewData = { publishFlow: { subStep: 3, perfects: [1, 1] }, template: { occId: 764, templateInfo: { code: '764', controlInfoList: [{ fieldName: '职业偏好字段', orderAsc: 1, title: '您接受每周兼职几天？', subtitle: '已有50%的同行添加，完善后更吸引老板', controlCode: '1337', controlTypeCode: 'LABEL', controlNatureCode: 'PART_TIME', controlAttr: { dataList: [{ code: 'limitNum', name: '可选标签数', value: '1' }, { code: 'ifMulti', name: '是否多选', value: 'false' }], labelList: [{ code: '3030', name: '5天及以上', cornerMark: null, orderAsc: 1 }, { code: '3031', name: '3-4天', cornerMark: null, orderAsc: 2 }, { code: '3032', name: '2-3天', cornerMark: null, orderAsc: 3 }, { code: '3033', name: '1-2天', cornerMark: null, orderAsc: 4 }], leftList: null, rightList: null }, controlName: '每周兼职天数', ifManageMust: false, ifDetailShow: false, ifListShow: false, ifCompleteMust: false, status: 1, ifStandard: true }, { fieldName: '职业偏好字段', orderAsc: 2, title: '您接受在什么时间兼职?', subtitle: '已有50%的同行添加，完善后更吸引老板', controlCode: '1338', controlTypeCode: 'LABEL', controlNatureCode: 'PART_TIME', controlAttr: { dataList: [{ code: 'limitNum', name: '可选标签数', value: '3' }, { code: 'ifMulti', name: '是否多选', value: 'true' }], labelList: [{ code: '3034', name: '周末节假日', cornerMark: null, orderAsc: 1 }, { code: '3035', name: '工作日', cornerMark: null, orderAsc: 2 }, { code: '3036', name: '接受轮班', cornerMark: null, orderAsc: 3 }], leftList: null, rightList: null }, controlName: '兼职工作时间', ifManageMust: false, ifDetailShow: false, ifListShow: false, ifCompleteMust: false, status: 1, ifStandard: true }, { fieldName: '职业偏好字段', orderAsc: 3, title: '您期望做什么时段的兼职?', subtitle: '已有50%的同行添加，完善后更吸引老板', controlCode: '1339', controlTypeCode: 'LABEL', controlNatureCode: 'PART_TIME', controlAttr: { dataList: [{ code: 'limitNum', name: '可选标签数', value: '4' }, { code: 'ifMulti', name: '是否多选', value: 'true' }], labelList: [{ code: '3037', name: '早班', cornerMark: null, orderAsc: 1 }, { code: '3038', name: '午班', cornerMark: null, orderAsc: 2 }, { code: '3039', name: '晚班', cornerMark: null, orderAsc: 3 }, { code: '1334', name: '夜班', cornerMark: null, orderAsc: 4 }], leftList: null, rightList: null }, controlName: '兼职工作时段', ifManageMust: false, ifDetailShow: false, ifListShow: false, ifCompleteMust: false, status: 1, ifStandard: true }, { fieldName: '职业偏好字段', orderAsc: 4, title: '请问您持有哪种驾照？', subtitle: '已有50%的同行添加，完善后更吸引老板', controlCode: '1067', controlTypeCode: 'LABEL', controlNatureCode: 'ALL', controlAttr: { dataList: [{ code: 'limitNum', name: '可选标签数', value: '1' }, { code: 'ifMulti', name: '是否多选', value: 'false' }], labelList: [{ code: '2763', name: 'A1照', cornerMark: null, orderAsc: 1 }, { code: '2762', name: 'A2照', cornerMark: null, orderAsc: 2 }, { code: '2751', name: 'B1照', cornerMark: null, orderAsc: 3 }, { code: '2750', name: 'B2照', cornerMark: null, orderAsc: 4 }, { code: '2748', name: 'C1照', cornerMark: null, orderAsc: 5 }, { code: '2764', name: 'A1A2D照', cornerMark: null, orderAsc: 6 }], leftList: null, rightList: null }, controlName: '驾照', ifManageMust: true, ifDetailShow: true, ifListShow: true, ifCompleteMust: true, status: 1, ifStandard: true }, { fieldName: '职业偏好字段', orderAsc: 5, title: '请问您具备多少年的驾龄？', subtitle: '已有50%的同行添加，完善后更吸引老板', controlCode: '1068', controlTypeCode: 'LABEL', controlNatureCode: 'ALL', controlAttr: { dataList: [{ code: 'limitNum', name: '可选标签数', value: '1' }, { code: 'ifMulti', name: '是否多选', value: 'false' }], labelList: [{ code: '2161', name: '驾龄1年内', cornerMark: null, orderAsc: 1 }, { code: '2162', name: '驾龄1-2年', cornerMark: null, orderAsc: 2 }, { code: '2160', name: '驾龄3-5年', cornerMark: null, orderAsc: 3 }, { code: '2159', name: '驾龄6-10年', cornerMark: null, orderAsc: 4 }, { code: '2163', name: '驾龄10年及以上', cornerMark: null, orderAsc: 5 }], leftList: null, rightList: null }, controlName: '驾龄', ifManageMust: false, ifDetailShow: true, ifListShow: true, ifCompleteMust: false, status: 1, ifStandard: true }, { fieldName: '职业偏好字段', orderAsc: 6, title: '您拥有哪些证件？', subtitle: '已有50%的同行添加，完善后更吸引老板', controlCode: '1053', controlTypeCode: 'LABEL', controlNatureCode: 'ALL', controlAttr: { dataList: [{ code: 'limitNum', name: '可选标签数', value: '4' }, { code: 'ifMulti', name: '是否多选', value: 'true' }], labelList: [{ code: '2560', name: '出租车资格证', cornerMark: null, orderAsc: 1 }, { code: '1511', name: '网约车资格证', cornerMark: null, orderAsc: 2 }, { code: '2046', name: '客运资格证', cornerMark: null, orderAsc: 3 }, { code: '2140', name: '健康证', cornerMark: null, orderAsc: 4 }], leftList: null, rightList: null }, controlName: '证件', ifManageMust: false, ifDetailShow: true, ifListShow: true, ifCompleteMust: false, status: 1, ifStandard: true }, { fieldName: '职业偏好字段', orderAsc: 7, title: '您接受哪些工具来源？', subtitle: '已有50%的同行添加，完善后更吸引老板', controlCode: '1069', controlTypeCode: 'LABEL', controlNatureCode: 'ALL', controlAttr: { dataList: [{ code: 'limitNum', name: '可选标签数', value: '4' }, { code: 'ifMulti', name: '是否多选', value: 'true' }], labelList: [{ code: '1025', name: '自己带车', cornerMark: null, orderAsc: 1 }, { code: '2318', name: '公司提供车', cornerMark: null, orderAsc: 2 }, { code: '2116', name: '接受租车', cornerMark: null, orderAsc: 3 }, { code: '2119', name: '接受购车', cornerMark: null, orderAsc: 4 }], leftList: null, rightList: null }, controlName: '工具来源', ifManageMust: false, ifDetailShow: true, ifListShow: true, ifCompleteMust: false, status: 1, ifStandard: true }, { fieldName: '职业偏好字段', orderAsc: 8, title: '您有哪些驾驶优势？', subtitle: '已有50%的同行添加，完善后更吸引老板', controlCode: '1078', controlTypeCode: 'LABEL', controlNatureCode: 'ALL', controlAttr: { dataList: [{ code: 'limitNum', name: '可选标签数', value: '4' }, { code: 'ifMulti', name: '是否多选', value: 'true' }], labelList: [{ code: '1473', name: '无一次性扣6分及以上违章', cornerMark: null, orderAsc: 1 }, { code: '1474', name: '无一次性扣12分违章', cornerMark: null, orderAsc: 2 }, { code: '1480', name: '无酒驾醉驾及重大交通事故', cornerMark: null, orderAsc: 3 }, { code: '1528', name: '退伍军人', cornerMark: null, orderAsc: 4 }, { code: '1759', name: '商务驾驶经验', cornerMark: null, orderAsc: 5 }, { code: '1758', name: '商务接待经验', cornerMark: null, orderAsc: 6 }, { code: '2561', name: '出租车驾驶经验', cornerMark: null, orderAsc: 7 }, { code: '1258', name: '有代驾经验', cornerMark: null, orderAsc: 8 }], leftList: null, rightList: null }, controlName: '驾驶优势', ifManageMust: false, ifDetailShow: true, ifListShow: true, ifCompleteMust: false, status: 1, ifStandard: true }, { fieldName: '职业偏好字段', orderAsc: 9, title: '您具备哪些个人亮点？', subtitle: '已有50%的同行添加，完善后更吸引老板', controlCode: '1152', controlTypeCode: 'LABEL', controlNatureCode: 'ALL', controlAttr: { dataList: [{ code: 'limitNum', name: '可选标签数', value: '4' }, { code: 'ifMulti', name: '是否多选', value: 'true' }], labelList: [{ code: '1999', name: '零交通事故', cornerMark: null, orderAsc: 1 }, { code: '1652', name: '熟练使用导航', cornerMark: null, orderAsc: 2 }, { code: '1616', name: '熟悉维修保养', cornerMark: null, orderAsc: 3 }, { code: '1025', name: '自己带车', cornerMark: null, orderAsc: 4 }, { code: '1719', name: '身体健康', cornerMark: null, orderAsc: 5 }, { code: '2370', name: '服务意识强', cornerMark: null, orderAsc: 6 }, { code: '1803', name: '认真负责', cornerMark: null, orderAsc: 7 }, { code: '2118', name: '接受夜班/倒班', cornerMark: null, orderAsc: 8 }], leftList: null, rightList: null }, controlName: '个人亮点', ifManageMust: false, ifDetailShow: true, ifListShow: true, ifCompleteMust: false, status: 1, ifStandard: true }, { fieldName: '', orderAsc: 11, title: null, subtitle: null, controlCode: 'F_Name', controlTypeCode: 'SPECIAL_CONTROL', controlNatureCode: 'ALL', controlAttr: { dataList: null, labelList: null, leftList: null, rightList: null }, controlName: '姓名', ifManageMust: false, ifDetailShow: true, ifListShow: true, ifCompleteMust: true, status: 1, ifStandard: false }, { fieldName: '', orderAsc: 11, title: null, subtitle: null, controlCode: 'F_Gender', controlTypeCode: 'SPECIAL_CONTROL', controlNatureCode: 'ALL', controlAttr: { dataList: null, labelList: null, leftList: null, rightList: null }, controlName: '性别', ifManageMust: false, ifDetailShow: true, ifListShow: true, ifCompleteMust: true, status: 1, ifStandard: false }, { fieldName: '', orderAsc: 11, title: null, subtitle: null, controlCode: 'F_Age', controlTypeCode: 'SPECIAL_CONTROL', controlNatureCode: 'ALL', controlAttr: { dataList: null, labelList: null, leftList: null, rightList: null }, controlName: '出生年月', ifManageMust: false, ifDetailShow: true, ifListShow: true, ifCompleteMust: true, status: 1, ifStandard: false }, { fieldName: '', orderAsc: 11, title: null, subtitle: null, controlCode: 'F_WorkType', controlTypeCode: 'SPECIAL_CONTROL', controlNatureCode: 'ALL', controlAttr: { dataList: null, labelList: null, leftList: null, rightList: null }, controlName: '期望职位\r\n（入口）', ifManageMust: false, ifDetailShow: true, ifListShow: true, ifCompleteMust: false, status: 1, ifStandard: false }, { fieldName: '', orderAsc: 11, title: null, subtitle: null, controlCode: 'F_WorkNature', controlTypeCode: 'SPECIAL_CONTROL', controlNatureCode: 'ALL', controlAttr: { dataList: null, labelList: null, leftList: null, rightList: null }, controlName: '求职类型', ifManageMust: false, ifDetailShow: true, ifListShow: true, ifCompleteMust: false, status: 1, ifStandard: false }, { fieldName: '', orderAsc: 11, title: null, subtitle: null, controlCode: 'F_SalaryDay', controlTypeCode: 'SPECIAL_CONTROL', controlNatureCode: 'PART_TIME', controlAttr: { dataList: null, labelList: null, leftList: null, rightList: null }, controlName: '期望薪资', ifManageMust: false, ifDetailShow: true, ifListShow: true, ifCompleteMust: false, status: 1, ifStandard: false }, { fieldName: '', orderAsc: 11, title: null, subtitle: null, controlCode: 'F_SalaryMonth', controlTypeCode: 'SPECIAL_CONTROL', controlNatureCode: 'FULL_TIME', controlAttr: { dataList: null, labelList: null, leftList: null, rightList: null }, controlName: '期望薪资', ifManageMust: false, ifDetailShow: true, ifListShow: true, ifCompleteMust: false, status: 1, ifStandard: false }, { fieldName: '', orderAsc: 11, title: null, subtitle: null, controlCode: 'F_Preference', controlTypeCode: 'SPECIAL_CONTROL', controlNatureCode: 'ALL', controlAttr: { dataList: null, labelList: null, leftList: null, rightList: null }, controlName: '职位偏好\r\n（入口）', ifManageMust: false, ifDetailShow: true, ifListShow: true, ifCompleteMust: false, status: 1, ifStandard: false }, { fieldName: '', orderAsc: 11, title: null, subtitle: null, controlCode: 'F_SelfIntroduction', controlTypeCode: 'SPECIAL_CONTROL', controlNatureCode: 'ALL', controlAttr: { dataList: null, labelList: null, leftList: null, rightList: null }, controlName: '个人优势\r\n（入口）', ifManageMust: false, ifDetailShow: true, ifListShow: true, ifCompleteMust: false, status: 1, ifStandard: false }, { fieldName: '', orderAsc: 11, title: null, subtitle: null, controlCode: 'F_City', controlTypeCode: 'SPECIAL_CONTROL', controlNatureCode: 'ALL', controlAttr: { dataList: null, labelList: null, leftList: null, rightList: null }, controlName: '工作城市\r\n（入口）', ifManageMust: false, ifDetailShow: true, ifListShow: true, ifCompleteMust: false, status: 1, ifStandard: false }, { fieldName: '', orderAsc: 11, title: null, subtitle: null, controlCode: 'F_EducationalExperience', controlTypeCode: 'AGGREGATION_SPECIAL_CONTROL', controlNatureCode: 'ALL', controlAttr: { dataList: null, labelList: null, leftList: null, rightList: null }, controlName: '教育经历\r\n（入口）', ifManageMust: false, ifDetailShow: true, ifListShow: true, ifCompleteMust: false, status: 1, ifStandard: false }, { fieldName: '', orderAsc: 11, title: null, subtitle: null, controlCode: 'F_ProjectExperience', controlTypeCode: 'AGGREGATION_SPECIAL_CONTROL', controlNatureCode: 'ALL', controlAttr: { dataList: null, labelList: null, leftList: null, rightList: null }, controlName: '项目经历\r\n（入口）', ifManageMust: false, ifDetailShow: true, ifListShow: true, ifCompleteMust: false, status: 1, ifStandard: false }, { fieldName: '', orderAsc: 11, title: null, subtitle: null, controlCode: 'F_VideoResume', controlTypeCode: 'AGGREGATION_SPECIAL_CONTROL', controlNatureCode: 'ALL', controlAttr: { dataList: null, labelList: null, leftList: null, rightList: null }, controlName: '视频简历', ifManageMust: false, ifDetailShow: true, ifListShow: true, ifCompleteMust: false, status: 1, ifStandard: false }, { fieldName: '', orderAsc: 11, title: null, subtitle: null, controlCode: 'F_WorkExperience', controlTypeCode: 'AGGREGATION_SPECIAL_CONTROL', controlNatureCode: 'ALL', controlAttr: { dataList: null, labelList: null, leftList: null, rightList: null }, controlName: '工作经历\r\n（入口）', ifManageMust: false, ifDetailShow: true, ifListShow: true, ifCompleteMust: false, status: 1, ifStandard: false }, { fieldName: '', orderAsc: 11, title: null, subtitle: null, controlCode: 'F_UploadResumeAttachment', controlTypeCode: 'SPECIAL_CONTROL', controlNatureCode: 'ALL', controlAttr: { dataList: null, labelList: null, leftList: null, rightList: null }, controlName: '上传简历附件', ifManageMust: false, ifDetailShow: true, ifListShow: true, ifCompleteMust: false, status: 1, ifStandard: false }, { fieldName: '', orderAsc: 11, title: null, subtitle: null, controlCode: 'F_Credentials', controlTypeCode: 'SPECIAL_CONTROL', controlNatureCode: 'ALL', controlAttr: { dataList: null, labelList: null, leftList: null, rightList: null }, controlName: '技能证书', ifManageMust: false, ifDetailShow: true, ifListShow: true, ifCompleteMust: false, status: 1, ifStandard: false }] } }, params: { occAreaReq: { occupations: [{ occupation: '764', name: '网约车司机', mode: 2, industries: ['12'] }], hopeArea: ['322', '323', '324'] }, userReq: { name: '木工', gender: 2, birthDay: '2006-11-01' } }, publishRuleSwitch: true }
