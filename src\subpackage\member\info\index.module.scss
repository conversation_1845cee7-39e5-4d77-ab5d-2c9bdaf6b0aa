
.body {
  padding: 0 32px;
  background: #fff;
  @include safe-area(48px);
}

.title {
  flex: 1;
  font-size: 34px;
}
.avatar {
  display: flex;
  align-items: center;
  padding: 24px 0;
  @include bottom-line-b();
}
.avatarCont {
  position: relative;
  width: 128rpx;
  height: 128rpx;
  border-radius: 50%;
  overflow: hidden;
}
.avatarImg {
  width: 100%;
  height: 100%;
}

.realName {
  color: rgba(0, 0, 0, 0.45);
  font-size: 30px;
}

/** 审核中 */
.statusCenter {
  color: #0092ff;
  font-weight: 400;
  line-height: 42px;
  font-size: 30px;
}

/** 头像审核状态 */
.avatarStatus {
  position: absolute;
  width: 128px;
  height: 44px;
  bottom: 0;
  left: 0;
  z-index: 2;
  background: rgba(0, 0, 0, 0.65);
  font-weight: 500;
  font-size: 26px;
  color: #fff;
  text-align: center;
  line-height: 44px;
}
