.sheetBox {
  padding: 24px;
  @include safe-area(24px);
}

.sheetList {
  border-radius: 24px;
  background: #ffffff;
  overflow: hidden;
}
.sheetTitle {
  font-size: 26px;
  height: 88px;
  color: rgba(0, 0, 0, 0.45);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 24px;
}
.sheetItem {
  height: 112px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 24px;
  font-size: 34px;
  color: rgba(0, 0, 0, 0.85);
  @include top-line-b();
  @include active();
  &.active {
    color: $primary-color!important;
    font-weight: bold;
  }
}

.itemClose {
  text-align: center;
  color: rgba(0, 0, 0, 0.65);
}
