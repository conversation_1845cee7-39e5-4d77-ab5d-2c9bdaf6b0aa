import { Text, View } from '@tarojs/components'
import { useRef, useState } from 'react'
import { useLoad } from '@tarojs/taro'
import Page from '@/components/Page'
import { getListValue } from '@/core/utils/publish'
import style from './index.module.scss'
import ResumeTemplatesV4 from './components/ResumeTemplatesV4'
import Click from '@/components/Click'
import CustomNavbar from '@/components/CustomNavbar'
import { getShowTemplate } from '../utils'
import { getResumeDetails } from '@/utils/helper/resume/index'

export default function Index() {
  const { template, preferenceList, occupation } = $.router.data

  const [data, setData] = useState<any>({
    resumeTl: {},
    /** 未修改值的提交数据 */
    oldValue: [],
    // 模板的控件 只存controlCode
    controlList: [],
    // 模板的控件 对应对象，controlCode为键值
    controlObj: {} as any,
    // 模板对应的工种
    classify: {},
    // 模板的code
    templateCode: '',
    // 默认选中的值
    preferenceList: [],
    footerHeight: 0,

    query: {
      /** 工种id,多个用,分割 */
      occIds: '',
      /** 初始进入求职期望工种id,用于编辑期望时用到 */
      oOccIds: '',
      /** 来源页  add:简历发布  edit:编辑简历 */
      origin: 'add',
      positionType: 1,
      industries: '',
      resumeSubUuid: '',
      oPositionType: 1,
    },
  })
  const changeList = useRef({})

  useLoad((options) => {
    init(options)
  })

  const init = async (query) => {
    let nTemplate = { ...template }
    if ($.isEmptyObject(template)) {
      const occIds = `${query.occIds}`.split(',').map(id => Number(id))
      if (!$.isArrayVal(occIds)) {
        return
      }
      const [templates] = await $.request['POST/cms/occTemplate/v1/listTemplateByOccIdsV2']({ occIdList: occIds, businessDomain: 'resume', displayScenes: ['RESUME_EDIT_PREFERENCE_DISPLAY'] })
      if ($.isArrayVal(templates)) {
        nTemplate = { ...templates[0] }
      }
    }
    if ($.isEmptyObject(nTemplate)) {
      return
    }
    const { templateInfo, occId } = nTemplate || {}
    const { code } = templateInfo || {}
    const { controlObj, controlList } = await getShowTemplate(query, nTemplate, preferenceList)
    const sData: any = { controlObj, controlList, templateCode: code, oldValue: preferenceList }
    const classifys = await $.getClassifyByIds([occId])
    if ($.isArrayVal(classifys)) {
      sData.classify = { ...(classifys[0] || {}) }
    }
    setData((prev) => ({ ...prev, ...sData, query }))
  }

  const onChange = (e) => {
    const { list } = e || {}
    changeList.current = list
  }

  /** 判断是否可以提交数据 */
  const getSubmitParams = async () => {
    const { oldValue } = data
    const { templateCode } = data
    const { value, isErrMsg } = getListValue({ list: Object.values(changeList.current || {}), reqAll: false, mustKey: 'ifMust', templateCode, notIfMust: false, isHideMsg: false })

    if (isErrMsg) {
      return false
    }

    // 判断值是否有变化  两个对象是否相等
    if ($.isArrayVal(value) && $.isEqualObj(oldValue, value)) {
      $.router.back(1)
      return false
    }
    return value
  }

  /** 提交数据 */
  const onSubmit = async () => {
    const params = await getSubmitParams()
    if (!params) {
      return
    }
    const { query } = data
    const { oOccIds, occIds, resumeSubUuid, positionType, oPositionType } = query || {}

    if (positionType != oPositionType || (occIds != oOccIds && oOccIds) || !resumeSubUuid || $.isEmptyObject(occupation)) {
      saveBack(params)
    } else {
      const occCtrls = params.map((oitem) => ({
        templateCode: oitem.templateCode,
        controlCode: oitem.controlCode,
        controlTypeCode: oitem.controlTypeCode,
        controlValues: oitem.controlValues,
      }))

      $.showLoading('保存中')
      $.request['POST/resume/v3/perfect/updateNormalCtrl']({ occCtrls, scene: 7, resumeSubUuid, positionType: Number(positionType), occupations: occupation }).then((res) => {
        $.hideLoading()
        const { error, message } = res || {}
        if (error && message && message != '请求成功' && message != 'success') {
          $.msg(message)
          return
        }
        saveBack(params)
        // 必须更新我的找活信息数据
        getResumeDetails()
      }).catch(() => {
        $.hideLoading()
        saveBack(params)
      })
    }
  }

  // 保存数据，返回上一页
  const saveBack = (params) => {
    const { controlList } = data
    let nArr = [...(params || [])]
    if ($.isArrayVal(controlList) && $.isArrayVal(params)) {
      const obj:any = {}
      params.forEach((item) => {
        obj[item.controlCode] = item
      })
      nArr = []
      controlList.forEach((control) => {
        const { code, cObj } = control || {}
        if (obj[code]) {
          nArr.push(obj[code])
        }
        if (!$.isEmptyObject(cObj)) {
          const coArr = Object.values(cObj).flat()
          if ($.isArrayVal(coArr)) {
            coArr.forEach((c:any) => {
              if (obj[c]) {
                nArr.push(obj[c])
              }
            })
          }
        }
      })
    }

    $.router.event(nArr)
    $.router.back(1)
  }

  const onBack = async () => {
    const value = await getSubmitParams()
    if (!value) {
      $.router.back()
      return
    }
    const { oldValue } = data
    const nValue = {}
    const oValue = {}
    value.forEach(item => {
      nValue[item.controlCode] = {
        templateCode: item.templateCode,
        controlCode: item.controlCode,
        controlTypeCode: item.controlTypeCode,
        controlValues: item.controlValues,
      }
    })
    oldValue.forEach(item => {
      oValue[item.controlCode] = {
        templateCode: item.templateCode,
        controlCode: item.controlCode,
        controlTypeCode: item.controlTypeCode,
        controlValues: item.controlValues,
      }
    })

    // 判断值是否修改过值
    if ($.isEqualObj(oValue, nValue)) {
      $.router.back()
      return
    }
    $.confirm({
      content: '页面内容存在修改，请确认是否保存',
      cancelText: '取消',
      confirmText: '保存',
    }).then(() => {
      onSubmit()
    }).catch(() => {
      $.router.back()
    })
  }
  return (
    <Page backgroundColor='#fff'>
      <CustomNavbar title=' ' onBack={onBack} />
      <View className={style.body}>
        <View className={style.headv}>
          <View className={style.headTile}>{data.classify.name}岗位求职偏好</View>
          <View className={style.headDesc}>你的偏好选择将用于为你推荐更匹配的职位</View>
        </View>
        <ResumeTemplatesV4
          templateCode={data.templateCode}
          controlObj={data.controlObj}
          controlList={data.controlList}
          query={data.query}
          change={onChange}
        />
      </View>
      <View className={style.footer}>
        <View className={style.btnView}>
          <Click className={style.btn} onClick={onSubmit}><Text>保存</Text></Click>
        </View>
      </View>
    </Page>
  )
}
