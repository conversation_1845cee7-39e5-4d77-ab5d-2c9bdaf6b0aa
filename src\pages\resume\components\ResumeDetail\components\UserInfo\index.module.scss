.content {
  padding-bottom: 48px;
  @include bottom-line-b();
}

.user-cont {
  display: flex;

  .user-left {
    flex: 1;
    padding-right: 24px;
  }

  .user-right {
    width: 112px;
    height: 112px;
    border-radius: 50%;
    overflow: hidden;
    position: relative;
  }
}

.user-name {
  display: flex;
  align-items: center;
  padding-bottom: 16px;
}

.user-tag {
  padding: 4px 16px;
  padding-right: 0;

  .user-icon {
    margin-left: 8px;
    width: 12px;
    height: 12px;
  }

  .m-tag {
    border: 2px solid #E0F3FF;
    background: #FFF;
    color: #0092FF;
    font-weight: bold;
    padding: 0 8px;
    font-size: 26px;
    height: 44px;
    line-height: 42px;
    border-radius: 8px;
  }

  .m-tag-two {
    color: rgba(0, 0, 0, 0.45);
    border: 2px solid rgba(0, 0, 0, 0.45);
    padding: 0 8px;
    background: #FFF;
    font-weight: bold;
    font-size: 26px;
    height: 44px;
    line-height: 46px;
    border-radius: 8px;
  }
}

.user-edit-icon {
  padding-left: 16px;
}

.user-text {
  height: 72px;
  font-size: 52px;
  line-height: 72px;
  font-weight: bold;
}

.user-desc {
  font-size: 26px;
  line-height: 36px;
  color: rgba(0, 0, 0, 0.65);
  padding-bottom: 16px;
}

.user-avatar {
  width: 112px;
  height: 112px;
  border-radius: 50%;
}

.user-avatar-text {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  line-height: 40px;
  color: #FFF;
  background: rgba(0, 0, 0, 0.65);
  font-weight: bold;
  font-size: 22px;
  text-align: center;
}

.user-footer {
  display: flex;
  flex-wrap: wrap;
}

.footer-cont {
  display: flex;
  align-items: center;
  color: rgba(0, 0, 0, 0.65);
  padding-right: 16px;

  .cont-text {
    font-size: 26px;
    padding-left: 8px;
  }
}
