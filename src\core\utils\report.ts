import report from '@datarangers/sdk-mp'
import * as config from '../config'
import { store } from '../store'

try {
  report.init({
    app_id: config.FINDER_APP_ID,
    channel_domain: 'https://snssdk.yupaowang.com', // 设置数据上送地址
    log: false, // 是否开启日志打印
    auto_report: {
      appLaunch: true,
      appTerminate: true,
      appError: true, // 上报on_error事件，false时不上报
      pageShow: true, // 上报predefine_pageview事件，false时不上报
      pageHide: true, // 上报predefine_pageview_hide事件，false时不上报，或者pageShow为false时该事件也不会上报
      pageShare: true, // 上报分享事件
      pageFavorite: true, // 上报收藏事件
      click: true, // 上报bav2b_click事件（tap点击事件）
      clickIgnoreProperty: ['item'], // 在click设置为true时，clickIgnoreProperty生效，目的过滤被点击元素上相应的属性，比如['imgSrc']表示会过滤data-img-src属性，不会采集该属性值
    }, // 是否开启预置事件采集
    enable_buffer: true, // 是否开启缓存
    buffer_interval: 5000, // 缓存间隔时间
    buffer_number: 20, // 缓存条数
  })
  report.config({
    /** 小程序名称 */
    mini_name_v: '支付宝',
    /** 版本号 */
    app_version: config.VERSION,
    /** 版本号 */
    mini_version: config.VERSION,
    /** 小程序appid */
    package: config.appid,
  })

  if (process.env.NODE_ENV === 'development') {
    const oldEvent = report.event as any
    /** 代理这个函数, 执行时候, 会打印出埋点信息 */
    report.event = new Proxy(oldEvent, {
      apply(target, thisArg, argumentsList) {
        if (store.getState().storage.buryDebug) {
          console.log(`%c${argumentsList[0]}`, 'color: white; background: red; font-weight: bold;', ...argumentsList.slice(1))
        }
        return Reflect.apply(target, thisArg, argumentsList)
      },
    })
  }

  report.send()
} catch (err) {
  console.log('埋点初始化失败', err)
  report.event = () => {}
  report.config = () => {}
}

export default report
