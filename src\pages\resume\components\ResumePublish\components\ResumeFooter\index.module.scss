.fotter {
  position: fixed;
  bottom: 0;
  width: 100vw;
  background: #fff;
  z-index: 10;
}

.btnOut {
  padding: 24rpx 32rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.btn {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 24rpx 0;
  background: rgba(0, 146, 255, 1);
  border-radius: 16rpx;
  color: rgba(255, 255, 255, 1);
  font-weight: bold;
  font-size: 34rpx;
  line-height: 48rpx;
}

.delBtn {
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 12rpx;
  background: rgba(245, 246, 250, 1);
  color: rgba(0, 0, 0, 0.65);
  width: 220rpx;
  height: 96rpx;
  font-size: 34rpx;
  font-weight: bold;
  margin-right: 20rpx;
}

.ruleTips {
  line-height: 40rpx;
  text-align: center;
  color: rgba(0, 0, 0, 0.45);
  font-size: 28rpx;
  width: 100%;
  padding: 20rpx 0;
}

.rule {
  color: rgba(0, 146, 255, 1);
}

.pdBtm {
  padding-bottom: constant(safe-area-inset-bottom); /* 兼容 IOS<11.2 */
  padding-bottom: env(safe-area-inset-bottom); /* 兼容 IOS>11.2 */
}