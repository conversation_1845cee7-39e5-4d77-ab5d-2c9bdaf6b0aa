.warp {
  padding: 0 32px;
}

.title {
  font-size: 42px;
  color: rgba(0, 0, 0, 0.85);
  margin-bottom: 24px;
  padding-top: 48px;
  font-weight: bold;
}

.header {
  height: 112px;
  padding: 32px;
}

.scrollView {
  max-height: calc(80vh - 120px);
}

.tips {
  display: block;
  font-size: 26px;
  color: #000000A6;
  margin-bottom: 48px;
  line-height: 150%;
  margin-right: 16px;

}

.workTime {
  display: flex;
  flex-wrap: wrap;
}


.line {
  border-bottom: 1px solid #e9edf3ff;
}

.subTitle {
  padding-top: 48px;
  font-size: 34px;
  color: rgba(0, 0, 0, 0.85);
  margin-bottom: 24px;
  font-weight: bold;
}

.item {
  display: flex;
  margin-bottom: 80px;
  height: 86px;
}

.itemText {
  margin-left: 48px;
}

.text {
  font-size: 32px;
  color: rgba(0, 0, 0, 0.85);
  font-weight: bold;
}

.textTips {
  display: block;
  font-size: 26px;
  color: #000000A6;
  padding-top: 8px;
}