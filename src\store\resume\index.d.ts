/*
 * @Author: jiang<PERSON>
 * @LastEditors: jianglong
 */

type queryTemplateByUserId = YModels['POST/resume/v1/resumeTplOccRelation/queryTemplateByUserId']['Res']['data']['list']
type IResControlItem = queryTemplateByUserId[0]['controlList'][0]

/** 控件集合 */
type IControlList = (Omit<IResControlItem, 'attrItems' | 'extendJson'> & {
  attrItems?: Array<{ label: string, value: string | number}>
  extendJson?: { [key: string]: any } | ''
})[]

/** 模板数据列表 */
export type ITemplateList = (Omit<queryTemplateByUserId[0], 'controlList'> & {
  /** 控件集合 */
  controlList: IControlList
  /** 信息是否已完善 */
  completed?: boolean
  /** 工种名字 */
  occName?: string
})[]
