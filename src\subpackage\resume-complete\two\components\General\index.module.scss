.item {
  padding: 40rpx 0;
  border-bottom: 2rpx solid rgba(233, 237, 243, 1);
}

.label {
  display: flex;
  align-items: center;
  color: rgba(0, 0, 0, 0.65);
  font-size: 30rpx;
  line-height: 42rpx;
}

.must {
  font-size: 28rpx;
  color: rgba(232, 54, 46, 1);
  line-height: 40rpx;
}

.content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 16rpx;
}

.names {
  color: rgba(0, 0, 0, 0.85);
  font-size: 34rpx;
  line-height: 48rpx;
}

.plac {
  color: rgba(0, 0, 0, 0.25);
}

.icon {
  margin-left: 32rpx;
}

.txtNowrap {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
