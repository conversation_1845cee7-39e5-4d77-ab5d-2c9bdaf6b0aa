
page {
  font-size: 30rpx;
  color: rgba(0, 0, 0, 0.85);
}

view, scroll-view, textarea, input, image, text, button, div {
  box-sizing: border-box;
}

/** 骨架屏 Start */
.skeleton {
  position: relative;
  overflow: hidden;

  &::after {
    content: ' ';
    display: block;
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: #f2f2f2 linear-gradient(90deg, #f2f2f2 25%, #e6e6e6 37%, #f2f2f2 63%);
    background-size: 400% 100%;
    animation: skeleton-loading 1.4s ease infinite;
  }
}

.skeleton-bg {
  background: #f2f2f2 linear-gradient(90deg, #f2f2f2 25%, #e6e6e6 37%, #f2f2f2 63%);
  background-size: 400% 100%;
  animation: skeleton-loading 1.4s ease infinite;
}

@keyframes skeleton-loading {
  0% {
    background-position: 100% 50%;
  }
  to {
    background-position: 0 50%;
  }
}

/** 荣耀设备快手小程序特殊处理 */
.honor-kwai-fix {
  z-index: 99999 !important;
  position: fixed !important;
  // 确保在所有原生组件之上
  isolation: isolate;
  // 创建新的层叠上下文
  will-change: transform;
  // 强制硬件加速
  transform: translateZ(0);
}

.honor-kwai-mask {
  z-index: 99998 !important;
  // 确保遮罩层也在原生组件之上
  isolation: isolate;
  // 强制硬件加速
  transform: translateZ(0);
}

.tab-bar-height {
  height: calc(112px + constant(safe-area-inset-bottom));
  height: calc(112px + env(safe-area-inset-bottom));
}
