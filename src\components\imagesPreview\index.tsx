import { View, Image, Swiper, SwiperItem } from '@tarojs/components'
import './index.scss'
import { useState, useEffect } from 'react'

interface IProps {
  images: string[] // 支持网络图片URL数组
  currentIndex?: number
  onClose: () => void
}

const ImagePreview: React.FC<IProps> = ({ images, currentIndex = 0, onClose }) => {
  const [current, setCurrent] = useState(currentIndex)
  const [sysInfo, setSysInfo] = useState<any>(null)

  useEffect(() => {
    // 获取系统信息，用于快手小程序适配
    const info = $.sysInfo()
    setSysInfo(info)
  }, [])

  // 处理滑动切换
  const handleSwiperChange = (e) => {
    setCurrent(e.detail.current)
  }

  // 计算关闭按钮的top位置，快手小程序需要特殊处理
  const getCloseButtonTop = () => {
    if (process.env.TARO_ENV === 'kwai' && sysInfo) {
      // 快手小程序使用状态栏高度 + 一些间距
      return `${sysInfo.statusBarHeight + 15}px`
    }
    return '100rpx'
  }

  // 获取预览容器的样式，快手小程序需要特殊处理
  const getPreviewMaskStyle = () => {
    if (process.env.TARO_ENV === 'kwai' && sysInfo) {
      return {
        paddingTop: `${sysInfo.statusBarHeight}px`,
      }
    }
    return {}
  }

  return (
    <View className="preview-mask" style={getPreviewMaskStyle()} onClick={onClose}>
      {/* 新增关闭按钮 */}
      <Image
        src="https://cdn.yupaowang.com/yupao_mini/Close-small-white.svg"
        className="preview-close"
        mode="widthFix"
        style={{ top: getCloseButtonTop() }}
        onClick={(e) => {
          e.stopPropagation()
          onClose()
        }}
      />

      {/* 图片展示区域 */}
      <Swiper
        className="preview-swiper"
        current={current}
        onChange={handleSwiperChange}
      >
        {images?.map((img, index) => (
          <SwiperItem key={index} className="preview-swiper-item">
            <Image
              src={img}
              mode="aspectFit"
              className="preview-image"
              onClick={(e) => e.stopPropagation()}
            />
          </SwiperItem>
        ))}
      </Swiper>

      {/* 底部指示器 */}
      <View className="preview-indicator">
        {current + 1}/{images.length}
      </View>
    </View>
  )
}

export default ImagePreview
