import { useState } from 'react'
import s from './index.module.scss'
import IconFont from '@/components/IconFont'
import CellCard from '../CellCard'
import PopStatus from '../PopStatus'
// import { StandardProps } from '@tarojs/components'

type Props = {
  onRefresh: () => void
  dataSource: {[key: string]: any}
}

/** 求职状态 */
export default function Index(props: Props) {
  const dataSource = props.dataSource || {}
  const [visible, setVisible] = useState(false)

  const onTap = () => {
    setVisible(true)
  }

  return (
    <>
      <CellCard
        title="求职状态"
        onClick={onTap}
      >
        <V onClick={onTap} className={s.content}>
          <V className={s['c-text']}>{dataSource.workStatusName}</V>
          <IconFont type="yp-mbxl" size={32} color="rgba(0, 0, 0, 0.45)" />
        </V>
      </CellCard>
      <PopStatus visible={visible}
        onChange={() => props.onRefresh && props.onRefresh()}
        onClose={() => setVisible(false)}
      />
    </>
  )
}
