import { ScrollView, View as V } from '@tarojs/components'
import { TalentDevelopmentType } from '../../formatEnterpriseMainView'
import styles from './index.module.scss'

interface IProps {
  data: TalentDevelopmentType
}

/**
 * 人才发展
*/
export default (props: IProps) => {
  return (
    <V className={styles.wrap}>
      <V className={styles.title}>人才发展</V>
      <ScrollView scrollX className={styles.tagList}>
        <V style={{ display: 'flex' }}>
          {
            props.data.map((item, index) => (
              <>
                <V className={styles.tag} key={index}>{item.label}</V>
                {index === props.data.length - 1 && <V style={{ width: '32rpx', height: '1px', opacity: 0 }}>1</V>}
              </>
            ))
          }
        </V>
      </ScrollView>
    </V>
  )
}
