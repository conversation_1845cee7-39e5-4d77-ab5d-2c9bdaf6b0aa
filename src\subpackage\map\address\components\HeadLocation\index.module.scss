$header-header:112px;

/** header */
.header {
  display: flex;
  align-items: center;
  height: $header-header;
  background: #FFF;
  font-size: 30px;
  justify-content: space-between;
  @include bottom-line-b();
  &.no-header {
    height: 100%;
    top: 0;
  }
}

.left-icon {
  color: $primary-color;
  margin: 0 8px 0 32px;
}

.left {
  flex: 1;

  .text {
    display: inline-block;
    line-height: $header-header;
    color: rgba(0, 0, 0, 0.85);

    .value {
      display: inline-block;
      color: $primary-color;
    }
  }
}

.right {
  display: flex;
  align-items: center;
  width: 184px;
  color: $primary-color;

  .right-icon {
    margin-right: 8px;
    color: $primary-color;
  }
  .right-text {
    font-weight: bold;
  }
}
