import { useCallback, useEffect, useRef } from 'react'

/**
 * 创建一个定时器，在组件销毁时自动清除定时器
 * @returns 返回一个对象，包含创建定时器的方法
 */
export default function useTimeout() {
  const timer = useRef<ReturnType<typeof setTimeout>[]>([])

  /**
   * 创建定时器的方法
   * @param fn 定时器执行的函数
   * @param time 定时器时间，默认1000ms
   */
  const timeout = useCallback((fn: Function, time = 1000) => {
    const id = setTimeout(() => {
      fn()
    }, time)
    timer.current.push(id)
  }, [])

  useEffect(() => {
    return () => {
      timer.current.forEach(id => clearTimeout(id))
    }
  }, [])

  return { timeout }
}
