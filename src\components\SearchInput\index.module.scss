
.searchInput {
  align-items: center;
  display: flex;
  position: relative;
  width: 100%;
}

.inputBox {
  flex:1;
  align-items: center;
  display: flex;
  border-radius: 16px;
  padding: 10px 24px;
  padding-right: 0px;
  background-color: #F5F7FCFF;
}


.disableInput {
  flex: 1;
  background-color: #F5F7FCFF;
  padding-block: 5px;
  margin-left: 5px;
}


.leftSlot {
  margin-right: 16rpx;
  line-height: 1;
}


.disablePlaceHolder {
  color: #bfbfbf;
}

.input {
  flex: 1;
  border: none;
  background: transparent;
  outline: none;
  font-size: 30px;
  caret-color: #0092FFFF;
  color: rgba(0, 0, 0, 0.85);
  // 解决快手小程序输入框撑开问题
  width: 0;
  min-width: 0;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  word-break: keep-all;
  word-wrap: normal;
}

.input::placeholder {
  color: #00000040;
}


.rightSlot {
  cursor: pointer;
  margin-left: 32px;
  line-height: 1;
}

.defaultSearch {
  font-size: 30px;
  color: rgba(0, 0, 0, 0.85);
}


.clearIcon {
  padding: 0px 24px;
  height: 72px;
  display: flex;
  align-items: center;
  justify-content: center;
}


.ttText {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  font-weight: normal;
  width: 100%;
  word-break: break-all;
}