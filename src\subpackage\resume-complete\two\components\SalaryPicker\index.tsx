import { useEffect, useRef, useState } from 'react'
import Popup from '@/components/Popup'
import style from './index.module.scss'
import { handleSalaryValue } from './utils'
import { getDayData, getMonthData, getSalaryText } from '@/core/utils/publish'
import PopHeader from '../PopHeader'
import SalaryItem from '../SalaryItem'

type ISalaryPickerProps = {
  /** 是否显示弹框 */
  visible?: boolean,
  /** 薪资组件类型, 默认月薪, month: 月薪, day: 日薪  */
  type?: string,
  /** 当前薪资范围 */
  value?: string,
  cancel?: () => void,
  submit?: (value: any) => void,
}
const SalaryPicker = (props: ISalaryPickerProps) => {
  const { visible = false, type = 'month', value = '', cancel, submit } = props

  const [data, setData] = useState({
    /** 当前选择的薪资范围 */
    selectValue: [] as any,
    /** 薪资总数据 */
    salary: [] as Array<any>,
    /** 上一次的type值 */
    lastType: '',
  })
  const isOk = useRef(true)

  useEffect(() => {
    visible && getSalaryData()
  }, [visible])

  useEffect(() => {
    upSalaryData()
  }, [type])

  /** 点击取消按钮 */
  const onCancel = () => {
    cancel && cancel()
  }

  const onStart = async () => {
    isOk.current = false
    await $.wait(2000)
    !isOk.current && onEnd()
  }

  const onEnd = () => {
    isOk.current = true
  }

  /** 点击确定按钮 */
  const onSubmit = () => {
    if (!isOk.current) {
      return
    }
    let subStr = '' // 用于提交的字符串
    let value = data.selectValue

    const stype = type === 'day' ? '1' : '2' // 日结1  月结2   计量3  面议4
    if (!$.isArrayVal(value)) { // 处理空值的默认数据
      value = ['面议', '面议']
    }
    if (value[0] === '面议') {
      subStr = '4'
    } else {
      subStr = `${stype},${value.join(',')},${stype}`
    }
    const detail = {
      type: 'submit',
      value,
      subStr,
      valFormat: getSalaryText(subStr),
    }
    submit && submit(detail)
  }

  /** 生成数据 */
  const getSalaryData = () => {
    const nValue = handleSalaryValue(value, type)
    setData((prev) => ({ ...prev, selectValue: nValue }))
    upSalaryData(nValue)
  }

  /** 更新薪资数据 */
  const upSalaryData = (selectValue) => {
    const { lastType, salary } = data
    if (lastType !== type || !salary.length) {
      setData((prev) => ({
        ...prev,
        salary: type === 'day' ? getDayData() : getMonthData(),
        lastType: type,
      }))
    }
    if ($.isArrayVal(selectValue)) {
      const defValue = ['面议', '面议']
      if (!salary.includes(selectValue[0])) {
        setData((prev) => ({ ...prev, selectValue: defValue }))
        return
      }
      if (selectValue[1] && !salary.includes(selectValue[1])) {
        setData((prev) => ({ ...prev, selectValue: defValue }))
      }
    }
  }

  const onChange = ({ detail }) => {
    setData((prev) => ({ ...prev, selectValue: detail.value }))
  }
  return (
    <Popup
      visible={visible}
      position="bottom"
      onClose={onCancel}
      zIndex={9990}
      className={style.pop}
      disableScroll={false}
      catchMove={false}
    >
      <V className={style.body}>
        <PopHeader
          title={type == 'day' ? '薪资要求(日薪，单位:元)' : '薪资要求(月薪，单位:元)'}
          cancel={onCancel}
          confirm={onSubmit}
        />
        <V className={style.picker}>
          <SalaryItem
            value={data.selectValue}
            salary={data.salary}
            change={onChange}
            start={onStart}
            end={onEnd}
          />
        </V>
      </V>
    </Popup>
  )
}

export default SalaryPicker
