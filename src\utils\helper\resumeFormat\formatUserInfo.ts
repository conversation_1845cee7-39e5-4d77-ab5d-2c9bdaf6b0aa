/*
 * @Author: ji<PERSON><PERSON>
 * @LastEditors: jianglong
 * @Description: 处理用户信息
 */

import { IRresumeInfoMe } from './index.d'
import { realNameStatusMap, avatarStatusMap, eduData } from './data'

export type IUserNameObj = {
  /** 这里主要用在编辑页的提交 */
  name: string,
  /** 格式化姓名：包括实名之后格式化的姓名 */
  nameFormat: string
}

/** 处理姓名的显示 */
function handlerUserName(userInfo: any): IUserNameObj {
  // 性别 1男，2女
  // 性别 1男，2女
  let name = userInfo.userName
  if (!name) {
    name = userInfo.sex == 2 ? '女士' : '先生'
  }

  // 这里的nameFormat先留着
  return name
}

/**
 * 获取教育经历name, 用于展示
 */
function getEduStr(eduVal) {
  const edu = eduData.find(item => item.value == eduVal)
  return edu ? edu.name || '' : ''
}

/** 处理用户信息 */
export default function formatUserInfo(resumeDetail: IRresumeInfoMe) {
  const { userInfoResp, basicResp } = resumeDetail
  if (!userInfoResp || !basicResp) {
    return {}
  }

  /** 头像审核状态 0-未申请 1-未审核 2-审核通过 3-审核失败 */
  const auditStatusObj = avatarStatusMap[(userInfoResp && userInfoResp.headPortraitAuditStatus) || 0]
  /** 实名状态 0-未认证 1-审核中 2-已通过 -1-不通过 */
  const realNameStatusObj = realNameStatusMap[(userInfoResp && userInfoResp.realNameStatus) || 0]

  // const hasVip = wx.$.u.getObjVal(userInfoResp, 'memberVipInfo.hasVip', false)
  return {
    ...userInfoResp,
    /** 简历id */
    resumeId: basicResp.resumeId,
    /** 简历uuid */
    resumeUuid: basicResp.resumeUuid,
    /** 头像 */
    avatar: userInfoResp.headPortrait,
    /** 数据值： '男 · 31岁 · 乌孜别克族' 的数据 */
    infoFormat: getInfoFormat(resumeDetail),
    /** 姓名 */
    nameFormat: handlerUserName(userInfoResp),
    /** 头像审核状态 0-未申请 1-未审核 2-审核通过 3-审核失败 */
    avatarAuditStatus: auditStatusObj.id,
    avatarAuditText: auditStatusObj.text || '',
    /** 实名状态 0-未认证 1-审核中 2-已通过 -1-不通过 */
    realNameStatus: realNameStatusObj.id,
    realNameText: realNameStatusObj.text || '',
  }
}

/** 处理数据： '男 · 31岁 · 乌孜别克族' 的数据 */
function getInfoFormat(resumeDetail: IRresumeInfoMe): string {
  const userInfoResp = resumeDetail.userInfoResp || {} as any
  const arr: string[] = []
  // 性别
  arr.push(userInfoResp?.sex == 2 ? '女' : '男')

  // 年龄
  if (userInfoResp.age) {
    arr.push(`${userInfoResp.age}岁`)
  }
  // 民族
  if (userInfoResp.nation && userInfoResp.nation.length) {
    arr.push(userInfoResp.nation)
  }
  // 学历
  if (userInfoResp.eduBackground) {
    arr.push(getEduStr(userInfoResp.eduBackground))
  }
  // 工作年限
  if (userInfoResp.workExpYear) {
    arr.push(`${userInfoResp.workExpYear}年经验`)
  }
  return arr.join(' · ')
}
