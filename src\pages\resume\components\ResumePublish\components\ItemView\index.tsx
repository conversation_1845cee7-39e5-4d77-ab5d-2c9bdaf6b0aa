import IconFont from '@/components/IconFont'
import style from './index.module.scss'

type IItemViewProps = {
  title?: string,
  desc?: string,
  placeholder?: string,
  value?: string,
  onClick?: () => void
}
export default function ItemView(props: IItemViewProps) {
  const { value = '', title = '', desc = '【可多选】', placeholder = '', onClick } = props
  return (
    <V className={style.item}>
      <V className={style.label}>
        <T >{title}</T>
        <T className={style.desc}>{desc}</T>
      </V>
      <V className={style.input} onClick={onClick}>
        <T className={`${value ? style.inputTxt : style.inputPlaceholder}`}>{value || placeholder}</T>
        <IconFont className={style.icon} type='yp-mianbaoxue' size={32} color='rgba(0, 0, 0, 0.45)' />
      </V>
    </V>
  )
}
