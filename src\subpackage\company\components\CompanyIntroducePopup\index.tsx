import { useEffect } from 'react'
import { ScrollView, Text as T, View as V } from '@tarojs/components'
import useReducer from '@/hooks/useReducer'
import styles from './index.module.scss'
import IconFont from '@/components/IconFont'
import Popup from '@/components/Popup'
import Tab from '../Tab'
import { IntroduceList } from '../../formatEnterpriseMainView'

type IProps = {
  data: IntroduceList
  index: number
  onClose?: () => void
}

/**
 * 公司介绍弹窗
*/
export default (props: IProps) => {
  const { data, index, onClose } = props

  const [{ showIndex, scrollIntoView }, dispatch] = useReducer({ showIndex: -1, scrollIntoView: 'descContent-1' })

  const items = data.map(it => it.title)

  const tabChange = (index: number) => {
    dispatch({ scrollIntoView: `descContent-${index}` })
  }

  useEffect(() => {
    dispatch({ showIndex: index })
    const timer = setTimeout(() => {
      tabChange(index)
    }, 100)
    return () => clearInterval(timer)
  }, [index])

  return (
    <Popup
      visible={showIndex !== -1}
      onClose={() => dispatch({ showIndex: -1 })}
      catchMove={false} disableScroll={false}
    >
      <V className={styles.header}>
        <T className={styles.title}>公司介绍</T>
        <IconFont type="yp-close-small" size={48} color="#000000A6" onClick={() => {
          dispatch({ showIndex: -1 })
          onClose?.()
        }}
        />
      </V>
      <Tab items={items} onChange={(index) => tabChange(index)} value={showIndex === -1 ? 0 : showIndex} />
      <ScrollView className={styles.scrollView} scrollY scrollIntoView={scrollIntoView}>
        {
          data.map((item, index) => (
            <V key={item.type} id={`descContent-${index}`}>
              <V className={styles.subTitle}>{item.title}</V>
              <V className={styles.desc}>{item.content}</V>
            </V>
          ))
        }
      </ScrollView>
    </Popup>
  )
}
