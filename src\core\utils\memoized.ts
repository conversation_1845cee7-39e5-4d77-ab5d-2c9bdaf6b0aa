/**
 * 缓存函数结果，避免多次计算，使用场景：
 * 1. 容易重复请求的配置接口
 * 2. 耗时较长的函数，如：递归获取数据，可以适当扩大limit的限制
 * @warning 此函数的 缓存函数不支持 `thisArg` ，被传入函数请勿持有上下文
 * @warning 如果第一个参数不是一个函数，会直接返回该参数，注意该参数是否可执行
 * @param {Function} fn 被缓存的函数
 * @param {any} catchValue 报错时返回的默认值，类似于 `tryPromise` 的默认值, 此参数被传入时跑错会返回此参数，否则默认抛出错误
 * ```
 *  .catch(error) {
 *          if (catchValue)
 *              return catchValue
 *          throw error
 *                 }
 * ```
 * @param limit 缓存数量，超过数量，会删除最旧的缓存 default: 5
 * @returns 返回时会在缓存函数上返回一个 detach 方法，用于卸载，可在函数页面或组件卸载时使用，防止内存溢出
 * @warning `detach`方法永远期望被主动调用，而不是等待GC
 */

export const memoized = function <T extends (...args: any) => any> (fn:T, catchValue: ReturnType<T>, limit = 5): T | MemoizedFn<T> {
  /** 传入参数是否可执行，不可执行参数，直接返回参数本身；走后续逻辑没有任何意义 */
  if (!fn || typeof fn.call !== 'function') return fn
  let map: Map<any, any>
  const memoizedFn = (function (...args) {
    try {
      /** 创建索引，索引值基于函数的入参，必然是一个 ArrayLike */
      const keyPath = JSON.stringify(args)
      /** 挂在map，此处惰性初始化Map，防止被detach之后依旧持有函数并调用导致缓存函数报错 */
      if (!map) map = new Map()
      if (map.has(keyPath)) return map.get(keyPath)
      /**
         * 如果是一个异步函数，如果是异步函数，则构造一个Promise对象来处理返回值的缓存
         * 注意，异步函数存在副作用，副作用不该被缓存，所以缓存值应该来自于`.then`;
         *      否则，异步函数的返回值会被缓存，导致后续的调用结果不正确。
         */
      if (fn.constructor.name === 'AsyncFunction') {
        return new Promise<Awaited<ReturnType<T>>>((resolve) => {
          fn(...args).then((res) => {
            /** 按照顺序删除map上的缓存 */
            if (map.size >= limit) map.delete(map.keys().next().value)
            map.set(keyPath, res)
            resolve(res)
          }).catch((error) => {
            console.error('[memoized fn error]:', error)
            if (catchValue) return catchValue
            throw error
          })
        })
      }
      /**
         * 同步函数,如果同步函数没有异常，都应该被缓存；
         * 否则，走入最顶层的catch
         *  */
      const result = fn(...args)
      /** 按照顺序删除map上的缓存 */
      if (map.size >= limit) map.delete(map.keys().next().value)
      map.set(keyPath, result)
      return result
    } catch (error) {
      if (catchValue) return catchValue
      throw error
    }
  }) as T & { detach: () => void }
  /** 实现缓存卸载的方法，`detach`方法永远期望被主动调用，而不是等待GC */
  memoizedFn.detach = function () {
    if (map) {
      map.clear()
      map = undefined as any
    }

    console.warn('memoized detached')
  }
  return memoizedFn
}

  type MemoizedFn<T extends (...args: any) => any> = T & { detach: () => void }
