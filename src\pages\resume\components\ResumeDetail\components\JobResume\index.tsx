import cn from 'classnames'
import CellCard from '../CellCard'
import s from './index.module.scss'
import IconFont from '@/components/IconFont'
import { getState } from '@/core/store'

/** 求职期望 */
export default function Index(props) {
  const { onClick } = props
  const {
    jobFull,
    jobPart,
  } = props.dataSource || { jobFull: [], jobPart: [] }

  const onAdd = () => {
    const { subCount,
      subPartCount,
      subFullCount } = getState().storage.myResumeDetails

    // 由于海投融合，resumeSubMaxNum 可能大于 maxFTPositionNum + maxPTPositionNum，但实际最大可添加数量应以 maxFTPositionNum + maxPTPositionNum 为准
    const { resumeSubMaxNum, maxFTPositionNum, maxPTPositionNum } = getState().resume.globalConfig.basicConfigResp as any
    const realMaxNum = Math.min(resumeSubMaxNum, maxFTPositionNum + maxPTPositionNum)
    if (subCount >= realMaxNum) {
      $.msg('已达上限')
      return
    }
    onClick && onClick()
    $.router.push(
      '/subpackage/resume-complete/two/index',
      {
        type: '1', // 1.添加 2.编辑
        origin: 'edit',
        /** 全职可选数量 */
        numFull: maxFTPositionNum - subFullCount,
        /** 兼职可选数量 */
        numPart: maxPTPositionNum - subPartCount,
      },
    )
  }

  const onEdit = (item: any) => {
    const { subCount,
      subPartCount,
      subFullCount } = getState().storage.myResumeDetails
    const {
      maxFTPositionNum,
      maxPTPositionNum } = getState().resume.globalConfig.basicConfigResp as any

    let industries = ''
    let mode = ''
    if (item.occupationInfo) {
      mode = item.occupationInfo.mode
      if ($.isArrayVal(item.occupationInfo.industries)) {
        industries = item.occupationInfo.industries.join(',')
      }
    }
    onClick && onClick()
    $.router.push(
      '/subpackage/resume-complete/two/index',
      {
        type: '2', // 1.添加 2.编辑
        origin: 'edit',
        mode,
        industries,
        resumeSubUuid: item.resumeSubUuid || '',
        occIds: item.occupationInfo.occId || '',
        /** 子名片总数 */
        numMax: subCount,
        /** 全职可选数量 */
        numFull: maxFTPositionNum - subFullCount,
        /** 兼职可选数量 */
        numPart: maxPTPositionNum - subPartCount,
        positionType: item.positionType,
      },
    )
  }
  return (
    <CellCard
      title="求职期望"
      titleIcon="yp-add"
      className={s['cell-card']}
      titleStyle="padding: 40rpx 0 0 0"
      onIcon={() => onAdd()}
    >
      {/* <!-- 全职期望 --> */}
      {jobFull.length > 0
        && <V className={s.list}>
          <V className={s.title}>全职期望</V>
          {jobFull.map((item, index) => (
            <V key={index} onClick={() => onEdit(item)} className={cn(s.item, index === 0 && s['pd-top-none'])}>
              <V className={s.head}>
                <V className={s.label}>{item.occName || ''}</V>
                <V className={s.value}>{item.salaryExpectation || ''}</V>
                <V className={s.icon}>
                  <IconFont type="yp-mbxl" size={32} color="rgba(0, 0, 0, 0.45)" />
                </V>
              </V>
              {!!item.occCtrlsStr && <V className={s['content-text']}>{item.occCtrlsStr}</V>}
            </V>
          ))}
        </V>
      }

      {/* 零工/兼职期望 */}
      {jobPart.length > 0
        && <V className={cn(s.list, s['top-line'])}>
          <V className={s.title}>零工/兼职期望</V>
          {jobPart.map((item, index) => (
            <V key={index} onClick={() => onEdit(item)} className={cn(s.item, index === 0 && s['pd-top-none'])}>
              <V className={s.head}>
                <V className={s.label}>{item.occName || ''}</V>
                <V className={s.value}>{item.salaryExpectation || ''}</V>
                <V className={s.icon}>
                  <IconFont type="yp-mbxl" size={32} color="rgba(0, 0, 0, 0.45)" />
                </V>
              </V>
              {!!item.occCtrlsStr && <V className={s['content-text']}>{item.occCtrlsStr}</V>}
            </V>
          ))}
        </V>
      }
    </CellCard>
  )
}
