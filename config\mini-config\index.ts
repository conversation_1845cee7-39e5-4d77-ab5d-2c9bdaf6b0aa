/*
 * @Date: 2025-05-12 09:42:53
 * @Description: 项目配置
 */

export default {
  tt50eb2656cfd1bd43: {
    name: '鱼泡网',
    miniToken: 'douyin',
    appid: 'tt50eb2656cfd1bd43',
    logo: 'https://cdn.yupaowang.com/yupao_common/d6fd953a.png',
    theme: '#0092ff',
    customNavBar: true, // 自定义导航栏
    runtime: 'BD_MINI',
  },
  tt530725bacc328bc501: {
    name: '宜宾人社',
    miniToken: 'dy_ybrs',
    appid: 'tt530725bacc328bc501',
    logo: 'https://cdn.yupaowang.com/yupao_common/b26db847.png',
    theme: '#cc1984',
    customNavBar: false, // 自定义导航栏
    runtime: 'BD_MINI',
  },
  tt959de3200aaf5cea01: {
    name: '优岗直聘',
    miniToken: 'ygzp',
    appid: 'tt959de3200aaf5cea01',
    logo: 'https://cdn.yupaowang.com/yupao_common/d6fd953a.png',
    customNavBar: true, // 自定义导航栏
    runtime: 'BD_MINI',
  },
  ttc242b4ff5d766f5b01: {
    name: '宜就业',
    miniToken: 'dy_yjy',
    appid: 'ttc242b4ff5d766f5b01',
    logo: 'https://cdn.yupaowang.com/yupao_common/d6fd953a.png',
    customNavBar: false, // 自定义导航栏
    runtime: 'BD_MINI',
  },
  // 支付宝小程序
  // eslint-disable-next-line quote-props
  '2021004190694160': {
    name: '鱼泡直聘',
    miniToken: 'alipay',
    appid: '2021004190694160',
    logo: 'https://cdn.yupaowang.com/yupao_mini/yp_mini_zpframeName.png',
    customNavBar: true, // 自定义导航栏
    runtime: 'ALIPAY_MINI',
  },
  // 小红书小程序
  '6723389da1d0f60001dd06d3': {
    name: '萌瓜招聘',
    miniToken: 'xhs',
    appid: '6723389da1d0f60001dd06d3',
    logo: 'https://cdn.yupaowang.com/yupao_mini/yp_mini_zpframeName.png',
    customNavBar: true, // 自定义导航栏
    runtime: 'XHS_MINI',
  },
  // 小红书小程序
  ks712413168329900478: {
    name: '鱼泡网',
    miniToken: 'ks',
    appid: 'ks712413168329900478',
    logo: 'https://cdn.yupaowang.com/yupao_mini/yp_mini_zpframeName.png',
    customNavBar: true, // 自定义导航栏
    runtime: 'KS_MINI',
  },
} as Record<string, MiniConfigType>
