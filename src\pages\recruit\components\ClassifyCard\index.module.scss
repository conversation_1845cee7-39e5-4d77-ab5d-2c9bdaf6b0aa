.container {
    width: 100%;
    padding-top: 16px;
}

.headerRow {
    width: 100%;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center; // Ensure proper alignment
}

.classifyName {
    display: block; // Use block or inline-block for ellipsis to work
    flex-shrink: 1; // Allow shrinking if necessary
    flex-grow: 1; // Allow growing to fill available space
    max-width: 100%; // Constrain the width to the parent container
    overflow: hidden; // Hide overflow content
    text-overflow: ellipsis; // Add ellipsis for overflow
    white-space: nowrap; // Prevent wrapping
    font-size: 30px;
    font-weight: 600;
    color: $text85;
    line-height: 42px;
}

.salary {
    display: block; // Ensure it behaves as a block element
    flex-shrink: 0; // Prevent shrinking
    flex: none; // Maintain its intrinsic size
    white-space: nowrap; // Prevent wrapping
    margin-left: 24px;
    font-size: 30px;
    font-weight: 600;
    color: $primary-color;
    line-height: 42px;
}
.contentContainer {
    padding-top: 24px;
    padding-bottom: 8px;             
}

.compact {
    margin-top: 16px !important;
}
