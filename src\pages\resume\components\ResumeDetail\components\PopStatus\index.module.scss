
.pop-cont {
  display: block;
  background-color: #fff;
  border-radius: 16px;
  padding: 32px 24px;
  @include safe-area(64px);
}

.pop-head {
  // padding-bottom: 32px;

  .pop-title {
    padding-bottom: 16px;
    color: rgba(0, 0, 0, 0.85);
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .pop-tit-text {
    font-size: 34px;
    font-weight: bold;
  }

  .pop-desc {
    color: rgba(0, 0, 0, 0.45);
    font-size: 26px;
    padding-bottom: 24px;
  }
}

.hidden {
  display: none !important;
}

.state-status {
  display: flex;
  align-items: center;
  padding: 12px 24px;
  border-radius: 8px;
  background: #ffefde;
  color: #ff8904;
  .status-text {
    padding-left: 16px;
    font-weight: bold;
    font-size: 26px;
    line-height: 36px;
  }
}

.list {
  display: block;

  .item {
    padding: 32px 0;
    display: flex;
    align-items: center;
    @include bottom-line-b(1,1px);
  }

  .item-text {
    color: rgba(0, 0, 0, 0.85);
    font-size: 34px;

    &.text-checked {
      color: #0092FF;
      font-weight: bold;
    }
  }

  .item-tag {
    height: 44px;
    border-radius: 8px;
    padding: 0px 12px;
    margin-left: 24px;
    font-size: 26px;
    color: #0092FF;
    background: #E0F3FF;
    display: inline-flex;
    align-items: center;
  }

  .item-icon {
    flex: 1;
    padding-left: 20px;
    display: inline-flex;
    justify-content: flex-end;
  }
}
