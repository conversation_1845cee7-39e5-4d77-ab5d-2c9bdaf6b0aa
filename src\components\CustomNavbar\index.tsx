import { View, Text } from '@tarojs/components'
import { useEffect, useState } from 'react'
import s from './index.module.scss'
import IconFont from '../IconFont'

type ICustomNavbarProps = {
  title?: string;
  onBack?: () => void;
  // 是否需要显示回到首页按钮
  isHome?: boolean;
  isBack?: boolean;
};
const CustomNavbar = (props: ICustomNavbarProps) => {
  const { title = '页面标题', onBack, isHome = false, isBack = true } = props
  const [isShowHome, setIsShowHome] = useState(true)
  const sysInfo = $.sysInfo()

  useEffect(() => {
    handleHomeStatus()
  }, [isHome])

  const handleHomeStatus = () => {
    if (isHome) {
      const pages = $.router.getCurrentPages()
      if ($.isArrayVal(pages) && pages.length > 1) {
        setIsShowHome(false)
      } else {
        setIsShowHome(true)
      }
    } else {
      setIsShowHome(false)
    }
  }

  const onBackClick = () => {
    if (onBack) {
      onBack()
    } else {
      $.router.back(1)
    }
  }
  const onHomeClick = () => {
    $.router.back(9999)
  }

  return (
    <View className={s.box}>
      <View className={s.customNavbar} style={{ paddingTop: sysInfo.statusBarHeight, height: sysInfo.headerHeight }}>
        {
          isBack ? <View className={s.navbarLeft} onClick={onBackClick}>
            <IconFont className={s.iconfont} size={48} type='yp-back' />
          </View> : null
        }
        {
          isShowHome ? (<View className={s.navbarHome} onClick={onHomeClick}>
            <IconFont className={s.iconfont} size={48} type='yp-shouye' />
          </View>) : null
        }
        <View className={s.navbarTitle}>
          <Text>{title}</Text>
        </View>
        {
          isBack ? <View className={s.navbarRight}></View> : null
        }

        {
          isShowHome ? (<View className={s.navbarHome}></View>) : null
        }
      </View>
      <View className={s.zw} style={{ height: sysInfo.statusBarHeight + sysInfo.headerHeight }}></View>
    </View>
  )
}

export default CustomNavbar
