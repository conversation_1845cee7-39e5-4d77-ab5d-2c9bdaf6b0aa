/** 处理薪资回显
 * @param type 薪资类型 'month'|'day'
 * 当value = 4 或者 type = 4 时，返回[0, 0] 代表面议
 */
export function handleSalaryValue(value: string, type = '') {
  /** month: 10000-11000，day默认展示200-300 */
  const selectValue = type == 'month' ? [10000, 11000] : [200, 300]
  const typeStr = $.typeOf(value)
  if (!value) {
    return selectValue
  }
  if (value == '4' || type == '4') {
    return [0, 0]
  }
  if ($.isArrayVal(value)) {
    return value
  }
  if (typeStr !== 'string') {
    return selectValue
  }
  const arr = `${value}`.split(',')
  if (arr.length < 1) {
    return selectValue
  }
  return [Number(arr[1]), Number(arr[2])]
}
