module.exports = {
  globals: {
    definePageConfig: true,
    React: true,
    my: true,
    $: true,
    V: true,
    T: true,
    Img: true,
    Nullable: true,
  },
  extends: [
    "taro/react",
    "eslint-config-airbnb-base",
    "plugin:sonarjs/recommended-legacy",
  ],
  plugins: ["sonarjs"],
  rules: {
    "react/jsx-uses-react": "off",
    "react/react-in-jsx-scope": "off",
    "import/first": "off",
    "@typescript-eslint/no-shadow": "off",
    // 禁用分号
    semi: ["error", "never"],
    // 强制使用单引号
    quotes: ["error", "single"],
    indent: ["error", 2],
    "import/no-unresolved": 0,
    "import/extensions": 0,
    "import/no-extraneous-dependencies": 0,
    "class-methods-use-this": 0,
    "sonarjs/sonar-no-unused-class-component-methods": 0,
    "no-undef": 0,
    "no-unused-vars": 0, // 未使用的变量，需配合下面的规则使用
    "@typescript-eslint/no-unused-vars": ["error"], // 未使用的变量
    "arrow-parens": 0,
    "no-use-before-define": 0,
    "no-shadow": 0, // 变量名字和外部名字重复
    "arrow-body-style": 0,
    "sonarjs/no-unused-expressions": 0, // 可以使用 fn && fn()
    "no-unused-expressions": 0, // 可以使用 fn && fn()
    "no-await-in-loop": 0, // 可以在循环中使用await
    "sonarjs/no-misused-promises": 0,
    "no-async-promise-executor": 0,
    "default-param-last": 0,
    "no-promise-executor-return": 0,
    "import/prefer-default-export": 0,
    "object-curly-newline": 0,
    "sonarjs/new-cap": 0,
    'max-len': 0,
    'eqeqeq': 0,
    "sonarjs/hook-use-state": 0,
    "react/jsx-no-undef": 0,
    "sonarjs/no-array-index-key": 0,
    "no-nested-ternary": 0,
    "no-param-reassign": 0,
    "react-hooks/exhaustive-deps": 0,
    "default-case": 0,
    "sonarjs/no-nested-conditional":0,
    "sonarjs/prefer-for-of":0,
    'prefer-promise-reject-errors': 0,
    'react/jsx-indent-props': 0,
    'linebreak-style': 0
  },
};
