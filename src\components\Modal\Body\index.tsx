/*
 * @Date: 2024-11-21 10:25:57
 * @Description: 弹窗动画淡出
 */

import cn from 'classnames'
import { memo, useEffect, useRef, useState } from 'react'
import s from './index.module.scss'

type Props = {
  visible: boolean
  children: React.ReactNode
  contentClassName?: string
  onMaskClick?: () => void
  position?: 'bottom'|'center'
  zIndex?: number
}

export default memo((props: Props) => {
  const { visible, children, onMaskClick, contentClassName, position = 'center', zIndex = 10001 } = props
  const [render, setRender] = useState(false)
  const [opacity, setOpacity] = useState(0)

  const timer = useRef<NodeJS.Timeout>()

  useEffect(() => {
    if (timer) clearTimeout(timer.current)

    if (visible) {
      setRender(true)
      timer.current = setTimeout(() => {
        setOpacity(1)
      }, 50)
    } else {
      setOpacity(0)
      timer.current = setTimeout(() => {
        setRender(false)
      }, 250)
    }
  }, [visible])

  if (!render) {
    return null
  }

  return (
    <V className={cn(s.body)} style={{ opacity, zIndex }} catchMove disableScroll>
      <V className={s.mask} onClick={onMaskClick} />
      <V className={cn(s.content, contentClassName, s[position])}>{children}</V>
    </V>
  )
})
