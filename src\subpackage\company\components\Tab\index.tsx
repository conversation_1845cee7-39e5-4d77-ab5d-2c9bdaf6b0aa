import { useEffect, useRef, useState } from 'react'
import { Text as T, View as V } from '@tarojs/components'
import classNames from 'classnames'
import styles from './index.module.scss'

type IProps = {
  /** tab 渲染项 */
  items: string[]
  /** change 事件 */
  onChange?: (i: number) => void
  /** 选中哪一项, 选中的下标 */
  value?: number
}

/**
 * 公司介绍，tab 切换
*/
export default (props: IProps) => {
  const { items, onChange, value = 0 } = props

  const [active, setActive] = useState(value)

  const domsRef = useRef([])

  const lefDis = useRef(0)
  const clickItem = (index: number) => {
    getDis(index)
    onChange?.(index)
    setActive(index)
  }

  const getDis = (index: number) => {
    try {
      const { left, width } = domsRef.current[index]
      const firstLeft = domsRef.current[0].left
      const firstWidth = domsRef.current[0].width
      lefDis.current = left + (width / 2) - (firstLeft + firstWidth / 2)
    } catch (error) {
      console.log(error)
    }
  }

  /**
   * 获取dom 信息，返回一个promise
  */
  const getDomeInfo = () => {
    return new Promise((resolve) => {
      if (domsRef.current.length) {
        resolve(domsRef.current)
      }
      const SelectorQuery = $.taro.createSelectorQuery()
      SelectorQuery.selectAll('.tabItemName')
        .boundingClientRect()
        .exec((res) => {
          domsRef.current = res[0]
          resolve(res)
        })
    })
  }

  useEffect(() => {
    if (typeof value !== 'undefined') {
      getDomeInfo().then(() => {
        setActive(value)
        getDis(value)
      })
    }
  }, [value])

  useEffect(() => {
    getDomeInfo()
  }, [])

  return (
    <V className={styles.wrap}>
      {
        items.map((item, index) => (
          <V className={classNames(styles.tab, 'tabItemName')} key={index} onClick={() => clickItem(index)}>
            <T className={styles.tabText}>{item}</T>
            {
              index === 0 && <V className={styles.line} style={{ transform: `translateX(${lefDis.current}px)` }} />
            }
          </V>
        ))
      }

    </V>
  )
}
